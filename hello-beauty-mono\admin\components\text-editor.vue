<template>
  <div class="mb-12 w-full">
    <QuillEditor
      v-model:content="content"
      content-type="html"
      theme="snow"

    />
  </div>
</template>

<script>
import "@vueup/vue-quill/dist/vue-quill.snow.css";
import { QuillEditor } from "@vueup/vue-quill";

export default {
  components: {
    QuillEditor,
  },
  props: ["data"],
  data() {
    return {
      content: "",
    };
  },
  watch: {
    content: {
      handler(r) {
        this.$emit("content", r);
      },
    },
    data: {
      handler(r) {
        this.content = r;
      },
    },
  },
};
</script>


<style>
.ql-container {
  min-height: 10rem;
  height: "200px";
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
