require('dotenv').config();
const jwt = require('jsonwebtoken');

const encode = async (data) => await jwt.sign(data, process.env.SECRET_JWT);

const decode = async (token) => await jwt.verify(token, process.env.SECRET_JWT);

const decodeJwtClient = async (token) => {
  const x = token.split(' ')[1];
  
  // Try to decode with user secret first
  try {
    const d = await jwt.verify(x, process.env.SECRET_JWT);
    return d;
  } catch (error) {
    // If user secret fails, try MUA secret
    try {
      const d = await jwt.verify(x, process.env.JWT_SECRET_MUA);
      return d;
    } catch (muaError) {
      throw new Error('Invalid token');
    }
  }
}

module.exports = {
  encode,
  decode,
  decodeJwtClient
};
