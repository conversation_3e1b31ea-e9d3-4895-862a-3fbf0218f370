<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="400px"
        @closed="$emit('closed')"
      >
        <div class="p-6">
          <div class="flex justify-between items-center">
            <h1 class="text-lg font-bold"><PERSON><PERSON> Lokasi</h1>
          </div>
          <div class="mt-4">
            <p class="text-sm mb-3">
              Apakah anda yakin ingin menghapus lokasi ini?
            </p>

            <div class="mb-3">
              <p class="text-xs"><PERSON><PERSON></p>
              <p class="text-sm font-semibold">{{ data.name }}</p>
            </div>

            <div class="flex justify-start mt-4 gap-2">
              <button class="btn-danger" @click="save">
                <icon name="icon-park-twotone:delete" class="text-xs" />
                Hapus
              </button>
              <button
                class="p-2 bg-gray-300 text-gray-700 rounded-lg px-4 text-xs"
                @click="showPanel = false"
              >
                Batal
              </button>
            </div>
          </div>
        </div>

        <loader-full v-if="loading" />
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(['closed', 'refresh']);

const props = defineProps({
  show: Boolean,
  data: String
});

const showPanel = ref(false);

const loading = ref(false);

watch(
  () => props.show,
  val => {
    showPanel.value = !!val;
  },
  { deep: true }
);

const save = async () => {
  try {
    loading.value = true;
    const { data } = await adminDelete(`/location?id=${props.data._id}`);
    loading.value = false;
    $toast.success(data.message);
    emit('refresh');
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
