"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { api } from "../_helper/api";
import { useParams } from "next/navigation";
import moment from "../_helper/moment";
import convertorp from "@/app/_helper/convertorp";

export default function Blog() {
  const [booking, setBooking] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const params = useParams();

  const [pay, setPay] = useState([]);
  const [loadingPay, setLoadingPay] = useState(true);
  const [errorPay, setErrorPay] = useState("");

  const getBooking = async () => {
    try {
      setLoading(true);
      const res = await api("GET", `/order/${params.id}`);
      setLoading(false);
      setBooking(res.data);
    } catch (error) {
      console.log(error);
      setLoading(false);
      setError(error);
    }
  };
  const getDetailPay = async () => {
    try {
      setLoadingPay(true);
      setErrorPay("");
      const res = await api("GET", `/pay/${params.id}`);
      setLoadingPay(false);
      setPay(res.data);
    } catch (error) {
      setErrorPay("Terjadi kesalahan");
      setLoadingPay(false);
      console.log(error);
    }
  };
  useEffect(() => {
    getBooking();
    getDetailPay();
  }, []);
  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4 justify-between  max-w-[480px]">
          <button
            onClick={() => {
              window.history.back();
            }}
          >
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </button>
        </div>
        <div>
          <h3 className="text-2xl font-semibold text-center mb-6">
            Booking
            <br /> Detail<span className="text-hb-pink">.</span>
          </h3>
          <div className="mt-4 p-3">
            {!loading && (
              <div className="px-3">
                <div className="mb-3">
                  <span className="text-xs">Booking ID</span>
                  <h4 className="text-xs font-semibold">#{booking.trxId}</h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Paket</span>
                  <h4 className="text-xs font-semibold">
                    {booking.packageItemName} {booking.packageName}
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Kontak</span>
                  <h4 className="text-xs font-semibold">{booking.name}</h4>
                  <h4 className="text-xs font-semibold">
                    {booking.phone} / {booking.email}
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Alamat</span>
                  <h4 className="text-xs font-semibold">
                    {booking.address}
                    <br />
                    {booking.locationName}
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Waktu Booking</span>
                  <h4 className="text-xs font-semibold">
                    {moment(booking.bookingDate).format("dddd, DD MMM YYYY")},
                    Jam {booking.bookingTime}
                  </h4>
                </div>
                {booking.note?.length > 0 && (
                  <div className="mb-3">
                    <span className="text-xs">Catatan</span>
                    <h4 className="text-xs font-semibold">{booking.note}</h4>
                  </div>
                )}
                <div className="pt-3 border-t border-dotted">
                  <h3 className="text-lg font-semibold">Payment</h3>
                  <div className="grid grid-cols-1 gap-2 mt-2">
                    {pay.map((item, index) => (
                      <Link
                        key={index}
                        href={`/payment/${item.trxId}`}
                        className="bg-white p-3 border rounded-xl flex justify-between items-center"
                      >
                        <div>
                          <p className="text-xs text-gray-600">#{item.payId}</p>
                          <span className="font-semibold">
                            {convertorp(item.totalBayar)}
                          </span>
                        </div>
                        <div>
                          {item.status === "PAID" ? (
                            <span className="text-xs text-green-500">
                              Lunas
                            </span>
                          ) : (
                            <span className="text-xs text-red-500">
                              Belum Lunas
                            </span>
                          )}
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>

                <div className="my-2">
                  {
                    //   check if array pay have status unpaid
                    pay.filter((item) => item.status === "UNPAID").length >
                      0 && (
                      <div className="p-3 bg-hb-pink-light-2 text-hb-pink rounded-xl">
                        <p className="text-xs">Sisa Pembayaran </p>
                        <div className="font-semibold">
                          {convertorp(
                            pay
                              .filter((item) => item.status === "UNPAID")
                              .reduce((acc, item) => acc + item.totalBayar, 0),
                          )}
                        </div>
                        <p className="text-xs">
                          Pelunasan dibayar sebelum H-1 dari Waktu Booking
                        </p>
                      </div>
                    )
                  }
                  <button className="btn-primary my-2">Bayar Sekarang</button>
                  <button className="btn-secondary">Download Invoice</button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
