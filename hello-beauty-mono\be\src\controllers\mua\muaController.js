const mua = require("../../models/mua");
const { checkMuaVerification } = require("../../middlewares/muaMiddleware");

const getMua = async (req, res) => {
  try {
    // MUA data is already attached to req by middleware
    const muaData = req.mua;
    if (!muaData) {
      return res.status(404).json({
        message: "MUA not found",
      });
    }
    delete muaData.password;
    return res.status(200).json({
      message: "Detail MUA",
      data: muaData,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
};

const updateMua = async (req, res) => {
  try {
    const body = req.body;
    const muaData = await mua.findByIdAndUpdate(
      {
        _id: req.mua._id,
      },
      body,
      { new: true }
    );
    return res.status(200).json({
      message: "M<PERSON><PERSON> updated",
      data: muaData,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
}

// const detailBid = async
module.exports = {
  getMua,
  updateMua,
  checkMuaVerification, // Export middleware for use in routes
};
