const mongoose = require('mongoose');

const adminSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true,
        unique: true
    },
    phone: {
        type: String,
        required: true,
        unique: true
    },
    password: {
        type: String,
        required: true
    },
    menu: {
        type: Array,
        required: true
    },
    level:{
        type: String
    }
}, { timestamps: true });

module.exports = mongoose.model('admin', adminSchema);
