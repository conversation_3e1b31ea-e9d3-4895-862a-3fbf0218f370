"use client";
import BottomMenu from "@/app/_component/mua/bottomMenu";
import { apiMua } from "@/app/_helper/api-mua";
import { useEffect, useState } from "react";
import LoadingFull from "@/app/_component/loadingFull";
import Link from "next/link";
import { Icon } from "@iconify/react";
import moment from "@/app/_helper/moment";
import convertRp from "@/app/_helper/convertorp";

export default function Bid() {
  const [listBid, setListBid] = useState([]);
  const [loading, setLoading] = useState(true);
  const [listLocation, setListLocation] = useState([]);
  const [location, setLocation] = useState("");
  const [me, setMe] = useState({});

  const getListBid = async (l) => {
    try {
      setLoading(true);
      const response = await apiMua("GET", `/bid?location=` + l);
      setLoading(false);
      setListBid(response.data);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  const getMe = async () => {
    try {
      setLoading(true);
      const response = await apiMua("GET", "/me");
      setLoading(false);
      setMe(response.data);
      setLocation(response.data.locationId);
      // if(!response.data.isApproved) {
      //   window.location = "/mua/verify"
      // }
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  }

  const getListLocation = async () => {
    try {
      setLoading(true);
      const response = await apiMua("GET", "/location");
      setLoading(false);
      setListLocation(response.data);
      
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  }
  const handleChangeLocation = (value) => {
    setLocation(value);
    setTimeout(() => {
      getListBid(value);
    }, 100);
  }
  useEffect(() => {
    Promise.all([getMe(),getListLocation(), getListBid()]);
  }, []);
  return (
    <div className="max-w-[480px] mx-auto ">
    
      {loading && (
        <div>
          <LoadingFull />
        </div>
      )}
      <div className="p-4">
        <h1 className=" font-semibold mb-3">
          Penawaran Tersedia
        </h1>
        <div className="mb-3">
          <select
            onChange={(e) => {handleChangeLocation(e.target.value)}}
            className="form-input rounded-xl">
            <option value="">Semua Lokasi</option>
            {listLocation.map((item, index) => (
              <option key={index} value={item._id}>
                {item.name}
              </option>
            ))}
            </select>
          </div>
        <ul>
          {listBid.map((item, index) => (
            <li key={index} className="bg-white mb-2 p-3 border rounded-xl">
              <Link href={`/bid/${item.trxId}`} className="w-full">
                <div className="flex gap-2">
                  <div className="flex gap-1 py-1 items-center px-2 text-xs rounded-lg bg-green-100 text-green-600">
                    Open
                  </div>
                  
                  <div className="bg-gray-100 flex items-center font-semibold text-gray-600 px-2 py-1 text-xs rounded-lg">
                    <Icon icon="uil:moneybag" className="h-3" />
                    {convertRp(item.muaShare||0)}
                  </div>
                </div>
                <div className="mt-2"><div className=" text-hb-pink text-sm pr-20">
                    {item.packageItemName} {item.packageName}
                  </div>
                </div>
                <div className="mt-2">
                  {item.address},{" "}
                  <span className="font-semibold">{item.locationName}</span>
                </div>
                <div className="text-xs flex gap-1 items-center text-gray-600">
                  <Icon icon="akar-icons:calendar" className="h-3 " />
                  {moment(item.bookingDate).format("DD MMM YYYY")}
                  {","}
                  {item.bookingTime}
                </div>
                <div className="text-right text-xs text-gray-500 w-full">
                  {moment(item.updatedAt).fromNow()}
                </div>
              </Link>
            </li>
          ))}

          {!loading && listBid.length === 0 && (
            <div className="text-center text-gray-500 py-96">
              No bid available
            </div>
          )}
        </ul>
      </div>
      <BottomMenu />
    </div>
  );
}
