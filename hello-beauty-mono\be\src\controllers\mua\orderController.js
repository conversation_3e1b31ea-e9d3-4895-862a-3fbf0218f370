const transaction = require("../../models/transaction");
const detailOrder = async (req, res) => {
  try {
    const id = req.params.id;

    const checkTrx = await transaction.findOne({
      trxId: id,
    });

    if (!checkTrx) {
      return res.status(400).json({
        message: "Transaksi tidak ditemukan",
      });
    }

    const remapOrder = {
      trxId: checkTrx.trxId,
      address: checkTrx.address,
      locationName: checkTrx.locationName,
      packageName: checkTrx.packageName,
      packageItemName: checkTrx.packageItemName,
      pax: checkTrx.pax,
      isOpen: checkTrx.isOpen,
      createdAt: checkTrx.createdAt,
      updatedAt: checkTrx.updatedAt,
      bookingDate: checkTrx.bookingDate,
      bookingTime: checkTrx.bookingTime,
      note: checkTrx.note,
      muaShare: checkTrx.muaShare,
      status: checkTrx.status,
      customerName: checkTrx.customerName,
      customerPhone: checkTrx.customerPhone,
      totalPrice: checkTrx.totalPrice,
      paymentMethod: checkTrx.paymentMethod,
      paymentStatus: checkTrx.paymentStatus
    };

    return res.status(200).json({
      message: "Detail transaksi",
      data: remapOrder,
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message,
    });
  }
};

module.exports = {
  detailOrder,
};
