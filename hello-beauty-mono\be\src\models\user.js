const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true,
        unique: true
    },
    phone: {
        type: String,
        required: false,
        unique: true,
        sparse: true
    },
    password: {
        type: String,
        required: true
    },
    googleId:{
        type: String
    },
    profilePicture:{
        type: String
    },
    referralCode:{
        type: String
    },
    resetPasswordToken:{
        type: String
    },
    point: {
        type: Number,
        default: 0
    },
    saldoReferral: {
        type:Number,
        default: 0
    },
    photo: {
        type: String
    },
}, { timestamps: true });

module.exports = mongoose.model('user', userSchema);
