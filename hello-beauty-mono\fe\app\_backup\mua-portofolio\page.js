import Link from 'next/link'

export default function Home() {
  return (
    <div className="max-w-[480px] mx-auto">
      <div className="bg-hb-pink h-[200px]"/>
      <div className="grid-bg min-h-[800px]"/>
      <div className="p-4 -mt-[992px]"  data-aos="fade-up" >
        <img src="/images/mua-1.jpeg" className="mx-auto rounded-xl h-[220px] shadow-2xl w-full object-cover" />
      </div>
      <div className="-mt-24"  data-aos="fade-up" >
        <img src="/images/mua-logo.png" className="mx-auto rounded-full h-[140px] " />
      </div>

      <div className="p-4">
      <div  data-aos="fade-up"  className="p-3 bg-white mb-4 rounded-xl border grid grid-cols-2">
        <Link href="/mua-layanan" className="flex items-center justify-center">
          Layanan
        </Link>
        <Link href="/mua-portofolio" className="text-center border-l pl-2">
          <div className="py-2 text-center bg-hb-pink text-white rounded-lg">
          Portofolio
          </div>
        </Link>

      </div>

      <div className="mt-6 columns-2 [&>img:not(:first-child)]:mt-3">
        <img src="/images/gallery-1.jpeg"  data-aos="fade-up"  className="rounded-2xl h-[200px] w-full object-cover"/>
        <img src="/images/gallery-2.jpeg"  data-aos="fade-up"  className="rounded-2xl h-[400px] w-full object-cover"/>
        <img src="/images/gallery-3.jpeg"  data-aos="fade-up"  className="rounded-2xl"/>
        <img src="/images/gallery-4.jpeg"  data-aos="fade-up"  className="rounded-2xl"/>
        <img src="/images/gallery-5.jpeg"  data-aos="fade-up"  className="rounded-2xl"/>
        <img src="/images/gallery-6.jpeg"  data-aos="fade-up"  className="rounded-2xl"/>
    </div>




        </div>


    <div className="flex fixed bottom-0 left-0 h-[80px] items-center p-3 w-full z-50">
  <Link href="/prices" className="btn-primary">BOOK NOW</Link>
</div>
<div className=" w-full fixed bottom-0 left-0 h-[80px] backdrop-blur opacity-90 z-10"/>
    </div>
  );
}
