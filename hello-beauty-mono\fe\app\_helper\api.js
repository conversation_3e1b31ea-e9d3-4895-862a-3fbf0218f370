const baseUrl = process.env.NEXT_PUBLIC_API_URL

const getAuth = () => {
  const name = 'hb_token';
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
}

const api = async (method, url, data) => {
  const token = await getAuth();
  
  const options = {
    method: method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };

  // Only add body for non-GET requests
  if (method !== 'GET' && data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(baseUrl + url, options);

  if (!response.ok) {
    // Check if response is JSON before trying to parse
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const error = await response.json();
      throw error;
    } else {
      // If not JSON, create a generic error
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  return response.json();
}

// API for public endpoints (no authentication required)
const apiPublic = async (method, url, data) => {
  const options = {
    method: method,
    headers: {
      'Content-Type': 'application/json'
    }
  };

  // Only add body for non-GET requests
  if (method !== 'GET' && data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(baseUrl + url, options);

  if (!response.ok) {
    // Check if response is JSON before trying to parse
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const error = await response.json();
      throw error;
    } else {
      // If not JSON, create a generic error
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  return response.json();
}

const apiUpload = async (url, data) => {
  const token = await getAuth();
  const response = await fetch(baseUrl + url, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: data,
  });

  // intercept response here
  if (response.status === 401) {
    // redirect to login
    window.location.href = "/login";
  }
  if (!response.ok) {
    // Check if response is JSON before trying to parse
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const error = await response.json();
      throw error;
    } else {
      // If not JSON, create a generic error
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  return response.json();
};

export { api, apiPublic, apiUpload };
