import { Icon } from "@iconify/react";

export default function StatusDisplay({ status }) {
  const getStatusClass = (status) => {
    switch (status.toUpperCase()) {
      case "PENDING":
        return "text-yellow-600";
      case "FIND_MUA":
        return "text-blue-600";
      case "SELECTED_MUA":
        return "text-green-600";
      case "ON_PROGRESS":
        return "text-purple-600";
      case "DONE":
        return "text-green-600";
      case "CANCELED":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusIcon = (status) => {
    switch (status.toUpperCase()) {
      case "PENDING":
        return "mdi:clock-time-four-outline";
      case "FIND_MUA":
        return "mdi:account-search";
      case "SELECTED_MUA":
        return "mdi:account-check";
      case "ON_PROGRESS":
        return "mdi:progress-clock";
      case "DONE":
        return "mdi:check-circle";
      case "CANCELED":
        return "mdi:close-circle";
      default:
        return "mdi:help-circle";
    }
  };

  const getStatusText = (status) => {
    switch (status.toUpperCase()) {
      case "PENDING":
        return "Pending";
      case "FIND_MUA":
        return "Menunggu MUA";
      case "SELECTED_MUA":
        return "MUA Dipilih";
      case "ON_PROGRESS":
        return "Sedang Berlangsung";
      case "DONE":
        return "Selesai";
      case "CANCELED":
        return "Dibatalkan";
      default:
        return status;
    }
  };

  return (
    <div className={`w-full flex items-center gap-1 text-xs font-medium ${getStatusClass(status)}`}>
      <Icon icon={getStatusIcon(status)} className="text-lg" />
      <span>{getStatusText(status)}</span>
    </div>
  );
}
