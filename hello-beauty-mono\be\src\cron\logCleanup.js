const fs = require('fs');
const path = require('path');
const { logFunction, logError, logSuccess } = require('../utils/logger');

const LOGS_DIR = path.join(__dirname, '../../logs');
const MAX_SIZE_MB = 50;
const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024; // Convert MB to bytes

const cleanupLogs = () => {
  const fnName = 'cleanupLogs';
  logFunction(fnName, { maxSizeMB: MAX_SIZE_MB });

  try {
    // Check if logs directory exists
    if (!fs.existsSync(LOGS_DIR)) {
      logFunction(fnName, { message: 'Logs directory does not exist' }, 'warn');
      return;
    }

    // Get all files in logs directory
    const files = fs.readdirSync(LOGS_DIR);
    logFunction(fnName, { fileCount: files.length });
    
    let deletedCount = 0;
    files.forEach(file => {
      const filePath = path.join(LOGS_DIR, file);
      const stats = fs.statSync(filePath);
      
      // Check if file size exceeds limit
      if (stats.size > MAX_SIZE_BYTES) {
        logFunction(fnName, { 
          file,
          size: `${(stats.size / (1024 * 1024)).toFixed(2)}MB`,
          action: 'deleting'
        }, 'warn');
        
        fs.unlinkSync(filePath);
        deletedCount++;
        
        logSuccess(fnName, { action: 'deleted', file });
      }
    });

    logSuccess(fnName, { 
      totalFiles: files.length,
      deletedCount,
      remainingCount: files.length - deletedCount
    });
  } catch (error) {
    logError(fnName, error);
  }
};

module.exports = cleanupLogs; 
