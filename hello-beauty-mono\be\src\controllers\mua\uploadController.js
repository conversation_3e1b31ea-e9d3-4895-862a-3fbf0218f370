const multer = require('multer');
const path = require('path');
const moment = require('moment');
const fs = require('fs');
const {decodeJwtClient} = require('../../helper/jwt')

const checkFileType = (file, cb) => {
  const filetypes = /jpeg|jpg|png|gif|pdf/;
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = filetypes.test(file.mimetype);
  

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb('Error: Images only!');
  }
}

const uploadFile = async (req,res) => {
  try{
    const auth =  req.headers.authorization
    const u = await decodeJwtClient(auth)
    const currentMonthYear = moment().format('MM-YYYY');
    const uploadDir = `./src/public/uploads/${currentMonthYear}/mua/${u.id}`;
    if (!fs.existsSync(uploadDir)){
      fs.mkdirSync(uploadDir, { recursive: true,mode: 0o777 });
    }

    // check filesize 
    
    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, uploadDir)
      },
      filename: function (req, file, cb) {
        cb(null, file.fieldname + '-' + Date.now() + path.extname(file.originalname))
      }
    })
  
  const upload = multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
      checkFileType(file, cb);
    }
  }).single('file');
  
  upload(req, res, (err) => {
    if (err) {
      res.status(500).json({
        message: 'Internal server error',
        error: err.message
      });
    } else {
      const { filename, path } = req.file;
      const fullUrl = `https://assets.hellobeauty.id/uploads/${currentMonthYear}/mua/${u.id}/${filename}`;
      const currentPath = path.replace('src/public', '');
      const d = {
        fileName: filename,
        filePath: currentPath,
        fullUrl
      }
      res.status(201).json({
          message: 'Uploaded successfully',
          data: d
        });
    }
  });
  } catch(err) {
    res.status(500).json({
      message: 'Internal server error',
      error: err.message
    });
  }
  
}

const hapusFile = async (req,res) => {
  try{
    const auth =  req.headers.authorization
    const u = await decodeJwtClient(auth)
    if(!u){
      return res.status(401).json({
        message: 'Unauthorized'
      });
    }
    const {url} = req.body;

    const filePath = url.replace(`https://assets.hellobeauty.id`, '');
    // check if file exists
    if (!filePath) {
      return res.status(400).json({
        message: 'File path is required'
      });
    }

    // pastikan fullPath mengandung id user
    if(!filePath.includes(u.id)){
      return res.status(401).json({
        message: 'Unauthorized'
      });
    }


    const fullPath = `./src/public${filePath}`;
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath
      );
      res.status(200).json({
        message: 'File deleted successfully'
      });
    } else {
      res.status(404).json({
        message: 'File not found'
      });
    }
  } catch(err) {
    res.status(500).json({
      message: 'Internal server error',
      error: err.message
    });
  }
}




module.exports = {
  uploadFile,
  hapusFile
};
