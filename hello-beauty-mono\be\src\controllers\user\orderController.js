const package = require("../../models/package");
const transaction = require("../../models/transaction");
const setting = require("../../models/setting");
const voucher = require("../../models/voucher");
const location = require("../../models/location");
const user = require("../../models/user");
const generateTrxId = require("../../helper/genId");
const { decodeJwtClient } = require("../../helper/jwt");
const payment = require("../../models/payment");
const generatePayId = require("../../helper/genPayId");
const { sendWhatsapp } = require("../../helper/kirimi");
const { toRp } = require("../../helper/rupiah");
const { encode } = require("../../helper/jwt")
const { validateEmail, validatePhone } = require("../../helper/validation");
const { logFunction, logError, logSuccess } = require("../../utils/logger");

const listOrder = async (req, res) => {
  const fnName = 'listOrder';
  logFunction(fnName, { 
    page: req.query.page,
    limit: req.query.limit,
    search: req.query.search
  });

  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search || "";

    // Build search query
    const searchQuery = {
      userId: u.id,
    };

    if (search) {
      searchQuery.$or = [
        { trxId: { $regex: search, $options: "i" } },
        { packageItemName: { $regex: search, $options: "i" } },
        { packageName: { $regex: search, $options: "i" } },
      ];
    }

    // Get total count for pagination
    const total = await transaction.countDocuments(searchQuery);

    // Get paginated results
    const transactions = await transaction
      .find(searchQuery)
      .sort({ _id: -1 })
      .skip(skip)
      .limit(limit);

    logSuccess(fnName, { 
      userId: u.id,
      totalOrders: total,
      returnedOrders: transactions.length
    });

    return res.status(200).json({
      message: "List order",
      data: transactions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasMore: skip + limit < total,
      },
    });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({ message: "Internal server error" });
  }
};

const createOrder = async (req, res) => {
  const fnName = 'createOrder';
  logFunction(fnName, { 
    packageId: req.body.packageId,
    itemId: req.body.itemId,
    locationId: req.body.locationId,
    hasVoucher: !!req.body.voucherCode
  });

  try {
    const body = req.body;
    const auth = req.headers.authorization;
    const check = auth.split(" ");
    let u;
    let voucherData = null;
    let discountAmount = 0;

    // Validate and process voucher if provided
    if (body.voucherCode) {
      voucherData = await voucher.findOne({ code: body.voucherCode });
      
      if (!voucherData) {
        logFunction(fnName, { error: 'Invalid voucher code' }, 'warn');
        return res.status(400).json({
          message: "Kode voucher tidak valid",
        });
      }

      // Check voucher validity
      if (!voucherData.isActive) {
        logFunction(fnName, { error: 'Inactive voucher' }, 'warn');
        return res.status(400).json({
          message: "Voucher tidak aktif",
        });
      }

      // Check voucher dates
      const now = new Date();
      if (now < new Date(voucherData.startDate) || now > new Date(voucherData.endDate)) {
        logFunction(fnName, { error: 'Voucher expired' }, 'warn');
        return res.status(400).json({
          message: "Voucher tidak berlaku pada periode ini",
        });
      }

      // Check usage limit
      if (voucherData.usageLimit <= 0) {
        logFunction(fnName, { error: 'Voucher usage limit reached' }, 'warn');
        return res.status(400).json({
          message: "Voucher telah mencapai batas penggunaan",
        });
      }
    }

    if(check[0] !== "Bearer" || check[1]==='undefined'){
      // validate phone
      const phoneValidation = validatePhone(body.phone);
      if (!phoneValidation.isValid) {
        logFunction(fnName, { error: 'Invalid phone', message: phoneValidation.message }, 'warn');
        return res.status(400).json({
          message: phoneValidation.message
        });
      }

      // validate email
      const emailValidation = validateEmail(body.email);
      if (!emailValidation.isValid) {
        logFunction(fnName, { error: 'Invalid email', message: emailValidation.message }, 'warn');
        return res.status(400).json({
          message: emailValidation.message
        });
      }

      const checkEmail = await user.findOne({
        email: body.email
      })
      if(checkEmail) {
        u = {
          id: checkEmail.id,
          ...checkEmail._doc}
        logFunction(fnName, { action: 'User found by email', userId: u.id });
      }

      // check user
      const checkUser = await user.findOne({
        phone: phoneValidation.formattedPhone,
      });

      if(checkUser){
        u = {
          id: checkUser.id,
          ...checkUser._doc
        }
        logFunction(fnName, { action: 'User found by phone', userId: u.id });
      }

      if(!checkUser && !checkEmail){
        const newUser = await new user({
          name:body.name,
          email:body.email,
          phone:phoneValidation.formattedPhone,
          password:"-"
        })
        await newUser.save()
        u = {
          id: newUser._id,
          ...newUser
        }
        logFunction(fnName, { action: 'New user created', userId: u.id });
      }
    } else {
      u = await decodeJwtClient(auth);
      logFunction(fnName, { action: 'User authenticated', userId: u.id });
    }

    const dataToken = {
      id: u.id,
      name: u.name,
      email: u.email,
      phone: u.phone,
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 365)
    }
    const token = await encode(dataToken)

    // check phone
    if (!body.phone) {
      logFunction(fnName, { error: 'Phone number required' }, 'warn');
      return res.status(400).json({
        message: "Nomor telepon harus diisi",
      });
    }

    // check packageId
    if (!body.packageId) {
      logFunction(fnName, { error: 'Package not selected' }, 'warn');
      return res.status(400).json({
        message: "Paket belum dipilih",
      });
    }

    // check lokasi
    if (!body.locationId) {
      logFunction(fnName, { error: 'Location not selected' }, 'warn');
      return res.status(400).json({
        message: "Lokasi belum dipilih",
      });
    }

    const checkLokasi = await location.findOne({
      _id: body.locationId,
    });

    if (!checkLokasi) {
      logFunction(fnName, { error: 'Location not found' }, 'warn');
      return res.status(400).json({
        message: "Lokasi tidak ditemukan",
      });
    }

    // check package
    const checkPackage = await package.findOne({
      _id: body.packageId,
    });

    if (!checkPackage) {
      logFunction(fnName, { error: 'Package not found' }, 'warn');
      return res.status(400).json({
        message: "Paket tidak ditemukan",
      });
    }

    let selectedItem = checkPackage.items.filter(
      (item) => item._id == body.itemId,
    );
    if (selectedItem.length == 0) {
      logFunction(fnName, { error: 'Package item not found' }, 'warn');
      return res.status(400).json({
        message: "Paket Item tidak ditemukan",
      });
    }
    selectedItem = selectedItem[0];

    // Calculate total price
    let totalPrice = selectedItem.price * body.pax;

    // Apply voucher discount if valid
    if (voucherData) {
      if (totalPrice < voucherData.minPurchase) {
        logFunction(fnName, { 
          error: 'Minimum purchase not met',
          totalPrice,
          minPurchase: voucherData.minPurchase
        }, 'warn');
        return res.status(400).json({
          message: `Minimal pembelian untuk menggunakan voucher ini adalah ${toRp(voucherData.minPurchase)}`,
        });
      }

      if (voucherData.discountType === 'percentage') {
        discountAmount = (totalPrice * voucherData.discountValue) / 100;
        if (discountAmount > voucherData.maxDiscount) {
          discountAmount = voucherData.maxDiscount;
        }
      } else {
        discountAmount = voucherData.discountValue;
        if (discountAmount > voucherData.maxDiscount) {
          discountAmount = voucherData.maxDiscount;
        }
      }
      totalPrice -= discountAmount;
      logFunction(fnName, { 
        action: 'Voucher applied',
        originalPrice: totalPrice + discountAmount,
        discountAmount,
        finalPrice: totalPrice
      });
    }

    let point = 1;
    let komisiReferral = 0;

    // check referralCode
    // referral_code
    if (body.referral_code!=='') {
      const checkReferral = await user.findOne({
        referralCode: body.referral_code,
      });
      if (!checkReferral) {
        return res.status(400).json({
          message: "Referral code tidak valid",
        });
      }

      // cek jika menggunakan kode referral sendiri
      if (checkReferral._id == u.id) {
        return res.status(400).json({
          message: "Tidak bisa menggunakan kode referral sendiri",
        });
      }

      // cek setting komisi referral
      // get first row in setting
      const checkSetting = await setting.findOne();
      console.log(checkSetting);
      console.log(checkSetting.komisiReferral);
      
      
      if (checkSetting.komisiReferral) {
        komisiReferral = checkSetting.komisiReferral/100*selectedItem.price*body.pax
        console.log("komisiku adalah", komisiReferral);
        
      } else {
        komisiReferral = 5/100*selectedItem.price*body.pax
      }
      point = 2;
    }

    // create transaction
    const trxId = await generateTrxId();
    const persenDp = parseInt(body.dp);
    const userId = u.id;
    const muaShare = selectedItem.priceShare ? selectedItem.priceShare : 0;

    const newTrx = new transaction({
      trxId: trxId,
      userId: userId || "-",
      locationId: checkLokasi._id,
      locationName: checkLokasi.name,
      packageId: body.packageId,
      packageName: checkPackage.name,
      packageItemName: selectedItem.name,
      packagePrice: selectedItem.price,
      name: body.name,
      phone: body.phone,
      email: body.email,
      address: body.address,
      pax: body.pax,
      totalHarga: totalPrice,
      dp: persenDp,
      bookingNumber: 0,
      status: "PENDING",
      statusPayment: "DP_UNPAID",
      note: body.note,
      ref: body.ref,
      bookingDate: body.date,
      bookingTime: body.time,
      muaShare: muaShare * body.pax,
      referralCode: body.referral_code,
      point: point,
      komisiReferral: komisiReferral,
      voucherCode: body.voucherCode,
      discountAmount: discountAmount
    });

    await newTrx.save();

    // Update voucher usage limit if voucher was used
    if (voucherData) {
      voucherData.usageLimit -= 1;
      await voucherData.save();
    }

    // create transaction pay
    if (persenDp !== 100) {
      // bayar dp
      const bayarDp = new payment({
        trxId: trxId,
        payId: generatePayId(),
        userId: userId || "-",
        paidAt: "",
        totalBayar: (totalPrice * persenDp) / 100,
        totalHarga: totalPrice,
        status: "UNPAID",
        percent: persenDp,
        isDp: true,
      });

      // pelunasan
      const pelunasan = new payment({
        trxId: trxId,
        payId: generatePayId(),
        userId: userId || "-",
        paidAt: "",
        totalBayar: (totalPrice * (100 - persenDp)) / 100,
        totalHarga: totalPrice,
        status: "UNPAID",
        percent: 100 - persenDp,
        isDp: false,
      });
      await bayarDp.save();
      await pelunasan.save();
    } else {
      // bayar full
      const bayarFull = new payment({
        trxId: trxId,
        payId: generatePayId(),
        userId: userId || "-",
        paidAt: "",
        totalBayar: totalPrice,
        totalHarga: totalPrice,
        status: "UNPAID",
        percent: 100,
        isDp: true,
      });
      await bayarFull.save();
    }

    // send whatsapp
    let msg = `Halo, ${body.name}!\n\nTerima kasih telah memesan paket ${checkPackage.name} ${selectedItem.name} di ${checkLokasi.name}.\n\nDetail transaksi:\nID\n*${trxId}*\n\nPaket\n*${checkPackage.name} - ${selectedItem.name}*\n\nHarga\n*${toRp(selectedItem.price)}*\n\nJumlah\n*${body.pax} pax*\n\n`;
    
    if (voucherData) {
      msg += `Diskon Voucher\n*${toRp(discountAmount)}*\n\n`;
    }
    
    msg += `Total\n*${toRp(totalPrice)}*\n\nDP (${persenDp}%) \n*${toRp(persenDp/100*totalPrice)}*.\n\nTerima kasih!\n\nDetail dan Pembayaran\n\n*https://hellobeauty.id/booking/${trxId}*`;
    
    await sendWhatsapp(body.phone, msg);
    
    logSuccess(fnName, { 
      userId: u.id,
      packageId: body.packageId,
      totalPrice,
      discountAmount
    });

    return res.status(200).json({
      message: "Order berhasil dibuat",
      data: newTrx,
      token
    });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: error.message,
    });
  }
};

const detailOrder = async (req, res) => {
  try {
    const id = req.params.id;

    const checkTrx = await transaction.findOne({
      trxId: id,
    });

    if (!checkTrx) {
      return res.status(400).json({
        message: "Transaksi tidak ditemukan",
      });
    }

    return res.status(200).json({
      message: "Detail transaksi",
      data: checkTrx,
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message,
    });
  }
};

const payOrder = async (req, res) => {
  try {
    const id = req.params.id;

    const checkTrx = await transaction.findOne({
      trxId: id,
    });

    if (!checkTrx) {
      return res.status(400).json({
        message: "Transaksi tidak ditemukan",
      });
    }

    if (checkTrx.status == "PAID") {
      return res.status(400).json({
        message: "Transaksi sudah dibayar",
      });
    }

    checkTrx.status = "PAID";
    checkTrx.bookingNumber = Math.floor(Math.random() * 1000000);
    await checkTrx.save();

    return res.status(200).json({
      message: "Transaksi berhasil dibayar",
      data: checkTrx,
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message,
    });
  }
};

module.exports = {
  createOrder,
  detailOrder,
  payOrder,
  listOrder,
};
