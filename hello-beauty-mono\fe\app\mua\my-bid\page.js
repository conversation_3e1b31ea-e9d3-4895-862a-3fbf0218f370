"use client"
import LoadingFull from "@/app/_component/loadingFull";
import BottomMenu from "@/app/_component/mua/bottomMenu";
import { apiMua } from "@/app/_helper/api-mua";
import { Icon } from "@iconify/react";
import moment from "moment";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function MyBid() {
  const [loading, setLoading] = useState(true);
  const [myBid, setMyBid] = useState([]);

  const getMyBid = async () => {
    try {
      const response = await apiMua("GET", "/bid/my");
      setLoading(false);
      setMyBid(response.data);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    getMyBid();
  }, []);
  return (
    <div className="max-w-[480px] mx-auto ">
       {loading && (
        <div>
          <LoadingFull />
        </div>
      )}

      <div className="p-4">
        <h1 className=" font-semibold mb-3">
          Penawaran Saya
        </h1>
      <ul>
          {myBid.map((item, index) => (
            <li key={index} className="bg-white mb-2 p-3 border rounded-xl">
              <Link href={`/bid/${item.trxId}`} className="w-full">
                <div className="mb-2">
                  <div className="flex gap-1 items-center text-xs  text-hb-pink">
                    <img src="/icons/coin.svg" className="h-3" />
                    {item.idTrx.packageItemName} {item.idTrx.packageName}
                  </div>
                </div>
                 <div>
                    {!item.idTrx.selectedMuaId&&(
                      <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-lg">
                        Proses Pemilihan
                      </span>
                    )}
                    {
                      item.idTrx.selectedMuaId&&(
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-lg">
                        Selesai
                      </span>
                      )
                    }
                  </div>
                <div className="mt-2">
                  {item.idTrx.address},{" "}
                  <span className="font-semibold">{item.idTrx.locationName}</span>
                </div>
                <div className="text-xs flex gap-1 items-center text-gray-600">
                  <Icon icon="akar-icons:calendar" className="h-3 " />
                  {moment(item.idTrx.bookingDate).format("DD MMM YYYY")}
                  {","}
                  {item.idTrx.bookingTime}
                </div>
                <div className="text-right text-xs text-gray-500 w-full">
                  {moment(item.idTrx.updatedAt).fromNow()}
                </div>
              </Link>
            </li>
          ))}

          {!loading && myBid.length === 0 && (
            <div className="text-center text-gray-500 py-96">
              Belum ada melakukan penawaran
            </div>
          )}
        </ul>
      </div>
      <BottomMenu />
    </div>
  );
}
