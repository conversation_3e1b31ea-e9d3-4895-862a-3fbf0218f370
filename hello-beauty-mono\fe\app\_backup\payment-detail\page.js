import Link from 'next/link'

export default function Home() {
  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div className="h-14 flex items-center px-4">
        {/* <Link href="/">
        <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link> */}
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="mt-12">
        <h3 className="text-lg font-semibold text-center mb-6">
          Selamat <br/>Pembayaran anda Berhasil</h3>
      </div>

      <div className="mt-12 w-full text-center">
        <img src="/icons/ic-calendar.svg" className="mx-auto mb-4 h-12"/>
        <div className="mb-4">
          Nomor booking anda
        </div>
        <div className="h-20 w-20 rounded-full text-3xl bg-hb-pink flex items-center justify-center text-white font-semibold mx-auto">
          493
        </div>
      </div>

      <div className="mt-20 px-4">
        <h3 className="font-semibold text-lg">
          Booking Detail
        </h3>
      <div className="mt-6 grid grid-cols-1 bg-gray-100 rounded-xl p-4 gap-4">
        <div className="flex justify-between items-center">
          <div className="text-gray-600">
            Nama
          </div>
          <div className="">
            Jhon Doe
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="text-gray-600">
            Transaction ID
          </div>
          <div className="">
            23457680
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="text-gray-600">
            Nominal
          </div>
          <div className="">
            1350K
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="text-gray-600">
            Metode Pembayaran
          </div>
          <div className="">
            BCA VA
          </div>
        </div>
      </div>
      <div className="mt-6">
        <Link href="/" className="btn-secondary flex justify-center">
          KEMBALI
        </Link>
        </div>
      </div>
      </div>
  );
}
