const User = require('../models/user');
const Mua = require('../models/mua');
const { validateEmail, validatePhone } = require('../helper/validation');

const cleanInvalidData = async () => {
  try {
    console.log('Starting data validation and cleanup...');

    // Validate and clean user data
    const users = await User.find({});
    for (const user of users) {
      const emailValidation = validateEmail(user.email);
      const phoneValidation = validatePhone(user.phone);
      
      if (!emailValidation.isValid || !phoneValidation.isValid) {
        console.log(`Deleting invalid user: ${user.email} (${user.phone})`);
        await User.findByIdAndDelete(user._id);
      }
    }

    // Validate and clean MUA data
    const muas = await Mua.find({});
    for (const mua of muas) {
      const emailValidation = validateEmail(mua.email);
      const phoneValidation = validatePhone(mua.phone);
      
      if (!emailValidation.isValid || !phoneValidation.isValid) {
        console.log(`Deleting invalid MUA: ${mua.email} (${mua.phone})`);
        await Mua.findByIdAndDelete(mua._id);
      }
    }

    console.log('Data validation and cleanup completed successfully');
  } catch (error) {
    console.error('Error in data validation and cleanup:', error);
  }
};

// Run the function immediately for testing
cleanInvalidData();

module.exports = cleanInvalidData; 
