const md5 = require("md5");
const dotenv = require("dotenv");
const { encode, decodeJwtClient } = require("../../helper/jwt");
const sendEmail = require("../../helper/send-email");
const mua = require("../../models/mua");
const bcrypt = require("bcrypt");
const { validateEmail, validatePhone } = require("../../helper/validation");

dotenv.config();

const registerMua = async (req, res) => {
  try {
    const body = req.body;

    // validate name
    if (!body.name || body.name.trim().length === 0) {
      return res.status(400).json({
        message: "Nama tidak boleh kosong"
      });
    }

    // validate email
    const emailValidation = validateEmail(body.email);
    if (!emailValidation.isValid) {
      return res.status(400).json({
        message: emailValidation.message
      });
    }

    // validate phone
    const phoneValidation = validatePhone(body.phone);
    if (!phoneValidation.isValid) {
      return res.status(400).json({
        message: phoneValidation.message
      });
    }

    // check Email
    const checkUser = await mua.findOne({
      email: body.email,
    });
    if (checkUser) {
      return res.status(400).json({
        message: "Email sudah terdaftar",
      });
    }

    // check phone
    const checkPhone = await mua.findOne({
      phone: phoneValidation.formattedPhone,
    });

    if (checkPhone) {
      return res.status(400).json({
        message: "Nomor telepon sudah terdaftar",
      });
    }

    // insert data
    const encPassword = await bcrypt.hashSync(body.password, 10);

    const newUser = new mua({
      name: body.name,
      email: body.email,
      phone: phoneValidation.formattedPhone,
      password: encPassword,
    });
    await newUser.save();

    const dataToken = {
      id: newUser._id,
      name: newUser.name,
      email: newUser.email,
      phone: newUser.phone,
      exp: Math.floor(Date.now() / 1000) + 60 * 60 * 365,
    };
    const token = await encode(dataToken);
    if (!newUser) {
      return res.status(400).json({
        message: "Gagal mendaftarkan MUA",
      });
    }
    return res.status(201).json({
      message: "Berhasil mendaftarkan MUA",
      data: {
        mua: {
          name: newUser.name,
          email: newUser.email,
          phone: newUser.phone,
        },
        token,
      },
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message || "Internal Server Error",
    });
  }
};

const loginMua = async (req, res) => {
  try {
    const body = req.body;
    const checkUser = await mua.findOne({
      email: body.email,
    });
    if (!checkUser) {
      return res.status(400).json({
        message: "Email atau Password salah",
      });
    }
    const checkPassword = await bcrypt.compare(
      body.password,
      checkUser.password,
    );
    if (!checkPassword) {
      return res.status(400).json({
        message: "Email atau Password salah",
      });
    }
    const dataToken = {
      id: checkUser._id,
      name: checkUser.name,
      email: checkUser.email,
      phone: checkUser.phone,
      exp: Math.floor(Date.now() / 1000) + 60 * 60 * 365,
    };
    const token = await encode(dataToken);
    return res.status(200).json({
      message: "Berhasil login",
      data: {
        mua: {
          name: checkUser.name,
          email: checkUser.email,
          phone: checkUser.phone,
        },
        token,
      },
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message || "Internal Server Error",
    });
  }
};

const changePassword = async (req, res) => {
  try {
     const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const body = req.body;
    const checkUser = await mua.findOne({
      _id: u.id
    });
    if (!checkUser) {
      return res.status(400).json({
        message: "User tidak ditemukan",
      });
    }
    const checkPassword = await bcrypt.compare(
      body.old_password,
      checkUser.password,
    );
    if (!checkPassword) {
      return res.status(400).json({
        message: "Password lama salah",
      });
    }
    const encPassword = await bcrypt.hashSync(body.new_password, 10);
    checkUser.password = encPassword;
    await checkUser.save();
    return res.status(200).json({
      message: "Berhasil mengubah password",
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message || "Internal Server Error",
    });
  }
}

const sendEmailForgotPassword = async (req, res) => {
  try {
    const body = req.body
    
    // validate email
    const emailValidation = validateEmail(body.email);
    if (!emailValidation.isValid) {
      return res.status(400).json({
        message: emailValidation.message
      });
    }
    
    const checkUser = await mua.findOne({
      email: body.email})
    if (!checkUser) {
      return res.status(400).json({
        message: "Email telah dikirim, Silahkan cek email anda"
      })
    }

    const randomToken = Math.random().toString(36).substring(7)
    const timestamp = new Date().getTime()
    const token = md5(randomToken + timestamp + checkUser.email)
    checkUser.resetPasswordToken = token
    await checkUser.save()

    await sendEmail(checkUser.email, "Reset Password MUA", `Klik link berikut untuk reset password anda: ${process.env.URL_FRONTEND}/mua/reset-password?token=${token}`)

    return res.status(200).json({
      message: "Email telah dikirim, Silahkan cek email anda"
    })
  } catch (error) {
    console.log(error);
    
    return res.status(500).json({
      message: "Internal Server Error"
    })
  }
}

const validateResetPassword = async (req, res) => {
  try {
    const body = req.body
    const checkUser = await mua.findOne({
      resetPasswordToken: body.token
    })
    if (!checkUser) {
      return res.status(400).json({
        message: "Token tidak valid"
      })
    }
    const encPassword = await bcrypt.hashSync(body.password, 10)
    checkUser.password = encPassword
    checkUser.resetPasswordToken = null
    await checkUser.save()

    return res.status(200).json({
      message: "Berhasil mereset password"
    })
  } catch (error) {
    return res.status(500).json({
      message: "Internal Server Error"
    })
  }
}

module.exports = {
  registerMua,
  loginMua,
  changePassword,
  sendEmailForgotPassword,
  validateResetPassword
};
