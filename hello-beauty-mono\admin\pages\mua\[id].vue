<template>
  <div>
    <div v-if="!loading" class="p-3 grid md:grid-cols-1 grid-cols-1 gap-3">
      <div class="bg-white rounded-lg border relative w-full">
        <div class="p-4 flex items-center justify-between">
          <div class="flex items-center" @click.prevent="router.push('/mua')">
            <icon
              class="cursor-pointer text-xl"
              name="bitcoin-icons:arrow-left-filled"
              @click="router.go(-1)"
            />
            <h1 class="font-bold ml-2">Detail MUA</h1>
          </div>

          <div class="flex gap-2 items-center">
            <div v-if="detail.rating >= 0" class="flex gap-1 items-center">
              <button
                class="btn-secondary flex items-center gap-1"
                @click="showEditRating = true"
              >
                <icon name="iconoir:star" />
                <span class="font-semibold">
                  {{ detail.rating }}
                </span>
                <icon class="text-xs" name="iconoir:edit" /> Ubah
              </button>
            </div>
            <button class="btn-secondary" @click="showChangePassword = true">
              <icon name="iconoir:lock" /> Change Password
            </button>
            <button class="btn-secondary" @click="showEditProfile = true">
              <icon name="iconoir:profile-circled" /> Edit Profile
            </button>
          </div>
        </div>

        <div v-if="detail.approval" class="grid grid-cols-2 p-4 gap-3">
          <div class="mb-2">
            <label class="block text-xs font-semibold">Nama Lengkap</label>
            <span>{{ detail.approval.name }}</span>
          </div>

          <div class="mb-2">
            <label class="block text-xs font-semibold">MUA</label>
            <span>{{ detail.approval.profileName }}</span>
          </div>

          <div class="mb-2">
            <label class="block text-xs font-semibold">Email / Whatsapp</label>
            <span>
              {{ detail.approval.email }} / {{ detail.approval.phone }}
            </span>
          </div>

          <div class="mb-2">
            <label class="block text-xs font-semibold">Instagram</label>
            <a
              :href="`https://instgram.com/${detail.approval.instagram}`"
              target="_blank"
              class="text-primary"
            >
              {{ detail.approval.instagram }}
              <icon name="iconamoon:arrow-top-right-1-bold" class="text-sm" />
            </a>
          </div>

          <div class="mb-2 col-span-2">
            <label class="block text-sm font-semibold">Layanan</label>
            <div class="mt-2">
              <div class="flex flex-wrap gap-2">
                <div
                  v-for="(d, i) in detail.approval.serviceType"
                  :key="i"
                  class="bg-primary-light rounded-full px-2 text-primary"
                >
                  {{ d }}
                </div>
              </div>
            </div>
          </div>

          <div class="mb-2">
            <label class="block text-sm font-semibold">Alamat</label>
            <span>
              {{ detail.approval.address }}
            </span>
            <span class="block">
              {{ detail.approval.locationName }}
            </span>
          </div>

          <div class="mb-2">
            <label class="block text-sm font-semibold">Akun Sebelumnya</label>
            <a
              :href="detail.approval.linkHb"
              target="_blank"
              class="text-primary"
            >
              {{ detail.approval.linkHb }}
              <icon name="iconamoon:arrow-top-right-1-bold" class="text-sm" />
            </a>
          </div>

          <div class="mb-2">
            <label class="block text-sm font-semibold">Lama Menjadi MUA</label>
            <span>
              {{ detail.approval.lamaMua || "-" }}
            </span>
          </div>

          <div class="mb-2">
            <label class="block text-sm font-semibold">Ikut Acara HB</label>
            <span>
              {{ detail.approval.hasParticipated ? "Pernah" : "Tidak Pernah" }}
            </span>
          </div>
          <div class="mb-2">
            <label class="block text-sm font-semibold">Job HB </label>
            <span>
              {{ detail.approval.hasJob ? "Pernah" : "Tidak Pernah" }}
              {{
                detail.approval.hasJob
                  ? `( ${detail.approval.jumlah_job} x )`
                  : ""
              }}
            </span>
          </div>

          <div class="mb-2">
            <label class="block text-sm font-semibold"
              >Menjadi Partner / Kolaborasi</label
            >
            <span>
              {{ detail.approval.hasCollaboration ? "" : "Tidak Pernah" }}
            </span>
            <span
              v-if="detail.approval.hasCollaboration"
              class="block text-primary"
            >
              {{ detail.approval.collaboration }}
            </span>
          </div>
          <div class="mb-2 col-span-2">
            <label class="block text-sm font-semibold"
              >Sertifikat Training</label
            >
            <div class="w-full">
              <div
                v-for="(d, i) in detail.approval.trainings"
                :key="i"
                class="mb-2"
              >
                <a
                  :href="d"
                  target="_blank"
                  class="text-primary flex items-center border-b py-1 gap-2 justify-between"
                >
                  <div>Training {{ i + 1 }}</div>
                  <icon
                    name="iconamoon:arrow-top-right-1-bold"
                    class="text-sm"
                  />
                </a>
              </div>
              <div v-if="!detail.approval.trainings">
                <span class="text-gray-400">Belum ada training</span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-span-2">
            <label class="block text-sm font-semibold"
              >Sertifikat Nasional</label
            >
            <div class="w-full">
              <div
                v-for="(d, i) in detail.approval.certificates"
                :key="i"
                class="mb-2"
              >
                <a
                  :href="d"
                  target="_blank"
                  class="text-primary flex items-center border-b py-1 gap-2 justify-between"
                >
                  <div>Sertifikat {{ i + 1 }}</div>
                  <icon
                    name="iconamoon:arrow-top-right-1-bold"
                    class="text-sm"
                  />
                </a>
              </div>
              <div v-if="!detail.approval.certificates">
                <span class="text-gray-400">Belum ada sertifikat</span>
              </div>
            </div>
          </div>

          <div class="col-span-2">
            <div class="font-semibold">Portofolio</div>
            <div v-if="muaPortofolio.length === 0" class="text-gray-400">
              Belum ada portofolio
            </div>
            <div class="grid grid-cols-4 gap-2">
              <div v-for="(p, i) in muaPortofolio" :key="i" class="relative">
                <img :src="p.fullUrl" class="w-full h-96 object-cover" />
                <button
                  class="absolute top-2 text-xs right-2 bg-red-500 text-white rounded-full p-1"
                  @click="deletePortofolio(p._id)"
                >
                  <icon name="iconoir:trash" /> Hapus
                </button>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="grid grid-cols-2 p-4 gap-3">
          <div class="mb-2">
            <label class="block text-xs font-semibold">Nama Lengkap</label>
            <span>{{ detail.name }}</span>
          </div>
          <div class="mb-2">
            <label class="block text-xs font-semibold">Email / Phone</label>
            <span>{{ detail.email }} / {{ detail.phone }}</span>
          </div>
          <div class="mb-2">
            <label class="block text-xs font-semibold">Waktu Daftar</label>
            <span>{{
              useMoment(detail.createdAt).format("DD MMMM YYYY, HH:mm")
            }}</span>
          </div>
          <div class="mb-2">
            <label class="block text-xs font-semibold">Status</label>
            <status-akun :status="detail.isApproved" />
          </div>
        </div>
      </div>
    </div>

    <edit-rating
      :data="detail"
      :show="showEditRating"
      @closed="showEditRating = false"
      @refresh="getData(), (showEditRating = false)"
    />
    <change-password
      :show="showChangePassword"
      :mua-id="route.params.id"
      @closed="showChangePassword = false"
    />

    <edit-profile
      :data="{ ...detail.approval, ...detail }"
      :show="showEditProfile"
      @closed="showEditProfile = false"
      @refresh="getData(), (showEditProfile = false)"
    />
  </div>
</template>

<script setup>
const route = useRoute();
const router = useRouter();
const detail = ref({});
const loading = ref(false);
const errorMsg = ref("");
const showEditRating = ref(false);
const showChangePassword = ref(false);
const showEditProfile = ref(false);
definePageMeta({
  middleware: "auth-admin",
});
const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    detail.value = [];
    const { data } = await adminGet(`/mua/${route.params.id}`);
    loading.value = false;
    detail.value = data.data;
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

const muaPortofolio = ref([]);
const loadingPortofolio = ref(false);

const getPortofolio = async () => {
  try {
    loadingPortofolio.value = true;
    muaPortofolio.value = [];
    const { data } = await adminGet(`/mua/portofolio/${route.params.id}`);
    loadingPortofolio.value = false;
    muaPortofolio.value = data.data;
  } catch (error) {
    loadingPortofolio.value = false;
    errorMsg.value = error.response.data.message;
  }
};

const deletePortofolio = async (id) => {
  try {
    await adminDelete(`/mua/portofolio/${id}`);
    getPortofolio();
  } catch (error) {
    errorMsg.value = error.response.data.message;
  }
};

onMounted(() => {
  getData();
  getPortofolio();
  // set title document
  document.title = "Detail MUA";
});
</script>
