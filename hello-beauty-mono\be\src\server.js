require('dotenv').config();
const express = require('express');
const http = require('http');
const bodyParser = require('body-parser');
const cors = require('cors');
const compression = require('compression');
const helmet = require('helmet');
const session = require('express-session');
const passport = require('./config/passport');
// const rateLimit = require('express-rate-limit');
// const morgan = require('morgan');
const logger = require('./config/logger');
const connectDB = require('./config/mongoose');
const muaRoutes = require('./routes/mua.route');
const userRoutes = require('./routes/user.route');
const adminRoutes = require('./routes/admin.route');
const callbackRoutes = require('./routes/callback.route');
const googleAuthRoutes = require('./routes/googleAuth');
const googleAuthMuaRoutes = require('./routes/googleAuthMua');
const { initializeCronJobs } = require('./cron');

const app = express();
const port = process.env.PORT || 8900;
const httpServer = http.createServer(app);
const env = process.env.NODE_ENV || 'dev';

// Trust proxy for production (important for sessions behind load balancers)
if (env === 'production') {
  app.set('trust proxy', 1);
}

// Security middleware
app.use(helmet());

// Rate limiting
// const limiter = rateLimit({
//   windowMs: 15 * 60 * 1000, // 15 minutes
//   max: 100 // limit each IP to 100 requests per windowMs
// });
// app.use(limiter);

// Performance middleware
app.use(compression());

// Development middleware
app.use(cors());
// app.use(morgan('combined', { stream: logger.stream }));


// Body parser middleware with optimized settings
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ 
  extended: true,
  limit: '10mb',
  parameterLimit: 10000
}));

// Session configuration for Passport
app.use(session({
  secret: process.env.SESSION_SECRET || 'hello-beauty-session-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production' && process.env.COOKIE_SECURE !== 'false',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
  },
  name: 'hello-beauty-session'
}));

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Database connection
connectDB();

// Initialize cron jobs
initializeCronJobs();

// Routes
app.get('/', (req, res) => res.json({ message: 'Hello Beauty!' }));
app.use('/mua', muaRoutes);
app.use('/v1', userRoutes);
app.use('/chio', adminRoutes);
app.use('/uploads', express.static('./src/public/uploads'));
app.use('/cb', callbackRoutes);
app.use('/auth', googleAuthRoutes);
app.use('/mua/auth', googleAuthMuaRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Error:', {
    message: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method
  });
  
  res.status(500).json({ 
    message: 'Something went wrong!',
    error: env === 'dev' ? err.message : undefined
  });
});

// 404 handler
app.use((req, res) => {
  logger.warn('404 Not Found:', {
    path: req.path,
    method: req.method
  });
  res.status(404).json({ message: 'Not found' });
});

// Start server
const server = httpServer.listen(port, '0.0.0.0', () => {
  logger.info(`Server running in ${env} mode on port ${port}`);
});

// Graceful shutdown
const shutdown = () => {
  logger.info('Shutting down server...');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });

  // Force shutdown after 10 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
