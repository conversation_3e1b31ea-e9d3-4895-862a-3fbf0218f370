<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">Batalkan Pemilihan MUA</h2>
          </div>

          <div>
            <div v-if="data">
              <span class="text-label"> Nama MUA </span>
              <span class="block">
                {{ data.muaName }}
              </span>
            </div>
          </div>
          <div class="mt-4">
            <label>Alasan</label>
            <textarea
              required
              placeholder="Masukkan Alasan Pembatalan"
              v-model="payload.alasan"
              class="form-input"
            />
          </div>

          <div class="h-4" />
          <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);
const route = useRoute();
const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);

const payload = ref({
  trxId: route.params.id,
  isOpen: false,
});
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;

    if (props.data.muaId) {
      payload.value = {
        trxId: props.data.trxId,
        muaId: props.data.muaId,
        isOpen: props.data.isOpen,
      };
    }
  },
  { deep: true }
);

const save = async () => {
  try {
    loading.value = true;
    const p = {
      ...payload.value,
    };

    const res = await adminPost(`/bid/cancel`, p);
    loading.value = false;

    $toast.success(res.data.message);
    emit("refresh");
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
