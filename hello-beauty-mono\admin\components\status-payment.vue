<template>
  <div>
    <span :class="colorStatus" class="text-xs font-semibold">
      {{ changeText(props.status) }}
    </span>
  </div>
</template>

<script setup>
const props = defineProps({
  status: {
    type: String,
    required: true,
  },
});

const changeText = (status) => {
  if (status === "DP_UNPAID") {
    return "DP Belum Lunas";
  }
  if (status === "DP_PAID") {
    return "DP Lunas";
  }

  if (status === "PAID") {
    return "Lunas";
  }

  return status;
};

// - DP Belum Lunas = DP_UNPAID
// - DP Lunas = DP_PAID
// - Lunas = PAID

const colorStatus = computed(() => {
  if (props.status === "DP_UNPAID") {
    return "text-red-600";
  }
  if (props.status === "DP_PAID") {
    return "text-blue-600";
  }

  if (props.status === "PAID") {
    return "text-green-600";
  }

  return "text-gray-600";
});
</script>
