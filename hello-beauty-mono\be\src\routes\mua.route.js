const express = require("express");
const { loginMua, registerMua, changePassword, sendEmailForgotPassword, validateResetPassword } = require("../controllers/mua/authController");
const {
  listBid,
  ajukanBid,
  checkBid,
  myBid,
  myBidSelected,
} = require("../controllers/mua/bidController");
const { getMua } = require("../controllers/mua/muaController");
const checkSessionMua = require("../middlewares/check-auth-mua");
const { checkMuaVerification } = require("../middlewares/muaMiddleware");
const { detailOrder } = require("../controllers/mua/orderController");
const { listLocation } = require("../controllers/mua/locationController");
const { getProfile, editProfile, updatePhoto } = require("../controllers/mua/profilController");
const { uploadFile, hapusFile } = require("../controllers/mua/uploadController");
const { addApproval, checkApproval } = require("../controllers/mua/approvalController");
const { uploadPortofolio,listPortofolio, deletePortofolio } = require("../controllers/mua/portofolioMuaController");
const { uploadCheckIn, checkCheckin } = require("../controllers/mua/checkinController");
const { uploadCheckout, checkCheckout } = require("../controllers/mua/checkoutController");
const router = express.Router();

// auth
const authRouter = express.Router();
authRouter.post("/login", loginMua);
authRouter.post("/register", registerMua);
authRouter.post("/forgot-password", sendEmailForgotPassword)
authRouter.post("/validate-reset-password", validateResetPassword)

// bid
const bidRouter = express.Router();
bidRouter.get("/", checkMuaVerification, listBid);
bidRouter.get("/my", checkMuaVerification, myBid);
bidRouter.get("/selected", checkMuaVerification, myBidSelected);
bidRouter.put("/ajukan", checkMuaVerification, ajukanBid);
bidRouter.get("/check/:id", checkMuaVerification, checkBid);

// mua
const muaRouter = express.Router();
muaRouter.get("/", getMua);
muaRouter.post("/change-password", changePassword);

// order
const orderRouter = express.Router();
orderRouter.get("/:id", detailOrder);

// lokasi
const locationRouter = express.Router();
locationRouter.get('/', listLocation)

// me
const meRouter = express.Router();
meRouter.get("/", getProfile);
meRouter.put("/", editProfile);
meRouter.post("/update-photo", updatePhoto);

const uploadRouter = express.Router();
uploadRouter.post("/",  uploadFile);
uploadRouter.post("/delete",  hapusFile);

const approvalRouter = express.Router();
approvalRouter.post("/", addApproval);
approvalRouter.get("/check", checkApproval);

const portofolioRouter = express.Router();
portofolioRouter.get("/", checkMuaVerification, listPortofolio);
portofolioRouter.post("/", checkMuaVerification, uploadPortofolio);
portofolioRouter.delete("/:id", checkMuaVerification, deletePortofolio);

const checkinRouter = express.Router();
checkinRouter.post("/:trxId", checkMuaVerification, uploadCheckIn)
checkinRouter.get("/", checkMuaVerification, checkCheckin)

const checkoutRouter = express.Router();
checkoutRouter.post("/:trxId", checkMuaVerification, uploadCheckout)
checkoutRouter.get("/", checkMuaVerification, checkCheckout)

// assing router
router.use("/auth", authRouter);
router.use("/bid", checkSessionMua, bidRouter);
router.use("/mua", checkSessionMua, muaRouter);
router.use("/order", orderRouter);
router.use("/location", locationRouter)
router.use("/me", checkSessionMua, meRouter);
router.use("/upload", checkSessionMua, uploadRouter);
router.use("/approval", checkSessionMua, approvalRouter);
router.use("/portofolio", checkSessionMua, portofolioRouter);
router.use("/checkin", checkSessionMua, checkinRouter)
router.use("/checkout", checkSessionMua, checkoutRouter)

module.exports = router;
