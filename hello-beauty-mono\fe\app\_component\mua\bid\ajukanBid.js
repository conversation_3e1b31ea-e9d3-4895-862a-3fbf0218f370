"use client";
import { useState } from "react";
import "react-responsive-modal/styles.css";
import { Modal } from "react-responsive-modal";
import ErrorSpan from "@/app/_component/errorSpan";
import { apiMua, getAuth } from "@/app/_helper/api-mua";
import MuaLogin from "../login/muaLogin";

const AjukanBid = ({ data }) => {
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [tncError, setTncError] = useState(false);

  const [open, setOpen] = useState(false);
  const [openLogin, setOpenLogin] = useState(false);
  const [offeringLetter, setOfferingLetter] = useState("");
  const [tncChecked, setTncChecked] = useState(false);

  
  const checkAuth = () => {
    
    const auth = getAuth();
    if(auth) {
      setOpen(true);
    } else {
      setOpenLogin(true);
    }
  }

  const handleBid = async () => {
    if (!tncChecked) {
      setTncError(true);
      return;
    }
    setTncError(false);
    try {
      setLoading(true);
      setErrorMsg("");
      const res = await apiMua("PUT", `/bid/ajukan`, {
        trxId: data.trxId,
        note: offeringLetter,
      });
      setOpen(false);
      // reload
      window.location.reload();
      // if (res.status === 200) {
      // } else {
      //   setErrorMsg(res.data.message);
      // }
    } catch (e) {
      console.log(e.message);
      setErrorMsg(e.message);
    }
  };
  return (
    <div>
      <button
        className="btn-primary"
        onClick={() => {
          checkAuth();
        }}
      >
        Ajukan Bid
      </button>
      <Modal
        open={open}
        onClose={() => {
          setOpen(false);
        }}
        center
        classNames={{
          overlay: "",
          modal: "rounded-2xl p-6",
        }}
      >
        <div className="text-gray-600">
          <div className="mt-6">
            <div>
              {/*<p>{JSON.stringify(data)}</p>*/}
              <div>
                <label className="text-xs text-gray-500">Kenapa kami harus memilihmu ?</label>
                <textarea
                  onChange={(e) => setOfferingLetter(e.target.value)}
                  className="form-input text-sm"
                  placeholder="Ketik disini"
                />
              </div>
              <div className="mt-2">
               
              </div>
              <div className="mt-2">
                <input
                  type="checkbox"
                  id="tnc"
                  checked={tncChecked}
                  onChange={(e) => setTncChecked(e.target.checked)}
                />
                <label htmlFor="tnc" className="text-xs text-gray-500 ml-2">
                  Saya telah membaca dan mematuhi  <a href="/mua/terms-and-condition" target="_blank" className="text-xs text-blue-500 underline">
                   syarat dan ketentuan
                </a>
                </label>
              </div>
              {tncError && <div className="text-red-500 text-xs mt-2">Anda harus menyetujui syarat dan ketentuan</div>}
            </div>
            {errorMsg && <ErrorSpan msg={errorMsg} />}
            <div className="flex w-full mt-4">
              <button
                onClick={() => handleBid()}
                className="btn-primary w-full flex text-center justify-center"
              >
                Ajukan Bid
              </button>
            </div>
          </div>
        </div>
      </Modal>
      <Modal
        open={openLogin}
        onClose={() => {
          setOpenLogin(false);
        }}
        center
        classNames={{
          overlay: "",
          modal: "rounded-2xl",
        }}
      > 
      <div>
        <MuaLogin/>
      </div>
      </Modal>
    </div>
  );
};

export default AjukanBid;
