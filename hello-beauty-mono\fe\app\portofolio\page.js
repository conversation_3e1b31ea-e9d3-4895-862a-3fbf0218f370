"use client"
import Link from 'next/link'
import { useEffect, useState } from 'react';
import { api } from '../_helper/api';
import { useSearchParams } from 'next/navigation';
import { Icon } from '@iconify/react';
import Zoom from 'react-medium-image-zoom';
import 'react-medium-image-zoom/dist/styles.css';

export default function Home() {
  const searchParams = useSearchParams();
  const packageId = searchParams.get("packageId");

  const [portofolio, setPortofolio] = useState({});
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  

  const getPortofolio = async () => {
    try {
      setLoading(true);
      const res = await api("GET", "/package/" + packageId);
      setLoading(false);
      if (res.data) {
        setPortofolio(res.data);
      }
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    getPortofolio();
  }, []);

  const openModal = (image) => {
    setSelectedImage(image);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  };

  return (
    <div className="max-w-[480px] mx-auto">
      <div className="h-14 flex items-center px-4">
        <Link href="/booking">
          <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link>
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="p-4">
        <div className="mt-4">
          <h3 className="text-lg font-semibold text-center mb-6" data-aos="fade-up">
            {portofolio?.package?.name} Portofolio<span className="text-hb-pink">.</span>
          </h3>
        </div>
        {loading && (
          <div className="h-96 flex items-center justify-center">
            <Icon
              icon="svg-spinners:180-ring-with-bg"
              className="text-hb-pink animate-spin text-2xl"
            />
          </div>
        )}
        <div className="mt-6 columns-2 [&>img:not(:first-child)]:mt-3">
          {portofolio?.portofolios?.map((item, index) => (
            <img
              src={item.fullUrl}
              key={index}
              data-aos="fade-up"
              className="rounded-2xl w-full object-cover cursor-pointer"
              onClick={() => openModal(item.fullUrl)}
            />
          ))}
        </div>

        {portofolio?.portofolios?.length === 0 && (
          <div className="text-center mt-6" data-aos="fade-up">
            <p className="text-gray-400 mt-3">Portofolio belum tersedia</p>
          </div>
        )}
      </div>

      <div className="h-40"></div>

      
        <div
          className="w-full bg-white fixed bottom-0 left-0 z-[99999]"
          data-aos="fade-up"
        >
          <div className="max-w-[480px] mx-auto grid grid-cols-2 gap-3 h-[80px] items-center p-3 w-full ">
            <Link href="/booking" className="btn-primary bg-black border-black text-center">
              BOOKING
            </Link>
            <a href="https://wa.me/6281299111038" className="btn-secondary bg-white text-center">KONSULTASI</a>
          </div>
        </div>
      

      {isModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[100000]"
          onClick={closeModal}
        >
          <div className="relative" onClick={(e) => e.stopPropagation()}>
            <Zoom>
              <img
                src={selectedImage}
                alt="Selected"
                className="max-w-full max-h-screen rounded-lg"
              />
            </Zoom>
            <button
              className="absolute top-2 right-2 text-white text-2xl"
              onClick={closeModal}
            >
              &times;
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
