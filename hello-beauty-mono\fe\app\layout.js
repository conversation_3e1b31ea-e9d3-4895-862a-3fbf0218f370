// import {  Nunito_Sans } from "next/font/google";
import { AOSInit } from "./aos";
import "./globals.css";

// const nunito = Nunito_Sans({
//   weight: ["400", "500", "700"],
//   subsets: ["latin"],
// });

export const metadata = {
  title: "HelloBeauty",
  description: "#1 On-Demand Makeup Service in Indonesia\nMau cantik? Booking HelloBeauty aja.",
  openGraph: {
    title: "HelloBeauty",
    description: "#1 On-Demand Makeup Service in Indonesia\nMau cantik? Booking HelloBeauty aja.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "HelloBeauty",
    description: "#1 On-Demand Makeup Service in Indonesia\nMau cantik? Booking HelloBeauty aja.",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="id">
      <head>
        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-0YXFKJ6JJT"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-0YXFKJ6JJT');
            `,
          }}
        />
        {/* favicon */}
        <link rel="icon" href="/faviconfix.png" />
        <link rel="apple-touch-icon" href="/faviconfix.png" />
        <link rel="shortcut icon" href="faviconfix.png" />
        {/* meta */}
      </head>
      <AOSInit />
      <body>{children}</body>
    </html>
  );
}
