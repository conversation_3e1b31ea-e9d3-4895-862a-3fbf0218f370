<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="400px"
        @closed="$emit('closed')"
      >
        <div class="p-6">
          <div class="flex justify-between items-center">
            <h1 class="text-lg font-bold">Tarik Saldo Referral</h1>
          </div>
          <div class="mt-4">
            <div class="mb-4">
              <label class="block text-xs font-semibold">Saldo Referral</label>
              {{ useRupiah(props.data.saldoReferral) }}
            </div>

            <div class="mb-4">
              <input
                class="form-input mb-3"
                v-model="amount"
                type="number"
                placeholder="Masukkan Nominal"
              />

              <label class="text-xs text-gray-600 flex items-center gap-1">
                <input type="checkbox" @change="handleTarikSemua" />
                <PERSON><PERSON>
              </label>
            </div>

            <div>
              <textarea
                v-model="description"
                class="form-input"
                placeholder="Deskripsi Penarikan"
              />
            </div>
            <div
              v-if="errorTarik"
              class="bg-red-100 text-red-600 text-xs p-3 rounded-lg mt-3"
            >
              {{ errorTarik }}
            </div>
            <div class="flex justify-start mt-4 gap-2">
              <button class="btn-primary" @click="tarikSaldo">
                Tarik Saldo
              </button>
              <button
                class="p-2 bg-gray-300 text-gray-700 rounded-lg px-4 text-xs"
                @click="showPanel = false"
              >
                Batal
              </button>
            </div>
          </div>
        </div>
        <loader-full v-if="loading" />
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);

const props = defineProps({
  show: Boolean,
  data: String,
});

const showPanel = ref(false);
const loading = ref(false);
const amount = ref(0);
const description = ref("");
const errorTarik = ref("");

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
  },
  { deep: true }
);

const tarikSaldo = async () => {
  try {
    errorTarik.value = "";
    if (!amount.value) {
      errorTarik.value = "Masukkan nominal penarikan";
      return;
    }
    if (amount.value > props.data.saldoReferral) {
      errorTarik.value = "Saldo tidak cukup";
      return;
    }
    if (!description.value) {
      errorTarik.value = "Deskripsi Wajib di isi";
      return;
    }
    loading.value = true;
    const { data } = await adminPost(`/referral/tarik-saldo`, {
      userId: props.data._id,
      amount: amount.value,
      description: description.value,
    });
    loading.value = false;
    $toast.success(data.message);
    emit("refresh");
  } catch (error) {
    loading.value = true;
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};

const handleTarikSemua = (event) => {
  if (event.target.checked) {
    amount.value = props.data.saldoReferral;
  } else {
    amount.value = 0;
  }
};
</script>
