<template>
  <div>
    <span
      :class="colorStatus"
      class="text-[10px] font-semibold px-2 py-1 rounded-full"
    >
      {{ textStatus }}
    </span>
  </div>
</template>

<script setup>
const props = defineProps({
  status: {
    type: String,
    required: true,
  },
});

const colorStatus = computed(() => {
  if (props.status.toUpperCase() === "PAID") {
    return "text-green-600 bg-green-100";
  }
  if (props.status.toUpperCase() === "UNPAID") {
    return "text-red-600 bg-red-100";
  }

  return "text-gray-600 bg-gray-100";
});

const textStatus = computed(() => {
  if (props.status.toUpperCase() === "PAID") {
    return "Lunas";
  }
  if (props.status.toUpperCase() === "UNPAID") {
    return "Belum Lunas";
  }

  return "Tidak Diketahui";
});
</script>
