import slug from '../slug';

describe('slug', () => {
  it('should convert string to slug format', () => {
    const input = 'Hello World! 123';
    const result = slug(input);
    
    // Check if the result matches the expected format
    expect(result).toMatch(/^hello-world-123-[a-z0-9]{4}$/);
    
    // Check if all spaces and special characters are replaced with dashes
    expect(result).not.toMatch(/\s/);
    expect(result).not.toMatch(/[^a-z0-9-]/);
  });

  it('should handle empty string', () => {
    const result = slug('');
    expect(result).toMatch(/^-[a-z0-9]{4}$/);
  });

  it('should handle special characters', () => {
    const input = 'Test@#$%^&*()_+';
    const result = slug(input);
    expect(result).toMatch(/^test-[a-z0-9]{4}$/);
  });

  it('should generate unique slugs for same input', () => {
    const input = 'Test String';
    const results = new Set();
    
    for (let i = 0; i < 100; i++) {
      results.add(slug(input));
    }
    
    // All slugs should be unique due to random string
    expect(results.size).toBe(100);
  });
}); 
