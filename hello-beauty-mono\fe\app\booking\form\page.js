"use client";
import { api } from "@/app/_helper/api";
import convertToK from "@/app/_helper/convertok";
import convertRp from "@/app/_helper/convertorp";
import FormLogin from "@/app/_component/formLogin";
import Head from "next/head";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useMemo, useRef, useState } from "react";
import { Icon } from "@iconify/react";
import ErrorSpan from "@/app/_component/errorSpan";
import {setCookie} from '@/app/_helper/cookie';
import { Modal } from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import ConfirmationModal from "@/app/_component/booking/confirmationModal";


export default function FormBooking() {
  const [listPackage, setListPackage] = useState([]);
  const [packageType, setPackageType] = useState({});
  const [isLogin, setIsLogin] = useState(false);
  const [loadingMe, setLoadingMe] = useState(false);
  const [loadingPackage, setLoadingPackage] = useState(false);
  const [showFromLogin, setShowFromLogin] = useState(false);

  const [loadingDp, setLoadingDp] = useState(false);
  const [dpSetting, setDpSetting] = useState([]);
  const [error, setError] = useState("");
  const [form, setForm] = useState({
    name: "",
    phone: "",
    email: "",
    date: "",
    time: "",
    address: "",
    pax: 1,
    ref: "",
    note: "",
    dp: 100,
  });

  // Set default date to 3 days from today
  useEffect(() => {
    const today = new Date();
    const threeDaysLater = new Date(today);
    threeDaysLater.setDate(today.getDate() + 3);
    
    // Format date as YYYY-MM-DD
    const formattedDate = threeDaysLater.toISOString().split('T')[0];
    setForm(prev => ({ ...prev, date: formattedDate }));
  }, []);

  const [selectedPackage, setSelectedPackage] = useState("");
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [sortType, setSortType] = useState("default");
  const [referral_code, setReferralCode] = useState("");
  const [voucherCode, setVoucherCode] = useState("");
  const [isTCAccepted, setIsTCAccepted] = useState(false);
  const [voucherError, setVoucherError] = useState("");
  const [voucherLoading, setVoucherLoading] = useState(false);
  const [voucherData, setVoucherData] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationData, setConfirmationData] = useState(null);

  const [locations, setLocations] = useState([]);
  const [loadingLocations, setLoadingLocations] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);

  const sortedItems = useMemo(() => {
    if (!packageType?.items) return [];
    return [...packageType.items].sort((a, b) => {
      if (sortType === "cheapest") {
        return a.price - b.price;
      } else if (sortType === "mostExpensive") {
        return b.price - a.price;
      }
      return 0;
    });
  }, [packageType?.items, sortType]);

  const totalPrice = useMemo(() => {
    const selected =
      packageType.items &&
      packageType.items.find((item) => item._id === selectedPackage);
    return selected ? selected.price * form.pax : 0;
  }, [packageType.items, selectedPackage, form.pax]);

  const getDownPaymentOptions = () => {
    if (!form.date) {
      return [];
    }
    
    const today = new Date();
    const selectedDate = new Date(form.date);
    const dayDifference = Math.ceil((selectedDate - today) / (1000 * 60 * 60 * 24));

    if (dayDifference <= 1) {
      return dpSetting.filter(option => option.persen === 100);
    }

    return dpSetting;
  };

  const totalBayar = useMemo(() => {
    const dpPercentage = getDownPaymentOptions().find(option => option.persen === form.dp);
    let finalPrice = totalPrice;
    
    if (voucherData) {
      finalPrice -= voucherData.discountAmount;
    }
    
    return dpPercentage ? finalPrice * (dpPercentage.persen / 100) : finalPrice;
  }, [totalPrice, form.dp, dpSetting, form.date, voucherData]);

  const totalPelunasan = useMemo(() => {
    let finalPrice = totalPrice;
    
    if (voucherData) {
      finalPrice -= voucherData.discountAmount;
    }
    
    return finalPrice - totalBayar;
  }, [totalPrice, totalBayar, voucherData]);

  const increasePax = () => {
    setForm((prevForm) => ({ ...prevForm, pax: prevForm.pax + 1 }));
  };

  const decreasePax = () => {
    if (form.pax > 1) {
      setForm((prevForm) => ({ ...prevForm, pax: prevForm.pax - 1 }));
    }
  };

  const checkLogin = async () => {
    try {
      const res = await api("GET", "/me");
      if (res.data) {
        setForm({
          ...form,
          name: res.data.name,
          phone: res.data.phone,
          email: res.data.email,
        });
      }
    } catch (error) {
      setLoadingMe(false);
      setIsLogin(false);
    }
  };
  const searchParams = useSearchParams();
  const packageId = searchParams.get("packageId");
  const locationId = searchParams.get("locationId");

  const getPackages = async () => {
    try {
      setLoadingPackage(true);
      const res = await api("GET", "/package");
      setLoadingPackage(false);
      setListPackage(res.data);
      
      // Get packageId and itemId from URL
      const urlParams = new URLSearchParams(window.location.search);
      const packageId = urlParams.get('packageId');
      const itemId = urlParams.get('itemId');
      
      if (res.data.length > 0 && packageId) {
        const selectedPackage = res.data.find((item) => item._id === packageId);
        setPackageType(selectedPackage);
        
        // If itemId is provided, select that specific item
        if (itemId && selectedPackage?.items) {
          setSelectedPackage(itemId);
        } else if (selectedPackage?.items?.length > 0) {
          setSelectedPackage(selectedPackage.items[0]._id);
        }
      }
    } catch (error) {
      setLoadingPackage(false);
      console.log(error);
    }
  };

  const getSettingDp = async () => {
    try {
      setLoadingDp(true);
      const res = await api("GET", "/setting/dp");
      setLoadingDp(false);
      setDpSetting(res.data);
    } catch (error) {
      setLoadingDp(false);
      console.log(error);
    }
  };

  const getLocations = async () => {
    try {
      setLoadingLocations(true);
      const res = await api("GET", "/location");
      setLocations(res.data);
      
      // Auto-select location if locationId is provided in URL
      if (locationId) {
        const location = res.data.find(loc => loc._id === locationId);
        if (location) {
          setSelectedLocation(location);
        }
      }
      
      setLoadingLocations(false);
    } catch (error) {
      setLoadingLocations(false);
      console.log(error);
    }
  };

  const isFirstRender = useRef(true);

  useEffect(() => {
    getPackages();
    getSettingDp();
    getLocations();
    // set title
    document.title = "Form Pemesanan";
    if (isFirstRender.current) {
      const f = JSON.parse(localStorage.getItem("formBooking"));
      if (f) {
        setForm(f);
        setSelectedPackage(f.itemId);
        checkLogin();
      }
      isFirstRender.current = false;
    }
  }, [locationId]);

  useEffect(() => {
    const f = {
      ...form,
      packageId: packageType._id,
      itemId: selectedPackage,
      locationId,
    };
    localStorage.setItem("formBooking", JSON.stringify(f));
  }, [form, selectedPackage]);

  useEffect(() => {
    // Update DP options when the date changes
    if (form.date) {
      const availableDP = getDownPaymentOptions();
      if (!availableDP.find(option => option.persen === form.dp)) {
        setForm(prevForm => ({ ...prevForm, dp: availableDP[0]?.persen || 100 }));
      }
    }
  }, [form.date, dpSetting]);

  const estimatedCompletionTime = useMemo(() => {
    if (form.time) {
      const [hours, minutes] = form.time.split(":");
      const startTime = `${form.date} ${form.time}`;
      const date = new Date();
      date.setHours(parseInt(hours) + 2, parseInt(minutes));
      const endTime = date.toTimeString().split(" ")[0].substring(0, 5);
      return `Pengerjaan dimulai pada ${startTime} dan selesai sekitar ${endTime}`;
    }
    return "";
  }, [form.time, form.date]);

  const checkVoucher = async () => {
    if (!voucherCode.trim()) {
      setVoucherError("");
      setVoucherData(null);
      return;
    }

    try {
      setVoucherLoading(true);
      setVoucherError("");
      const res = await api("GET", `/voucher/check?code=${voucherCode.trim()}&amount=${totalPrice}`);
      setVoucherData(res.data);
    } catch (error) {
      if(error.message) {
        setVoucherError(error.message);
      }
      setVoucherData(null);
    } finally {
      setVoucherLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      // check if T&C is accepted
      if (!isTCAccepted) {
        setError("Anda harus menyetujui syarat dan ketentuan");
        return;
      }

      if (!selectedPackage) {
        setError("Pilih paket terlebih dahulu");
        return;
      }

      if (!selectedLocation) {
        setError("Pilih lokasi terlebih dahulu");
        return;
      }

      // check email
      if (!form.email) {
        setError("Email tidak boleh kosong");
        return;
      }

      // check format email
      if (!form.email.includes("@")) {
        setError("Format email tidak valid");
        return;
      }

      // check address
      if (!form.address) {
        setError("Alamat tidak boleh kosong");
        return;
      }

      // check address too short
      if (form.address.length < 10) {
        setError("Alamat terlalu pendek, berikan alamat yang jelas");
        return;
      }

      // pax must be number more than 0
      if (form.pax < 1) {
        setError("Jumlah pax minimal 1");
        return;
      }

      setError("");
      setVoucherError("");

      // Check voucher validity before submitting
      if (voucherCode.trim()) {
        await checkVoucher();
        if (voucherError) {
          return;
        }
      }

      const selectedPackageItem = packageType.items.find(item => item._id === selectedPackage);
      
      setConfirmationData({
        name: form.name,
        phone: form.phone,
        email: form.email,
        date: form.date,
        time: form.time,
        address: form.address,
        pax: form.pax,
        package: selectedPackageItem.name,
        price: convertToK(selectedPackageItem.price),
        totalPrice: convertToK(totalPrice),
        dp: form.dp,
        dpAmount: convertToK(totalBayar),
        voucherDiscount: voucherData ? convertToK(voucherData.discountAmount) : "0",
        finalPrice: convertToK(totalPrice - (voucherData?.discountAmount || 0)),
        location: selectedLocation.name
      });
      
      setShowConfirmation(true);
    } catch (error) {
      setError(error.message);
    }
  };

  const handleConfirmSubmit = async () => {
    try {
      setLoadingSubmit(true);
      const payload = {
        ...form,
        packageId: packageType._id,
        itemId: selectedPackage,
        locationId: selectedLocation._id,
        referral_code,
        voucherCode: voucherCode.trim() || undefined
      };

      const res = await api("POST", "/order", payload);
      if (res.data) {
        localStorage.removeItem("formBooking");
        setForm({
          name: "",
          phone: "",
          email: "",
          date: "",
          time: "",
          address: "",
          pax: 1,
          ref: "",
          note: "",
          dp: 100,
        });
        setSelectedPackage("");
        setVoucherCode("");
        setVoucherData(null);
        setCookie('hb_token', res.token);
        setTimeout(() => {
          window.location.href = "/booking/" + res.data.trxId;
        }, 500);
      }
    } catch (error) {
      setLoadingSubmit(false);
      setError(error.message);
    }
  };

  return (
    <>
      <Head>
        <title>Booking</title>
      </Head>
      <Suspense fallback={<div>Loading...</div>}>
        <form className="max-w-[480px] min-h-screen mx-auto" onSubmit={handleSubmit}>
          <div className="h-14 flex items-center px-4">
            <Link href="/">
              <img src="/icons/arrow-left.svg" alt="Logo" className="" />
            </Link>
            <img src="/logo.png" alt="Logo" className="mx-auto h-8" />
          </div>
          <div className=" form-bg p-4 h-[200px] flex items-center">
            <div>
              <h2 className="z-50 text-xl mb-4 font-semibold">
                Paket Makeup <br />
                {packageType.name}
              </h2>
              <span>Isi form untuk melakukan booking</span>
            </div>
          </div>

          <div className="dot-bg h-[300px]">
            <div className="px-4 py-2">
              <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white -mt-10">
                <Icon icon="mdi:account" className="mr-2 text-xl text-hb-pink" />
                <input
                  required
                  type="text"
                  placeholder="Nama"
                  value={form.name}
                  onChange={(e) => {
                    setForm({ ...form, name: e.target.value });
                  }}
                  className="w-full focus:outline-none text-sm"
                />
              </div>

              <div className="w-full p-3 border rounded-lg mb-2 flex items-center group bg-white">
                <Icon icon="mdi:whatsapp" className="mr-2 text-xl text-hb-pink" />
                <input
                  required
                  type="text"
                  inputMode="numeric"
                  value={form.phone}
                  onChange={(e) => {
                    setForm({ ...form, phone: e.target.value });
                  }}
                  placeholder="Nomor Whatsapp"
                  className="w-full focus:outline-none text-sm"
                />
              </div>
              <div className="flex items-center gap-2 mb-4 px-1">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                <span className="text-xs text-gray-600 font-medium">
                  Pastikan nomor WhatsApp benar ya, biar konfirmasi lancar!
                </span>
              </div>

              <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
                <Icon icon="mdi:email" className="mr-2 text-xl text-hb-pink" />
                <input
                  required
                  type="email"
                  value={form.email}
                  onChange={(e) => {
                    setForm({ ...form, email: e.target.value });
                  }}
                  placeholder="Email"
                  className="w-full focus:outline-none text-sm"
                />
              </div>
            </div>

            <div className="px-4">
              <div className="h-[1px] w-full bg-gray-200 mb-4" />
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="w-full p-3 border rounded-lg flex items-center group bg-white">
                  <Icon icon="mdi:calendar" className="mr-2 text-xl text-hb-pink" />
                  <input
                    required
                    type="date"
                    style={{ appearance: "none" }}
                    placeholder="00/00/00"
                    min={new Date().toISOString().split("T")[0]}
                    value={form.date}
                    onChange={(e) => {
                      setForm({ ...form, date: e.target.value });
                    }}
                    className="w-full focus:outline-none text-sm"
                  />
                </div>
                <div className="w-full p-3 border rounded-lg flex items-center group bg-white">
                  <Icon icon="mdi:clock" className="mr-2 text-xl text-hb-pink" />
                  <input
                    required
                    type="time"
                    placeholder="00:00"
                    value={form.time}
                    onChange={(e) => {
                      setForm({ ...form, time: e.target.value });
                    }}
                    className="w-full focus:outline-none text-sm"
                  />
                </div>
               
              </div>
               {form.date && form.time && (
                  <div className="text-sm p-2 bg-hb-pink-light-2 w-full col-span-2 rounded-lg text-hb-pink mb-4">
                    {estimatedCompletionTime}
                  </div>
                )}
              <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
                <Icon icon="mdi:map-marker" className="mr-2 text-xl text-hb-pink" />
                <input
                  required
                  type="text"
                  placeholder="Alamat Lengkap"
                  value={form.address}
                  onChange={(e) => {
                    setForm({ ...form, address: e.target.value });
                  }}
                  className="w-full focus:outline-none text-sm"
                />
              </div>

              <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
                <Icon icon="mdi:store" className="mr-2 text-xl text-hb-pink" />
                <select
                  required
                  value={selectedLocation?._id || ""}
                  onChange={(e) => {
                    const location = locations.find(loc => loc._id === e.target.value);
                    setSelectedLocation(location);
                  }}
                  className="w-full focus:outline-none bg-transparent text-sm"
                >
                  <option value="">Pilih Lokasi</option>
                  {locations.map((location) => (
                    <option key={location._id} value={location._id}>
                      {location.name}
                    </option>
                  ))}
                </select>
                {loadingLocations && (
                  <Icon icon="svg-spinners:180-ring-with-bg" className="text-hb-pink animate-spin" />
                )}
              </div>

              <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
                <Icon icon="mdi:account-group" className="mr-2 text-xl text-hb-pink" />
                <input
                  required
                  type="number"
                  value={form.pax}
                  onChange={(e) => {
                    setForm({ ...form, pax: e.target.value });
                  }}
                  placeholder="Jumlah Pax"
                  className="w-full focus:outline-none text-sm"
                />

                <div className="flex gap-2 cursor-pointer">
                  <div
                    className="bg-hb-pink px-3 h-8 flex items-center justify-center text-white rounded-lg"
                    onClick={() => {
                      decreasePax();
                    }}
                  >
                    -
                  </div>
                  <div
                    className="bg-hb-pink px-3 h-8 flex items-center justify-center text-white rounded-lg"
                    onClick={() => {
                      increasePax();
                    }}
                  >
                    +
                  </div>
                </div>
              </div>

              <div className="mb-4 p-3 rounded-xl border">
                <div className="font-semibold mb-2 flex justify-between items-center">
                  <span className="text-sm">Pilih Paket</span>
                  <button 
                    onClick={() => {
                      if (sortType === "default") setSortType("cheapest");
                      else if (sortType === "cheapest") setSortType("mostExpensive");
                      else setSortType("default");
                    }} type="button"
                    className="text-hb-pink hover:bg-hb-pink-light-2 p-2 rounded-lg"
                  >
                    <Icon 
                      icon={
                        sortType === "default" 
                          ? "mdi:sort" 
                          : sortType === "cheapest" 
                            ? "mdi:sort-numeric-ascending" 
                            : "mdi:sort-numeric-descending"
                      } 
                      className="text-xl"
                    />
                  </button>
                </div>
                <div className="grid grid-cols-1 gap-2 cursor-pointer">
                  {sortedItems.map((item, index) => (
                    <div
                      key={index}
                      onClick={() => {
                        setSelectedPackage(item._id);
                      }}
                      className={`border rounded-lg flex justify-between cursor-pointer ${
                        item._id === selectedPackage ? "bg-hb-pink-light-2 font-semibold text-hb-pink border-hb-pink" : ""
                      }`}
                    >
                      <h4 className="p-3 flex items-center gap-1">
                        <div>
                          <Icon icon="maki:diamond" className="text-hb-pink text-sm" />
                        </div>
                        <div className="text-sm">
                          {item.name}
                        </div>
                      </h4>
                      <div className="p-3 text-hb-pink rounded-r-md">
                        {convertToK(item.price)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {form.date && (
                <div className="mb-4 p-3 rounded-xl border">
                  <div className="font-semibold mb-2 text-sm">Down Payment</div>
                  <div className="grid grid-cols-2 bg-gray-100 p-3 rounded-md">
                    <h4 className="text-sm">Pilih DP</h4>
                    <select
                      value={form.dp}
                      onChange={(e) => {
                        setForm({ ...form, dp: parseInt(e.target.value) });
                      }}
                      disabled={!form.date}
                      className="text-right outline-none bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                    >
                      {getDownPaymentOptions().map((option) => (
                        <option key={option.persen} value={option.persen}>
                          {option.persen}%
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              )}

              <div className="space-y-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Kode Referral</label>
                  <div className="w-full p-2 border rounded-lg flex items-center group bg-white">
                    <Icon icon="mdi:account-group" className="mr-2 text-lg text-hb-pink" />
                    <input
                      type="text"
                      value={referral_code}
                      onChange={(e) => {
                        setReferralCode(e.target.value);
                      }}
                      placeholder="Masukkan kode referral"
                      className="w-full focus:outline-none text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Kode Promo</label>
                  <div className="w-full p-2 border rounded-lg flex items-center group bg-white">
                    <Icon icon="mdi:tag" className="mr-2 text-lg text-hb-pink" />
                    <input
                      type="text"
                      value={voucherCode}
                      onChange={(e) => {
                        setVoucherCode(e.target.value);
                        setVoucherError("");
                        setVoucherData(null);
                      }}
                      placeholder="Masukkan kode promo"
                      className="w-full focus:outline-none text-sm"
                    />
                    <button
                      type="button"
                      onClick={checkVoucher}
                      disabled={!voucherCode.trim() || voucherLoading}
                      className="bg-hb-pink text-white px-3 py-1 rounded-lg text-xs flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {voucherLoading ? (
                        <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin" />
                      ) : (
                        <Icon icon="tabler:discount" />
                      )}
                      Gunakan
                    </button>
                  </div>
                </div>
              </div>

              {voucherError && (
                <div className="mb-4 p-3 rounded-xl border border-red-200 bg-red-50">
                  <div className="text-red-500 text-sm flex items-center gap-2">
                    <Icon icon="tabler:alert-circle" className="text-lg" />
                    {voucherError}
                  </div>
                </div>
              )}
              {voucherData && (
                <div className="mb-4 p-3 rounded-xl border border-blue-200 bg-blue-50">
                  <div className="text-blue-600 text-sm flex items-center gap-2">
                    <Icon icon="tabler:discount-check" className="text-lg" />
                    Yeay! Kamu dapat potongan {convertRp(voucherData.discountAmount)} 🎉
                  </div>
                </div>
              )}

              <div>
                <label className="text-sm">Keterangan</label>
                <textarea
                  type="text"
                  value={form.note}
                  onChange={(e) => {
                    setForm({ ...form, note: e.target.value });
                  }}
                  placeholder="Masukkan Keterangan"
                  className="w-full mt-2 p-3 border rounded-lg mb-4 flex items-center group bg-white text-sm"
                />
              </div>

              <div className="bg-gray-100 rounded-xl text-sm p-3 space-y-3">
                <div className="space-y-2">
                  <div className="text-gray-500 text-xs">Rincian Harga</div>
                  <div className="flex justify-between">
                    <div>Harga Paket</div>
                    <div>{convertRp(totalPrice)}</div>
                  </div>
                  {voucherData && (
                    <div className="flex justify-between">
                      <div>Potongan Voucher</div>
                      <div className="text-blue-600">-{convertRp(voucherData.discountAmount)}</div>
                    </div>
                  )}
                  <div className="flex justify-between border-t pt-2">
                    <div>Total Harga</div>
                    <div>{convertRp(totalPrice - (voucherData?.discountAmount || 0))}</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-gray-500 text-xs">Pembayaran Saat Ini</div>
                  <div className="flex justify-between items-center bg-hb-pink-light-2 p-3 rounded-xl">
                    <div className="font-semibold text-hb-pink">
                      {form.dp === 100 ? 'Total Pembayaran' : `Down Payment (${form.dp}%)`}
                    </div>
                    <div className="text-hb-pink font-bold text-lg">
                      {convertRp(totalBayar)}
                    </div>
                  </div>
                </div>

                {form.date && form.dp !== 100 && (
                  <div className="space-y-2">
                    <div className="text-gray-500 text-xs">Pembayaran Selanjutnya</div>
                    <div className="flex justify-between">
                      <div>Pelunasan</div>
                      <div className="text-gray-600">{convertRp(totalPelunasan)}</div>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-6">
                {
                  error&& <ErrorSpan msg={error} />
                }
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="tcCheckbox"
                    checked={isTCAccepted}
                    onChange={(e) => setIsTCAccepted(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="tcCheckbox" className="text-sm">
                    Saya menyetujui <Link href={`/terms-and-condition`} className="underline text-hb-pink">Syarat & Ketentuan</Link>
                  </label>
                </div>
                <button
                  type="submit"
                  disabled={loadingSubmit || !isTCAccepted}
                  className="btn-primary flex justify-center items-center"
                >
                  {loadingSubmit && (
                    <Icon
                      icon="svg-spinners:180-ring-with-bg"
                      className="text-white animate-spin mr-2"
                    />
                  )}
                  BOOK NOW
                </button>
                <div className="text-center py-3">
                  <Link
                    href={`/terms-and-condition`}
                    className="underline text-center mt-6 text-hb-pink"
                  >
                    Terms & Conditions
                  </Link>
                </div>
              </div>
            </div>
          </div>
          <div className="h-20" />
        </form>

        {
          showFromLogin&& <FormLogin onClose={()=>{
            setShowFromLogin(false)
          }}/>
        }
        {/* <FormLogin/> */}
      </Suspense>
      <ConfirmationModal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={handleConfirmSubmit}
        isLoading={loadingSubmit}
        data={confirmationData}
      />
    </>
  );
}
