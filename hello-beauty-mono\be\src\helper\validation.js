const emailRegex = /^[a-zA-Z0-9](?:[a-zA-Z0-9._%+-]*[a-zA-Z0-9])?@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const phoneRegex = /^(?:\+?62|0)8[1-9][0-9]{6,10}$/;

const validateEmail = (email) => {
  if (!email) {
    return {
      isValid: false,
      message: "Email tidak boleh kosong"
    };
  }

  // Check for repeated special characters
  if (/([._%+-])\1/.test(email)) {
    return {
      isValid: false,
      message: "Email tidak valid: Karakter khusus tidak boleh berulang"
    };
  }

  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      message: "Format email tidak valid"
    };
  }

  return {
    isValid: true,
    message: "Email valid"
  };
};

const validatePhone = (phone) => {
  if (!phone) {
    return {
      isValid: false,
      message: "Nomor telepon tidak boleh kosong"
    };
  }

  // Remove any non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');

  // Check length
  if (cleanPhone.length < 10 || cleanPhone.length > 13) {
    return {
      isValid: false,
      message: "Panjang nomor telepon tidak valid (10-13 digit)"
    };
  }

  // Check if phone number matches the pattern
  if (!phoneRegex.test(cleanPhone)) {
    return {
      isValid: false,
      message: "Format nomor telepon tidak valid. Gunakan format 08xx atau +628xx"
    };
  }

  // Convert to standard format (08xx)
  let formattedPhone = cleanPhone;
  if (cleanPhone.startsWith('62')) {
    formattedPhone = '0' + cleanPhone.slice(2);
  } else if (cleanPhone.startsWith('+62')) {
    formattedPhone = '0' + cleanPhone.slice(3);
  }

  // Ensure the number starts with 08
  if (!formattedPhone.startsWith('08')) {
    return {
      isValid: false,
      message: "Nomor telepon harus dimulai dengan 08"
    };
  }

  return {
    isValid: true,
    message: "Nomor telepon valid",
    formattedPhone
  };
};

module.exports = {
  validateEmail,
  validatePhone
}; 
