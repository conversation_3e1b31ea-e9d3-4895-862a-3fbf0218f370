<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div  class="flex gap-2 items-center mb-4" @click.prevent="$emit('closed')">
             <icon name="line-md:arrow-left"/>
            <h2 class="font-bold">
              Tambah Tag
            </h2>
          </div>
          

        <div class="p-4">
          <div class="grid grid-cols-4 gap-2">
            <div v-for="(d,i) in data" :key="i">
              <img :src="d.fullUrl" class="w-full border h-32 object-contain rounded-lg" >
            </div>
          </div>
          <div class="my-4">
            <label class="text-sm">Paket</label>
            <select v-model="packageId" class="form-input">
              <option value="" disabled selected>Pilih <PERSON></option>
              <option v-for="d in packages" :key="d._id" :value="d._id">
                {{ d.name }}
              </option>
            </select>
          </div>
           <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </div>
      </form></VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(['closed', 'refresh']);

const props = defineProps({
  show: Boolean,
  data: Object
});

const showPanel = ref(false);

const loading = ref(false);

const packages = ref([]);
const packageId = ref('');

watch(
  () => props.show,
  val => {
    showPanel.value = !!val;
    getPackages();
  },
  { deep: true }
);

const getPackages = async () => {
  try {
    const { data } = await adminGet(`/package?page=1&limit=10`);
    packages.value = data.data
  } catch (error) {
    console.log(error);
    
    // $toast.error(`Terjadi kesalahan ${error}`);
  }
};

const images = computed(() => {
  return props.data.map((d) => {
    return d._id
  });
});


const save = async () => {
  try {
    loading.value = true;

    const p = {
      images: images.value,
      packageId: packageId.value
    };
    const res= await adminPut('/portofolio/', p);
    loading.value = false;
    $toast.success(res.data.message);
    emit('refresh');
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
