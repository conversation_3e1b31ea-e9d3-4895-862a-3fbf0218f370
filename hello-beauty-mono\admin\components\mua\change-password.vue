<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">Change Password</h2>
          </div>

          <div class="mt-4">
            <label>New Password</label>
            <input
              required
              type="password"
              v-model="payload.newPassword"
              class="form-input"
            />
          </div>

          <div class="mt-4">
            <label>Confirm Password</label>
            <input
              required
              type="password"
              v-model="payload.confirmPassword"
              class="form-input"
            />
          </div>

          <div class="h-4" />
          <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Saving" : "Save" }}
              </span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Cancel
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed"]);
const props = defineProps({
  show: Boolean,
  muaId: String,
});

const showPanel = ref(false);

const payload = ref({
  newPassword: "",
  confirmPassword: "",
});
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    payload.value.newPassword = "";
    payload.value.confirmPassword = "";
  },
  { deep: true }
);

const save = async () => {
  if (payload.value.newPassword !== payload.value.confirmPassword) {
    $toast.error("Passwords do not match");
    return;
  }

  try {
    loading.value = true;
    const res = await adminPost(`/mua/change-password`, {
      muaId: props.muaId,
      password: payload.value.newPassword,
    });
    loading.value = false;

    $toast.success(res.data.message);
    emit("closed");
  } catch (error) {
    loading.value = false;
    $toast.error(`Error: ${error.response.data.message}`);
  }
};
</script>
