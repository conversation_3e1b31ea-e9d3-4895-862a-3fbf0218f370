const mua = require("../../models/mua");
const payment = require("../../models/payment");
const transaction = require("../../models/transaction");
const user = require("../../models/user");

const getTotalCustomer = async (req, res) => {
    try {
      const total = await user.countDocuments();
     
      
      return res.status(200).json({ message: 'Total customer', data: total });
    } catch (error) {
        console.log(error);
        return res.status(500).json({ message: 'Internal server error' });
    }
}

const getTotalMua = async (req, res) => {
    try {
      const total = await mua.countDocuments();
      return res.status(200).json({ message: 'Total Mua', data: total });
    } catch (error) {
        console.log(error);
        return res.status(500).json({ message: 'Internal server error' });
    }
}

// Helper to get start of day in UTC+7, returned as UTC for MongoDB
function getStartOfDayUTC7(date = new Date()) {
  const utc7Offset = 7 * 60 * 60 * 1000;
  const d = new Date(date.getTime() + utc7Offset);
  d.setHours(0, 0, 0, 0);
  d.setTime(d.getTime() - utc7Offset);
  return d;
}

// Helper to get start of yesterday in UTC+7, returned as UTC for MongoDB
function getStartOfYesterdayUTC7(date = new Date()) {
  const startOfToday = getStartOfDayUTC7(date);
  return new Date(startOfToday.getTime() - 24 * 60 * 60 * 1000);
}

const getSummary = async (req, res) => {
  try {
    // Parallelize DB calls for efficiency
    const [totalMua, totalCustomer] = await Promise.all([
      mua.countDocuments(),
      user.countDocuments(),
    ]);

    // Date calculations
    const now = new Date();
    const startOfToday = getStartOfDayUTC7(now);
    const startOfYesterday = getStartOfYesterdayUTC7(now);

    // Parallelize transaction and payment queries
    const [
      transactionToday,
      transactionYesterday,
      paymentToday,
      paymentYesterday
    ] = await Promise.all([
      transaction.find({ createdAt: { $gte: startOfToday } }),
      transaction.find({ createdAt: { $gte: startOfYesterday, $lt: startOfToday } }),
      payment.find({ status: "PAID", paidAt: { $gte: startOfToday } }),
      payment.find({ status: "PAID", paidAt: { $gte: startOfYesterday, $lt: startOfToday } })
    ]);

    // Calculate totals
    const totalTransactionToday = transactionToday.length;
    const totalTransactionYesterday = transactionYesterday.length;
    const totalPaymentToday = paymentToday.length;
    const totalPaymentTodayAmount = paymentToday.reduce((acc, item) => acc + item.totalBayar, 0);
    const totalPaymentYesterday = paymentYesterday.length;
    const totalPaymentYesterdayAmount = paymentYesterday.reduce((acc, item) => acc + item.totalBayar, 0);

    return res.status(200).json({
      message: 'Summary',
      data: {
        mua: totalMua,
        customer: totalCustomer,
        transactionToday: totalTransactionToday,
        transactionYesterday: totalTransactionYesterday,
        paymentToday: totalPaymentToday,
        paymentTodayAmount: totalPaymentTodayAmount,
        paymentYesterday: totalPaymentYesterday,
        paymentYesterdayAmount: totalPaymentYesterdayAmount
      }
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

const getGraph = async (req, res) => {
  try {
    const { start, end } = req.query;
    let s = start;
    let e = end;
    
    if(!start || !end) {
      s = new Date();
      s.setDate(s.getDate() - 7);
      e = new Date();
    }

    // Convert to UTC+7
    const utc7Offset = 7 * 60 * 60 * 1000;
    const startDate = new Date(new Date(s).getTime() + utc7Offset);
    const endDate = new Date(new Date(e).getTime() + utc7Offset);

    // Set start of day in UTC+7
    startDate.setHours(0, 0, 0, 0);
    startDate.setTime(startDate.getTime() - utc7Offset); // Convert back to UTC for MongoDB

    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() - 30);
    if(startDate < maxDate) {
      return res.status(400).json({ message: 'Date range is too long' });
    }

    // Set end of day in UTC+7
    endDate.setHours(23, 59, 59, 999);
    endDate.setTime(endDate.getTime() - utc7Offset); // Convert back to UTC for MongoDB

    const data = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const nextDate = new Date(currentDate);
      nextDate.setDate(currentDate.getDate() + 1);

      const transactions = await transaction.countDocuments({
        createdAt: {
          $gte: currentDate,
          $lt: nextDate
        }
      });

      // Convert to UTC+7 for display
      const displayDate = new Date(currentDate.getTime() + utc7Offset);
      data.push({
        date: displayDate.toISOString().split('T')[0],
        total: transactions
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return res.status(200).json({ message: 'Graph data', data });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

const getGraphRevenue = async (req, res) => {
  const { start, end } = req.query;

  if (!start || !end) {
    return res.status(400).json({ error: 'Start and end dates are required' });
  }

  // Convert to UTC+7
  const utc7Offset = 7 * 60 * 60 * 1000;
  const startDate = new Date(new Date(start).getTime() + utc7Offset);
  const endDate = new Date(new Date(end).getTime() + utc7Offset);

  // Set start of day and end of day in UTC+7, then convert back to UTC for MongoDB
  startDate.setHours(0, 0, 0, 0);
  startDate.setTime(startDate.getTime() - utc7Offset);
  
  endDate.setHours(23, 59, 59, 999);
  endDate.setTime(endDate.getTime() - utc7Offset);

  console.log('Start Date:', startDate);
  console.log('End Date:', endDate);

  const dateArray = [];
  let currentDate = new Date(startDate.getTime() + utc7Offset);
  const displayEndDate = new Date(endDate.getTime() + utc7Offset);
  
  while (currentDate <= displayEndDate) {
    dateArray.push(currentDate.toISOString().split('T')[0]);
    currentDate.setDate(currentDate.getDate() + 1);
  }

  try {
    const revenueData = await payment.aggregate([
      { $match: { status: 'PAID', paidAt: { $gte: startDate, $lte: endDate } } },
      {
        $group: {
          _id: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: {
                $add: ["$paidAt", utc7Offset] // Convert to UTC+7 in aggregation
              }
            }
          },
          total: { $sum: "$totalBayar" }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    const revenueMap = revenueData.reduce((acc, item) => {
      acc[item._id] = item.total;
      return acc;
    }, {});

    const result = dateArray.map(date => ({
      date,
      total: revenueMap[date] || 0
    }));

    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
    getTotalCustomer,
    getTotalMua,
    getSummary,
    getGraph,
    getGraphRevenue
}
