"use client";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { apiMua } from "@/app/_helper/api-mua";

export default function ChangePassword() {
  const [form, setForm] = useState({
    old_password: "",
    new_password: "",
    confirm_password: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [passwordVisible, setPasswordVisible] = useState({
    old: false,
    new: false,
    confirm: false,
  });

  const checkPassword = useMemo(() => {
    if(form.old_password === "" || form.new_password === "" || form.confirm_password === "") {
      return "";
    }
    // check password criteria
    if(form.new_password.length < 8) {
      return "Password minimal 8 karakter";
    }
    if(form.new_password !== form.confirm_password) {
      return "Password tidak sama";
    }
    
    return "";
  })

  const handleSubmit = async (e) => { 
    e.preventDefault();
    if(checkPassword) {
      return;
    }
    setError("");
    setLoading(true);
    try {
      await apiMua("POST", "/mua/change-password", form);
      setForm({
        old_password: "",
        new_password: "",
        confirm_password: "",
      });
      window.location.href = "/mua/profil";
    } catch (error) {
      setError(error.message);
    }
    setLoading(false);
  }


  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
         <div className="h-16 flex items-center px-4 justify-between  max-w-[480px]">
          <Link href={`/mua/profil`}>
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
        </div>
        <div className="pt-4">
          <h3 className="text-2xl font-semibold text-center mb-6">
            Change<br/> Password<span className="text-hb-pink">.</span>
          </h3>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-3">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Password Lama
              </label>
              <input
                type={passwordVisible.old ? "text" : "password"}
                name="old_password"
                disabled={loading}
                required
                onChange={(e) => setForm({ ...form, old_password: e.target.value })}
                id="old_password"
                autoComplete="old_password"
                className="form-input"
              />
              <button
                type="button"
                onClick={() => setPasswordVisible({ ...passwordVisible, old: !passwordVisible.old })}
                className="ml-2"
              >
                <Icon icon={passwordVisible.old ? "mdi:eye-off" : "mdi:eye"} />
              </button>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Password Baru
              </label>
              <input
                type={passwordVisible.new ? "text" : "password"}
                name="new_password"
                id="new_password"
                disabled={loading}
                required
                onChange={(e) => setForm({ ...form, new_password: e.target.value })}
                autoComplete="new_password"
                className="form-input"
              />
              <button
                type="button"
                onClick={() => setPasswordVisible({ ...passwordVisible, new: !passwordVisible.new })}
                className="ml-2"
              >
                <Icon icon={passwordVisible.new ? "mdi:eye-off" : "mdi:eye"} />
              </button>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Konfirmasi Password Baru
              </label>
              <input
                type={passwordVisible.confirm ? "text" : "password"}
                name="confirm_password"
                id="confirm_password"
                disabled={loading}
                required
                onChange={(e) => setForm({ ...form, confirm_password: e.target.value })}
                autoComplete="confirm_password"
                className="form-input"
              />
              <button
                type="button"
                onClick={() => setPasswordVisible({ ...passwordVisible, confirm: !passwordVisible.confirm })}
                className="ml-2"
              >
                <Icon icon={passwordVisible.confirm ? "mdi:eye-off" : "mdi:eye"} />
              </button>
            </div>
            {checkPassword && (
              <div className="text-red-500 text-sm mb-4">
                {checkPassword}
              </div>
            )}
            {
              error && (
                <div className="text-red-500 text-sm mb-4">
                  {error}
                </div>
              )
            }
            <div className="mb-4">
              <button
            type="submit"
            className="btn-primary flex items-center justify-center"
          >
            {loading && (
              <Icon
                icon="svg-spinners:180-ring-with-bg"
                className="text-white animate-spin mr-2"
              />
            )}
            Simpan
          </button>
                </div>
          </div>
        </form>
      </div>
    </div>
  );
}
