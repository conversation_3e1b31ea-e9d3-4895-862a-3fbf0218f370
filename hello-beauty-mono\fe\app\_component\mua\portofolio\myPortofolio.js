"use client";
import { apiMua, apiMuaUpload } from "@/app/_helper/api-mua";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import { Modal } from "react-responsive-modal";
import "react-responsive-modal/styles.css";

const MyPortofolio = () => {
   // get Portfolio
   const [portofolio, setPortofolio] = useState([]);
   const [uploadProgress, setUploadProgress] = useState({});
   const [openDelete, setOpenDelete] = useState(false);
   const [selectedItem, setSelectedItem] = useState(null);

   const getPortofolio = async () => {
    try {
      const response = await apiMua("GET", "/portofolio");
      setPortofolio(response.data);
    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
    getPortofolio();
  }, []);

   const handleFile = (e) => {
    const files = e.target.files;
    for (let i = 0; i < files.length; i++) {
      uploadFile(files[i]);
    }
  }

  const uploadFile = async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      setUploadProgress((prev) => ({ ...prev, [file.name]: 0 }));

      const res = await apiMuaUpload('/portofolio', formData, {
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress((prev) => ({ ...prev, [file.name]: percentCompleted }));
        }
      });

      if (res.data) {
        getPortofolio();
      }
      setUploadProgress((prev) => {
        const newProgress = { ...prev, [file.name]: 100 };
        setTimeout(() => {
          setUploadProgress((currentProgress) => {
            const { [file.name]: _, ...remainingProgress } = currentProgress;
            return remainingProgress;
          });
        }, 1000);
        return newProgress;
      });
    } catch (error) {
      setUploadProgress((prev) => ({ ...prev, [file.name]: 0 }));
    }
  }

  const handleDelete = async () => {
    try {
      await apiMua("DELETE", `/portofolio/${selectedItem._id}`);
      setOpenDelete(false);
      getPortofolio();
    } catch (error) {
      console.log(error);
    }
  }

  return(
    <div>
      <div className="p-3 border rounded-xl mt-4">
         <h3 className="font-semibold flex gap-1 items-center mb-2">
        <Icon icon="streamline:make-up-brush" className="text-lg text-hb-pink" />
        <div className="text-sm mt-1">
        Portofolio
        </div>
      </h3>
        <div>
          <div className="grid grid-cols-3 gap-3">
            {
              portofolio.map((item, index) => (
                <div key={item._id} className="relative">
                  <img src={item.fullUrl} alt={`Portofolio ${index}`} className="w-full h-32 border object-cover rounded-xl" />
                  <button 
                    className="absolute top-2 right-2 text-xs bg-red-500 text-white px-2 py-1 rounded"
                    onClick={() => {
                      setSelectedItem(item);
                      setOpenDelete(true);
                    }}
                  >
                    Hapus
                  </button>
                </div>
              ))
            }
            {
              Object.keys(uploadProgress).map((fileName) => (
                <div key={fileName} className="relative w-full h-32 border object-cover rounded-xl bg-gray-200">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="loader"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center text-black">
                    {fileName}: {uploadProgress[fileName]}%
                  </div>
                </div>
              ))
            }
            <div className="w-full">
              <label className="h-32 flex items-center justify-center bg-hb-pink-light-2 rounded-xl border-2 border-hb-pink">
                <Icon icon="mage:image-upload" className="text-4xl mx-auto text-hb-pink" />
                <input type="file" multiple accept="image/*" onChange={(e) => handleFile(e)} style={{ display: 'none' }} />
              </label>
            </div>
          </div>
        </div>
      </div>
      <Modal
        open={openDelete}
        onClose={() => setOpenDelete(false)}
        center
        classNames={{
          overlay: "",
          modal: "rounded-2xl p-6",
        }}
      >
        <div className="text-gray-600 pt-12">
          <p>Apakah kamu yakin menghapus foto ini ?</p>
          <div className="flex w-full mt-4">
            <button
              onClick={() => handleDelete()}
              className="btn-primary w-full flex text-center justify-center"
            >
              Ya, Hapus
            </button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default MyPortofolio;
