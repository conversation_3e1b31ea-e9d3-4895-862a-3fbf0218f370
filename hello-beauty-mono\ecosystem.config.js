module.exports = {
  apps: [
    {
      name: 'hello-beauty-api',
      script: './src/server.js',
      cwd: './be',
      env: {
        PORT: 3500,
        NODE_ENV: 'production', // Set environment to production
      },
      max_memory_restart: '1G',  // Batasi memori 2GB
      instances: 'max',           // Gunakan semua core
      exec_mode: 'cluster',       // Mode cluster
    },
    {
      name: 'hello-beauty-admin',
      script: './.output/server/index.mjs',
      cwd: './admin',
      env: {
        PORT: 3501,
        NODE_ENV: 'production', // Set environment to production
      },
      max_memory_restart: '1G',  // Batasi memori 1GB
      instances: 'max',           // Gunakan semua core
      exec_mode: 'cluster',       // Mode cluster
    },
    {
      name: 'hello-beauty-fe',
      script: 'node_modules/next/dist/bin/next',
      args: 'start -p 3502',
      cwd: './fe',
      max_memory_restart: '2G',  // Batasi memori 2GB
      instances: 'max',           // Gunakan semua core
      exec_mode: 'cluster',       // Mode cluster
    }
  ]
};
