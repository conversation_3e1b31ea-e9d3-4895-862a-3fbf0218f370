<template>
  <div>
    <div
      class="border-r h-screen fixed p-3 bg-primary transition-all text-white"
      :class="isFull ? 'sidebar-full' : 'sidebar-mini'"
    >
      <div
        v-if="showSidebar"
        class="relative h-[calc(100vh-6rem)]"
      >
      <img src="/logo-2.png" class="h-8  mb-4" >
        <div class="mt-4">
          <nuxt-link
            v-for="(m, ix) in menu"
            v-show="m.show"
            :key="ix"
            :to="m.link"
            class="flex items-center h-10 text-sm gap-2 rounded-md mb-2 px-3"
            :class="
              m.link === route.fullPath || includeRoute === m.subRoute
                ? ' bg-gray-50 text-primary border'
                : 'text-slate-100 hover:translate-x-1 transition-all hover:bg-primary'
            "
          >
            <icon :name="m.icon" class="text-xl" />
            <label v-if="isFull">
              {{ m.label }}
            </label>
          </nuxt-link>
        </div>
        <div class="">
          <div class="pb-2 w-full border-t border-black opacity-10" />
          <nuxt-link
            to="/logout"
            class="flex items-center h-10 text-sm gap-2 rounded-md mb-2 px-3 text-slate-100 hover:translate-x-1 transition-all hover:bg-primary"
          >
            <icon name="tabler:logout-2" />
            <label v-if="isFull"> Keluar </label>
          </nuxt-link>
        </div>
      </div>
    </div>
    <div class="mobile-menu-button" @click="toggleSidebar">
      <icon name="tabler:menu-2" class="text-white text-2xl" />
    </div>
  </div>
</template>

<script setup>
// import menu from '~/lib/menuadmin';
const emit = defineEmits('setSidebar');
const route = useRoute();
const router = useRouter();
const dataIndex = ref('');
const isFull = ref(true);
const sameSub = ref(false);
const routeSplit = ref('');
const showSidebar = ref(true);
const menu = ref([])

const includeRoute = computed(() => {
  const x = route.fullPath.split('/');
  routeSplit.value = x[1];
  if (route.matched[0].path.includes(':')) {
    return route.matched[0].path;
  }
  return route.fullPath.replace(/[0-9]/g, '');
});

watch(route, (newValue, oldValue) => {
  setActive();
   if (window.innerWidth < 768) {
    showSidebar.value = false; // Hide the sidebar only on mobile when the route changes
  }
  
});

const setActive = () => {
  if (route.fullPath === includeRoute.value) {
    sameSub.value = true;
  } else {
    sameSub.value = false;
  }
};
setActive();

const getDetail = async() => {
  try {
    const res = await adminGet('/admin/detail');
    menu.value = res.data.data.menu;
  } catch (error) {
    console.log(error);
    
  }
};

const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value;
};

onMounted(() => {
  getDetail();
  window.addEventListener('resize', () => {
    if (window.innerWidth < 768) {
      isFull.value = false;
    } else {
      isFull.value = true;
    }
  });
});
</script>

<style>
.sidebar-full {
  width: 220px;
}

.sidebar-mini {
  width: 64px;
}

@media (max-width: 768px) {
  .sidebar-full, .sidebar-mini {
    width: 100%;
    height: auto;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1000;
  }

  .mobile-menu-button {
    display: block;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1100;
  }
}

@media (min-width: 769px) {
  .mobile-menu-button {
    display: none;
  }
}
</style>
