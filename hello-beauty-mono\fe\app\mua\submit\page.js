"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import ErrorSpan from "@/app/_component/errorSpan";
import { Icon } from "@iconify/react";
import { apiMua, apiMuaUpload } from "@/app/_helper/api-mua";
import { api } from "@/app/_helper/api";

export default function MuaRegister() {
  const [listLocation, setListLocation] = useState([]);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingLocation, setLoadingLocation] = useState(false);
  const [me, setMe] = useState({});
  const [payload, setPayload] = useState({});
  const [uploadProgress, setUploadProgress] = useState(0);

  const INITIAL_FORM_STATE = {
    name: "",
    profileName: "",
    serviceType: [],
    instagram: [],
    email: "",
    phone: "",
    address: "",
    location: "",
    linkHb: "",
    hasTraining: "",
    hasParticipated: "",
    isCertified: "",
    hasJob: "",
    hasCollaboration: "",
    jumlah_job: "",
    collaboration: "",
    lamaMua: ""
  };

  const [form, setForm] = useState(INITIAL_FORM_STATE);
  const [isOtherService, setIsOtherService] = useState(false);
  const [otherService, setOtherService] = useState('');
  const [certificates, setCertificates] = useState([]);
  const [trainings, setTrainings] = useState([]);
  const [errorCert, setErrorCert] = useState('');
  const [loadingUpload, setLoadingUpload] = useState(false);

  const getLocation = async () => {
    try {
      setLoadingLocation(true);
      const res = await api("GET", "/location");
      setLoadingLocation(false);
      setListLocation(res.data);
    } catch (error) {
      setLoadingLocation(false);
      console.log(error);
    }
  };

   useEffect(() => {
    const payload = {
      ...form,
      certificates,
      trainings
    };
    localStorage.setItem('mua-form', JSON.stringify(payload));
    setPayload(payload);
  }, [certificates, trainings, form, isOtherService, otherService]);

  const getMe = async () => {
    try {
      setLoading(true);
      const response = await apiMua("GET", "/me");
      setLoading(false);
      setMe(response.data);
      setForm((prevForm) => ({
        ...prevForm,
        email: response.data.email,
        name: response.data.name,
        phone: response.data.phone,
        
      }));
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    
    // check local storage
    const local = localStorage.getItem('mua-form')
    if(local) {
      const data = JSON.parse(local)
      setForm(data)
      setCertificates(data.certificates)
      setTrainings(data.trainings)
    }

    getLocation()
    getMe()

  }, []);

  const validateForm = () => {
    const errors = [];
    
    if (!form.name) errors.push('Nama lengkap harus diisi');
    if (!form.profileName) errors.push('Nama profile MUA harus diisi');
    if (!form.serviceType.length) errors.push('Pilih minimal satu jenis layanan');
    if (!form.instagram) errors.push('Akun Instagram harus diisi');
    if (!form.email) errors.push('Email harus diisi');
    if (!form.phone) errors.push('Nomor telepon harus diisi');
    if (!form.address) errors.push('Alamat harus diisi');
    if (!form.location) errors.push('Lokasi harus dipilih');
    if (!form.linkHb) errors.push('Link akun HB harus diisi');
    if (!form.lamaMua) errors.push('Lama menjadi MUA harus diisi');
    
    if (form.hasTraining === 'true' && !trainings.length) {
      errors.push('Upload minimal satu sertifikat training');
    }
    
    if (form.isCertified === 'true' && !certificates.length) {
      errors.push('Upload minimal satu sertifikat nasional');
    }
    
    if (form.hasJob === 'true' && !form.jumlah_job) {
      errors.push('Jumlah job harus diisi');
    }
    
    if (form.hasCollaboration === 'true' && !form.collaboration) {
      errors.push('Detail kolaborasi harus diisi');
    }

    return errors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setError(validationErrors.join(', '));
      return;
    }

    setLoading(true);
    try {
      const loc = listLocation.find((item) => item._id === form.location);
      const serviceType = isOtherService 
        ? [...new Set([...form.serviceType, otherService])]
        : [...new Set(form.serviceType)];

      const payload = {
        ...form,
        serviceType,
        locationid: form.location,
        locationName: loc ? loc.name : "",
        certificates,
        trainings
      };

      await apiMua("POST", "/approval", payload);
      localStorage.removeItem('mua-form');
      window.location.href = '/mua';
    } catch (error) {
      setError(error.message || 'Terjadi kesalahan saat mengirim data');
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setForm(prev => ({
        ...prev,
        [name]: checked 
          ? [...prev[name], value]
          : prev[name].filter(item => item !== value)
      }));
    } else {
      setForm(prev => ({ ...prev, [name]: value }));
    }
  };

  const arrayLayanan = [
    "Makeup",
    "Hairdo",
    "Face paint",
    "Model / Muse",
    "Attire / Wedding Gown / Wardrobe / Boutique",
    "Accessories (Crown, Hair Pieces, Siger Adat)"
  ]

  const handleFile = (e, label) => {
    const file = e.target.files[0]
    uploadFile(file, label)
    
  }

  const uploadFile = async (file, label) => {
    if (!file) return;

    try {
      const formData = new FormData();
      formData.append('file', file);
      
      setLoadingUpload(true);
      setErrorCert('Sedang mengupload...');
      setUploadProgress(0);

      const res = await apiMuaUpload('/upload', formData, {
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (res.data) {
        if (label === 'certificate') {
          setCertificates(prev => [...prev, res.data.fullUrl]);
        } else if (label === 'training') {
          setTrainings(prev => [...prev, res.data.fullUrl]);
        }
        
        // Reset file input
        const input = document.getElementById(label);
        if (input) input.value = '';
      }
    } catch (error) {
      setErrorCert(error.message || 'Gagal mengupload file');
    } finally {
      setLoadingUpload(false);
      setUploadProgress(0);
    }
  };

  const handleDelete = async (url, label) => {
    if (!url || !label) return;

    try {
      setLoadingUpload(true);
      setErrorCert('');
      
      await apiMua('POST', '/upload/delete', { url });
      
      if (label === 'certificate') {
        setCertificates(prev => prev.filter(item => item !== url));
      } else if (label === 'training') {
        setTrainings(prev => prev.filter(item => item !== url));
      }
    } catch (error) {
      setErrorCert(error.message || 'Gagal menghapus file');
    } finally {
      setLoadingUpload(false);
    }
  };

  useEffect(() => {
    return () => {
      // Cleanup function to remove any temporary data
      localStorage.removeItem('mua-form');
    };
  }, []);

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div className="h-14 flex items-center px-4">
        <Link href="/mua">
          <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link>
        <img src="/logo.png" alt="Logo" className="mx-auto h-8" />
      </div>

      <div className="mt-10">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Verifikasi <br /> Akun<span className="text-hb-pink">.</span>
        </h3>
      </div>

      <form onSubmit={handleSubmit} autoComplete="off">
        <div className="grid grid-cols-1 px-4 mt-12">
          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <Icon icon="hugeicons:user-circle-02" className="mr-2" />
            <input
              disabled={loading}
              required
              type="text"
              name="name"
              placeholder="Nama Lengkap"
              value={form.name}
              onChange={handleChange}
              className="w-full focus:outline-none"
            />
          </div>

          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <Icon icon="hugeicons:store-03" className="mr-2" />
            <input
              disabled={loading}
              required
              type="text"
              name="profileName"
              placeholder="Nama Profile MUA"
              value={form.profileName}
              onChange={handleChange}
              className="w-full focus:outline-none"
            />
          </div>
                  <div className="w-full p-3 grid grid-cols-1 border rounded-lg mb-4 group bg-white">
                    <label className="text-gray-500 text-sm">Jenis Layanan</label>
                    {
                      arrayLayanan.map((item, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            name="serviceType"
                            value={item}
                            checked={form.serviceType.includes(item)}
                            onChange={handleChange}
                          />
                          <label>{item}</label>
                        </div>
                      ))
                    }
                    <div className="flex items-center gap-2">
                      <input type="checkbox" name="serviceType" value="Lainnya" checked={isOtherService} onChange={(e) => {
                        if(e.target.checked) {
                          setIsOtherService(true)
                        } else {
                          setIsOtherService(false)
                          setOtherService('')
                        }
                      }}/>
                      <label>Lainnya</label>
                    </div>
                    {
                      isOtherService && (
                        <div className="flex items-center gap-2 mt-2">
                          <input
                            type="text"
                            name="otherServiceType"
                            placeholder="Masukkan Jenis Layanan"
                            value={otherService}
                            onChange={(e) => {
                              setOtherService(e.target.value)
                            }}
                            className="form-input w-full focus:outline-none"
                          />
                        </div>
                      )
                    }
                  </div>

          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <Icon icon="hugeicons:instagram" className="mr-2" />
            <input
              disabled={loading}
              required
              type="text"
              name="instagram"
              placeholder="Akun Instagram. Misal: @hellobeauty"
              value={form.instagram}
              onChange={handleChange}
              className="w-full focus:outline-none"
            />
          </div>

          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <Icon icon="hugeicons:mail-02" className="mr-2" />
            <input
              disabled={loading}
              required
              type="email"
              name="email"
              placeholder="Email"
              value={form.email}
              onChange={handleChange}
              className="w-full focus:outline-none"
            />
          </div>

          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <Icon icon="hugeicons:whatsapp" className="mr-2" />
            <input
              required
              type="text"
              inputMode="numeric"
              name="phone"
              placeholder="Nomor Whatsapp"
              value={form.phone}
              onChange={handleChange}
              className="w-full focus:outline-none"
            />
          </div>

          <div className="border-t py-2">
            <label className="text-gray-500 text-sm">Alamat</label>
          </div>
           <div className="w-full p-3 border rounded-lg mb-4 flex items-start group bg-white">
            <Icon icon="hugeicons:maping" className="mr-2 mt-1" />
            <textarea
              disabled={loading}
              required
              name="address"
              placeholder="Alamat"
              value={form.address}
              onChange={handleChange}
              className="w-full focus:outline-none"
            />
          </div>

          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <Icon icon="hugeicons:map-pinpoint-01" className="mr-2" />
            <select
              disabled={loading}
              required
              name="location"
              value={form.location}
              onChange={handleChange}
              className="w-full focus:outline-none"
            >
              <option value="" selected disabled>Pilih Lokasi</option>
              {listLocation.map((item) => (
                <option key={item._id} value={item._id}>
                  {item.name}
                </option>
              ))}
            </select>
          </div>

         

          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <Icon icon="hugeicons:job-link" className="mr-2" />
            <input
              disabled={loading}
              required
              type="text"
              name="linkHb"
              placeholder="Link akun HB sebelumnya"
              value={form.linkHb}
              onChange={handleChange}
              className="w-full focus:outline-none"
            />
          </div>
           <div className="w-full p-3 border rounded-lg  flex items-center group bg-white">
            <Icon icon="hugeicons:new-job" className="mr-2" />
            <input
              disabled={loading}
              required
              type="number"
              name="lamaMua"
              placeholder="Lama Menjadi MUA"
              value={form.lamaMua}
              onChange={handleChange}
              className="w-full focus:outline-none"
            />
           
          </div>
           <div className="text-xs text-gray-600 mb-4">
              * dalam tahun
            </div>

          <div className="space-y-6">
            {/* Experience Section */}
            <div className="bg-white p-6 rounded-lg border">
              <h4 className="text-lg font-medium mb-6">Pengalaman</h4>
              
              {/* Hello Beauty Event Participation */}
              <div className="mb-8 pb-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <label className="text-gray-700 text-base">Pernah ikut acara Hello Beauty?</label>
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'hasParticipated', value: 'true' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.hasParticipated === 'true' ? 'bg-hb-pink text-white border-hb-pink' : 'bg-white'
                      }`}
                    >
                      Ya
                    </button>
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'hasParticipated', value: 'false' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.hasParticipated === 'false' ? 'bg-gray-100 text-gray-700 border-gray-300' : 'bg-white'
                      }`}
                    >
                      Tidak
                    </button>
                  </div>
                </div>
              </div>

              {/* Training Experience */}
              <div className="mb-8 pb-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <label className="text-gray-700 text-base">Pernah ikut training / makeup class?</label>
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'hasTraining', value: 'true' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.hasTraining === 'true' ? 'bg-hb-pink text-white border-hb-pink' : 'bg-white'
                      }`}
                    >
                      Ya
                    </button>
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'hasTraining', value: 'false' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.hasTraining === 'false' ? 'bg-gray-100 text-gray-700 border-gray-300' : 'bg-white'
                      }`}
                    >
                      Tidak
                    </button>
                  </div>
                </div>
                {form.hasTraining === 'true' && (
                  <div className="mt-6 space-y-4">
                    <label className="block text-sm font-medium text-gray-700">Upload Sertifikat Training</label>
                    <div className="flex items-center space-x-3">
                      <input
                        type="file"
                        id="training"
                        accept="image/*, application/pdf"
                        onChange={(e) => handleFile(e, 'training')}
                        className="hidden"
                      />
                      <label
                        htmlFor="training"
                        className="flex-1 px-5 py-3 text-center border rounded-lg cursor-pointer hover:bg-gray-50"
                      >
                        Pilih File
                      </label>
                    </div>
                    {loadingUpload && (
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-hb-pink h-2.5 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                    )}
                    {trainings.length > 0 && (
                      <div className="space-y-3">
                        {trainings.map((url, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span className="text-sm text-gray-600">Sertifikat {index + 1}</span>
                            <div className="flex space-x-3">
                              <a
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1.5 px-3 py-1.5 text-sm text-hb-pink hover:text-hb-pink-dark border border-hb-pink rounded-lg hover:bg-hb-pink/5"
                              >
                                <Icon icon="uis:eye" className="text-lg" />
                                Lihat
                              </a>
                              <button
                                type="button"
                                onClick={() => handleDelete(url, 'training')}
                                className="flex items-center gap-1.5 px-3 py-1.5 text-sm text-red-500 hover:text-red-600 border border-red-200 rounded-lg hover:bg-red-50"
                              >
                                <Icon icon="uis:trash-alt" className="text-lg" />
                                Hapus
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Certification */}
              <div className="mb-8 pb-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <label className="text-gray-700 text-base">Apakah sudah bersertifikasi Nasional?</label>
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'isCertified', value: 'true' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.isCertified === 'true' ? 'bg-hb-pink text-white border-hb-pink' : 'bg-white'
                      }`}
                    >
                      Ya
                    </button>
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'isCertified', value: 'false' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.isCertified === 'false' ? 'bg-gray-100 text-gray-700 border-gray-300' : 'bg-white'
                      }`}
                    >
                      Tidak
                    </button>
                  </div>
                </div>
                {form.isCertified === 'true' && (
                  <div className="mt-6 space-y-4">
                    <label className="block text-sm font-medium text-gray-700">Upload Sertifikat Nasional</label>
                    <div className="flex items-center space-x-3">
                      <input
                        type="file"
                        id="certificate"
                        accept="image/*, application/pdf"
                        onChange={(e) => handleFile(e, 'certificate')}
                        className="hidden"
                      />
                      <label
                        htmlFor="certificate"
                        className="flex-1 px-5 py-3 text-center border rounded-lg cursor-pointer hover:bg-gray-50"
                      >
                        Pilih File
                      </label>
                    </div>
                    {loadingUpload && (
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-hb-pink h-2.5 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                    )}
                    {certificates.length > 0 && (
                      <div className="space-y-3">
                        {certificates.map((url, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span className="text-sm text-gray-600">Sertifikat {index + 1}</span>
                            <div className="flex space-x-3">
                              <a
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1.5 px-3 py-1.5 text-sm text-hb-pink hover:text-hb-pink-dark border border-hb-pink rounded-lg hover:bg-hb-pink/5"
                              >
                                <Icon icon="uis:eye" className="text-lg" />
                                Lihat
                              </a>
                              <button
                                type="button"
                                onClick={() => handleDelete(url, 'certificate')}
                                className="flex items-center gap-1.5 px-3 py-1.5 text-sm text-red-500 hover:text-red-600 border border-red-200 rounded-lg hover:bg-red-50"
                              >
                                <Icon icon="uis:trash-alt" className="text-lg" />
                                Hapus
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Hello Beauty Job Experience */}
              <div className="mb-8 pb-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <label className="text-gray-700 text-base">Pernah menerima job Hello Beauty?</label>
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'hasJob', value: 'true' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.hasJob === 'true' ? 'bg-hb-pink text-white border-hb-pink' : 'bg-white'
                      }`}
                    >
                      Ya
                    </button>
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'hasJob', value: 'false' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.hasJob === 'false' ? 'bg-gray-100 text-gray-700 border-gray-300' : 'bg-white'
                      }`}
                    >
                      Tidak
                    </button>
                  </div>
                </div>
                {form.hasJob === 'true' && (
                  <div className="mt-6">
                    <input
                      type="number"
                      name="jumlah_job"
                      value={form.jumlah_job}
                      onChange={handleChange}
                      placeholder="Berapa kali?"
                      className="w-full px-5 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-hb-pink"
                    />
                  </div>
                )}
              </div>

              {/* Collaboration Experience */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <label className="text-gray-700 text-base">Pernah menjadi partner / kolaborasi?</label>
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'hasCollaboration', value: 'true' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.hasCollaboration === 'true' ? 'bg-hb-pink text-white border-hb-pink' : 'bg-white'
                      }`}
                    >
                      Ya
                    </button>
                    <button
                      type="button"
                      onClick={() => handleChange({ target: { name: 'hasCollaboration', value: 'false' } })}
                      className={`px-5 py-2.5 rounded-lg border ${
                        form.hasCollaboration === 'false' ? 'bg-gray-100 text-gray-700 border-gray-300' : 'bg-white'
                      }`}
                    >
                      Tidak
                    </button>
                  </div>
                </div>
                {form.hasCollaboration === 'true' && (
                  <div className="mt-6">
                    <input
                      type="text"
                      name="collaboration"
                      value={form.collaboration}
                      onChange={handleChange}
                      placeholder="Kolaborasi di event apa?"
                      className="w-full px-5 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-hb-pink"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>

          {error && <ErrorSpan msg={error}/>}
          <button
            type="submit"
            className={`btn-primary ${
              loading ? "bg-gray-400" : "bg-hb-pink"
            }`}
            disabled={loading}
          >
            {loading ? "Loading..." : "Ajukan"}
          </button>

          <div className="h-14"/>
        </div>
      </form>
    </div>
  );
}
