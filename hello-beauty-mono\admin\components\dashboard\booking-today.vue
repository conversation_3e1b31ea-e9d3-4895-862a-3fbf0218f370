<template>
  <div class="border-t">
    <h1 class="text-sm font-semibold py-4">Booking Terdekat</h1>
    <div>
      <table class="w-full">
        <thead class="bg-gray-100">
          <tr>
            <th class="text-left p-2 text-xs">Order ID</th>
            <th class="text-left p-2 text-xs">Customer</th>
            <th class="text-left p-2 text-xs">Alamat</th>
            <th class="text-left p-2 text-xs">Waktu Booking</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="item in list"
            :key="item.id"
            class="border-b hover:bg-gray-100"
          >
            <td class="p-2 text-left text-xs">
              <nuxt-link
                :to="'/order/' + item.trxId"
                target="new_tab"
                class="hover:font-bold hover:text-primary"
              >
                {{ item.trxId }}
              </nuxt-link>
            </td>
            <td class="p-2 text-left text-xs font-semibold">
              <p>
                {{ item.name }}
              </p>
              <a
                :href="`https://wa.me/+62${item.phone}`"
                target="new_tab"
                class="text-xs flex items-center text-green-600"
              >
                <Icon name="ph:whatsapp-logo" />
                <p>
                  {{ item.phone }}
                </p>
              </a>
            </td>
            <td class="p-2 text-left text-xs">
              <p>
                {{ item.address }}
              </p>
              <p>
                {{ item.locationName }}
              </p>
            </td>
            <td class="p-2 text-left text-xs">
              {{ useMoment(item.bookingDate).format("DD MMM YYYY") }},
              {{ item.bookingTime }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>


<script setup>
const list = ref([]);
const date = ref(new Date());
const loading = ref(false);
const getList = async () => {
  loading.value = true;
  try {
    const { data } = await adminGet("/order/booking/nearest");
    list.value = data.data;
  } catch (error) {
    errorMsg.value = error.response.data.message;
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  getList();
});
</script>

