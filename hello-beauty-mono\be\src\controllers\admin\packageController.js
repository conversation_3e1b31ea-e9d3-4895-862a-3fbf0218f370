const package = require("../../models/package");
const listPackage = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const packages = await package.find().skip(skip).limit(limit);
    return res.status(200).json({
      message: "List paket",
      data: packages
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const detailPackage = async (req, res) => {
  try {
    const id = req.params.id;
    const packages = await package.findById(id);
    return res.status(200).json({
      message: "Detail paket",
      data: packages
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const createPackage = async (req, res) => {
  try {
    const body = req.body;
    const newpackage = await new package({
      name: body.name,
      description: body.description,
      isActive: body.isActive,
      items: body.items,
    });
    await newpackage.save();
    return res.status(201).json({ message: 'Berhasil menambahkan paket'})
    
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

const updatePackage = async (req, res) => {
  try {
    const body = req.body;
    const id = req.query.id;
    const updatepackage = await package.findByIdAndUpdate({"_id":id}, {
      name: body.name,
      description: body.description,
      isActive: body.isActive,
      items: body.items
    },{new: true});

    return res.status(201).json({ message: 'Berhasil mengubah paket'})
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

const deletePackage = async (req, res) => {
  try {
    const id = req.query.id;
    await package.findByIdAndDelete(id);
    return res.status(200).json({ message: 'Berhasil menghapus paket'})
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
}

module.exports = {
  createPackage,
  listPackage,
  updatePackage,
  deletePackage,
  detailPackage
}
