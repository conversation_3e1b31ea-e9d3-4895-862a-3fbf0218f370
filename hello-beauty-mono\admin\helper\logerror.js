import moment from 'moment';
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  defaultMeta: { service: 'user-service' },
  transports: [
    //
    // - Write all logs with importance level of `error` or less to `error.log`
    // - Write all logs with importance level of `info` or less to `combined.log`
    //
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});

export default function logError(resId, error) {
  logger.error(`[${resId}] - [${moment().format('DD-MMM-YYY,HH:mm:ss')}] - ${error}`);
  console.log(`[${resId}] - [${moment().format('DD-MMM-YYY,HH:mm:ss')}] - ${error}`);
}
