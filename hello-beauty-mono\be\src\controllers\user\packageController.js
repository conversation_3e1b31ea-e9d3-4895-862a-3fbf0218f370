const package = require('../../models/package')
const portofolio = require('../../models/portofolio')
const { logFunction, logError, logSuccess } = require('../../utils/logger')

const listPackage = async (req, res) => {
  const fnName = 'listPackage';
  logFunction(fnName);

  try {
    const packages = await package.find()
    
    logSuccess(fnName, { 
      totalPackages: packages.length
    });

    return res.status(200).json({
      message: "List paket",
      data: packages
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const portofolioPackage = async (req, res) => {
  const fnName = 'portofolioPackage';
  logFunction(fnName, { packageId: req.params.id });

  try {
    const { id } = req.params
    const portofolios = await portofolio.find({ 
      packageId: id,
      createdAt: { $gt: new Date('2024-12-01') }
    })
    const packageData = await package.findById(id)

    if (!packageData) {
      logFunction(fnName, { error: 'Package not found', packageId: id }, 'warn');
      return res.status(404).json({
        message: "Package not found"
      });
    }

    logSuccess(fnName, { 
      packageId: id,
      totalPortfolios: portofolios.length
    });

    return res.status(200).json({
      message: "List portofolio",
      data: {
        portofolios,
        package: packageData
      }
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const listPortofolio = async (req, res) => {
  const fnName = 'listPortofolio';
  logFunction(fnName);

  try {
    const portofolios = await portofolio.find().sort({ createdAt: -1 }).limit(10)

    logSuccess(fnName, {
      totalPortfolios: portofolios.length
    });

    return res.status(200).json({
      message: "List portofolio",
      data: portofolios
    })

  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const allPortofolio = async (req, res) => {
  const fnName = 'allPortofolio';
  logFunction(fnName);

  try {
    const portofolios = await portofolio.find().sort({ createdAt: -1 })

    logSuccess(fnName, {
      totalPortfolios: portofolios.length
    });

    return res.status(200).json({
      message: "All portofolio",
      data: portofolios
    })

  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

module.exports = {
  listPackage,
  portofolioPackage,
  listPortofolio,
  allPortofolio
}
