// import {Icon} from "@iconify/react";
"use client"
import { useEffect, useState } from "react";
const FormLogin = ({ onClose }) => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    // Untuk memulai animasi ketika komponen di-mount
    setShow(true);
  }, []);

  const handleClose = () => {
    // Untuk memulai animasi keluar sebelum benar-benar ditutup
    setShow(false);
    setTimeout(() => {
      onClose();
    }, 300); // Sesuaikan durasi dengan `duration-300` di bawah
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="bg-black bg-opacity-50 w-screen h-screen fixed inset-0 z-40 transition-opacity duration-300"
        onClick={handleClose}
      ></div>

      {/* Bottom Sheet Container */}
      <div
        className={`fixed bottom-0 left-0 w-full z-50 transform transition-transform duration-300 ${
          show ? "translate-y-0" : "translate-y-full"
        }`}
      >
        <div className="bg-white rounded-t-2xl p-6 shadow-lg">
          <h2 className="text-xl font-semibold mb-4">Login</h2>

          {/* Login Form */}
          <div className="mb-4">
            <input
              type="text"
              placeholder="Username"
              className="w-full p-3 rounded-md border border-gray-300 mb-2"
            />
            <input
              type="password"
              placeholder="Password"
              className="w-full p-3 rounded-md border border-gray-300 mb-4"
            />
            <button className="btn-primary">
              Login
            </button>
          </div>

          {/* Close Button */}
          <button
            onClick={handleClose}
            className="btn-secondary"
          >
            Cancel
          </button>
        </div>
      </div>
    </>
  );
};
export default FormLogin;
