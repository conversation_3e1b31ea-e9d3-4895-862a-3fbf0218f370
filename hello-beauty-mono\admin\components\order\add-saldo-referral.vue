<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">Tambahkan Saldo Referral</h2>
          </div>

          Apakah Anda yakin ingin menambahkan saldo referral ?

          <div class="h-4" />
          <div
            v-if="errorAdd"
            class="bg-red-100 text-red-600 text-xs p-3 rounded-lg my-3"
          >
            {{ errorAdd }}
          </div>
          <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);
const route = useRoute();
const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);
const errorAdd = ref("");
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
  },
  { deep: true }
);

const save = async () => {
  try {
    loading.value = true;
    errorAdd.value = "";
    const p = {
      trxId: props.data,
    };

    const res = await adminPost(`/referral/tambah-saldo`, p);
    loading.value = false;
    console.log(res);

    if (res && res.data) {
      $toast.success(res.data.message);
      emit("refresh");
    }
  } catch (error) {
    loading.value = false;
    console.log(error);

    if (error.response && error.response.data) {
      errorAdd.value = error.response.data.message;
    } else {
      errorAdd.value = "Terjadi kesalahan";
    }
  }
};
</script>
