const mongoose = require('mongoose');

const mutasiReferral = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'user'
    },
    amount: {
        type: Number,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    type: {
        type: String,
        required: true,
        enum: ['credit', 'debit']
    },
    adminId:{
        type: mongoose.Schema.Types.Mixed,
        default: "system"
    }
}, { timestamps: true });

module.exports = mongoose.model('mutasiReferral', mutasiReferral);
