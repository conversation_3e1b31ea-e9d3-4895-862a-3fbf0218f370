<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Customer</h1>
        <!-- <button
          class="btn-primary flex items-center"
          @click.prevent="showPanel = true"
        >
          <icon name="jam:plus" class="text-xl" />
          Tambah
        </button> -->
      </div>

      <form
        class="grid grid-cols-2 md:grid-cols-4 p-3 gap-2 mt-4"
        @submit.prevent="(page = 1), getData()"
      >
        <input
          v-model="search.search"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Cari..."
          type="text"
        />

        <div class="grid grid-cols-2 gap-2">
          <button class="btn-primary" type="submit">Cari</button>
          <button
            type="button"
            class="btn-secondary"
            @click.prevent="
              (search = {
                search: '',
              }),
                (page = 1),
                getData()
            "
          >
            Reset
          </button>
        </div>
      </form>

      <table class="w-full text-sm mt-4">
        <thead>
          <tr class="bg-gray-100 font-semibold">
            <td class="p-2 cursor-pointer" @click="sort('name')">
              <div class="flex items-center">
                <p>Nama</p>
                <span v-if="sortBy === 'name'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('email')">
              <div class="flex items-center">
                <p>Email</p>
                <span v-if="sortBy === 'email'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
              <p>Phone</p>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('point')">
              <div class="flex items-center">
                <p>Point</p>
                <span v-if="sortBy === 'point'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
              <p>Saldo Referral</p>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('createdAt')">
              <div class="flex items-center">
                <p>Waktu dibuat</p>
                <span v-if="sortBy === 'createdAt'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td />
          </tr>
        </thead>
        <tbody>
          <tr v-for="(d, i) in list" :key="i" class="hover:bg-gray-50 border-b">
            <td class="px-2 py-3">
              <p class="text-sm">
                {{ d.name }}
              </p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm text-gray-600">
                {{ d.email }}
              </p>
              <p class="text-xs">
                {{ d.phone }}
              </p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm text-gray-600">
                {{ d.point }}
              </p>
              <p class="text-xs">
                {{ useRupiah(d.saldoReferral) }}
              </p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm">
                {{ useMoment(d.createdAt).format("DD MMM, HH:mm") }}
              </p>
            </td>
            <td>
              <NuxtLink
                :to="`/customer/${d._id}`"
                class="btn-secondary text-xs"
              >
                <icon class="text-xs" name="ph:eye-bold" />
                Detail
              </NuxtLink>
              <button
                class="btn-danger text-xs ml-2"
                @click="showDeletePanel(d)"
              >
                <icon name="icon-park-twotone:delete" class="text-xs" /> Hapus
              </button>
            </td>
          </tr>
        </tbody>

        <tbody v-if="errorMsg && !loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              <icon class="text-2xl block" name="icon-park-twotone:data" />
              {{ errorMsg }}
            </td>
          </tr>
        </tbody>

        <!-- tbody loding -->
        <tbody v-if="loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              Memuat
              <icon class="text-2xl" name="svg-spinners:3-dots-bounce" />
            </td>
          </tr>
        </tbody>
      </table>

      <div class="p-3">
        <div class="flex justify-end gap-2 items-center mx-auto">
          <button
            v-if="page > 1"
            class="btn-secondary"
            @click.prevent="page--, page < 1 ? (page = 1) : '', getData()"
          >
            Prev
          </button>
          <form @submit="getData()">
            <input
              v-model="page"
              class="text-sm py-2 w-10 text-center border rounded-lg"
              type="number"
            />
          </form>

          <button class="btn-secondary" @click.prevent="page++, getData()">
            Next
          </button>
        </div>
      </div>
      <form-lokasi
        :data="selectedLokasi"
        :show="showPanel"
        @closed="(showPanel = false), (selectedLokasi = {})"
        @refresh="(showPanel = false), (selectedLokasi = {}), getData()"
      />

      <HapusCustomer
        :data="hapusId"
        :show="showHapus"
        @closed="
          showHapus = false;
          hapusId = null;
        "
        @refresh="
          showHapus = false;
          hapusId = null;
          getData();
        "
      />
    </div>
  </div>
</template>

<script setup>
import HapusCustomer from "~/components/customer/hapus-customer.vue";

definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Customer",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "Customer",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);
const search = ref({
  search: "",
  status: "",
});
const sortBy = ref("");
const sortOrder = ref("asc");

const sort = (column) => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === "asc" ? "desc" : "asc";
  } else {
    sortBy.value = column;
    sortOrder.value = "asc";
  }
  getData();
};

const hapusId = ref(null);
const showHapus = ref(false);
const selectedLokasi = ref({});
const showPanel = ref(false);

const showDeletePanel = (customer) => {
  hapusId.value = customer;
  showHapus.value = true;
};

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(
      `/user?page=${page.value}&limit=10&search=${search.value.search}&sort_by=${sortBy.value}&sort_order=${sortOrder.value}`
    );
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

onMounted(() => {
  getData();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
