const convertTime = (bookingDate, bookingTime) => { 

// <PERSON>uat objek Date dari bookingDate
const date = new Date(bookingDate);

// Pisahkan jam dan menit dari bookingTime
const [hours, minutes] = bookingTime.split(':');

// Set jam dan menit ke objek Date
date.setUTCHours(hours, minutes);

// <PERSON><PERSON> akhir
const bookingDateTime = date.toISOString();

console.log(bookingDateTime); // "2024-09-10T08:07:00.000Z"

}

export default convertTime;
