// Importing the files is enough to initialize their cron jobs
require('../controllers/bot/paymentBot');
require('./cleanInvalidData');
const cron = require('node-cron');
const cleanupLogs = require('./logCleanup');
const { logFunction, logError, logSuccess } = require('../utils/logger');

// Initialize all cron jobs
const initializeCronJobs = () => {
  const fnName = 'initializeCronJobs';
  logFunction(fnName);

  try {
    // Payment bot cron jobs are already scheduled in paymentBot.js
    // They run daily at 9:00 AM
    
    // Clean invalid data cron job runs daily at 1:00 AM
    // This is already scheduled in cleanInvalidData.js
    
    // Run log cleanup every day at midnight
    cron.schedule('0 0 * * *', () => {
      logFunction(fnName, { action: 'starting log cleanup' });
      cleanupLogs();
    });
    
    logSuccess(fnName, { message: 'All cron jobs initialized successfully' });
  } catch (error) {
    logError(fnName, error);
  }
};

module.exports = {
  initializeCronJobs
}; 
