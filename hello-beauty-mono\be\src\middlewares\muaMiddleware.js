const mua = require("../models/mua");
const { decodeJwtClient } = require("../helper/jwt");

const checkMuaVerification = async (req, res, next) => {
  try {
    const auth = req.headers.authorization;
    if (!auth) {
      return res.status(401).json({
        message: "Unauthorized",
      });
    }

    const u = await decodeJwtClient(auth);
    const searchMua = await mua.findById(u.id);
    
    if (!searchMua) {
      return res.status(404).json({
        message: "MUA not found",
      });
    }

    if (!searchMua.isApproved) {
      return res.status(403).json({
        message: "MUA belum diverifikasi",
      });
    }

    // Attach MUA data to request for use in controllers
    req.mua = searchMua;
    next();
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
};

module.exports = {
  checkMuaVerification,
}; 
