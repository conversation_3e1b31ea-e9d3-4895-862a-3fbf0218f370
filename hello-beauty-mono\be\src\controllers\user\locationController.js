const location = require('../../models/location')

const listLocation = async (req, res) => {
  try {
    const packages = await location.find()
    return res.status(200).json({
      message: "List lokasi",
      data: packages
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

module.exports = {
  listLocation
}
