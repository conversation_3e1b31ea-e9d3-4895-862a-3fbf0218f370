const { decodeJwtClient } = require("../helper/jwt");

const checkSession = async (req, res, next) => {
  try {
    const key = req.headers.authorization;
    const dec = await decodeJwtClient(key);
    if (dec) {
      req.user = dec; // Set the decoded user data to req.user
      await next();
    } else {
      await res.status(401).json({message:"Session not found"});
    }
  } catch (error) {
    console.log(error);
    await res.status(401).json({message:"Session not found"});
  }
};

module.exports = checkSession;
