const mutasiReferral = require("../models/mutasi-referral");
const transaction = require("../models/transaction");
const user = require("../models/user");

const addSaldoReferral = async (trxId, session = null) => {
  const fnName = "addSaldoReferral";
  
  console.log(`[${fnName}] trxId:`, trxId);

  try {
    console.log(`[${fnName}] Me<PERSON><PERSON> pengecekan transaksi`);
    // check trx
    const checkTrx = await transaction.findOne({
      trxId: trxId
    }).session(session);

    if (!checkTrx) {
      console.error(`[${fnName}] Transaksi tidak ditemukan`);
      return { status: "error", message: "Transaksi tidak ditemukan" };
    }

    console.log(`[${fnName}] Transaksi ditemukan`, checkTrx);

    if (checkTrx.statusPayment !== "PAID") {
      console.error(`[${fnName}] Transaksi belum lunas`);
      return { status: "error", message: "Transaksi belum lunas" };
    }

    console.log(`[${fnName}] Status pembayaran sudah lunas`);

    // check user
    console.log(`[${fnName}] Memulai pengecekan user dengan referralCode`, checkTrx.referralCode);
    const checkUser = await user.findOne({
      referralCode: checkTrx.referralCode
    }).session(session);

    if (!checkUser) {
      console.error(`[${fnName}] User tidak ditemukan`);
      return { status: "error", message: "User tidak ditemukan" };
    }

    console.log(`[${fnName}] User ditemukan`, checkUser._id);

    // check di mutasi referral
    console.log(`[${fnName}] Cek histori mutasi referral`);
    const checkHistory = await mutasiReferral.findOne({
      userId: checkUser._id,
      description: "Komisi referral dari transaksi " + checkTrx.trxId
    }).session(session);

    if (checkHistory) {
      console.error(`[${fnName}] Komisi referral sudah ditambahkan`);
      return { status: "error", message: "Komisi referral sudah ditambahkan" };
    }

    console.log(`[${fnName}] Belum ada histori komisi referral, lanjut proses tambah saldo`);

    // check saldo referral
    checkUser.saldoReferral += checkTrx.komisiReferral;
    await checkUser.save({ session });
    console.log(`[${fnName}] Saldo referral user berhasil diupdate`, checkUser.saldoReferral);

    // create history
    const createHistory = await mutasiReferral.create([
      {
        userId: checkUser._id,
        amount: checkTrx.komisiReferral,
        description: "Komisi referral dari transaksi " + checkTrx.trxId,
        type: "credit"
      }
    ], { session });

    if (!createHistory) {
      console.error(`[${fnName}] Gagal menambahkan saldo referral`);
      return { status: "error", message: "Gagal menambahkan saldo referral" };
    }

    console.log(`[${fnName}] Histori mutasi referral berhasil dibuat`, createHistory);
    console.log(`[${fnName}] Saldo referral berhasil ditambahkan`);
    return { status: "success", message: "Saldo referral berhasil ditambahkan" };

  } catch (error) {
    console.log(error);
    
    console.error(`[${fnName}] Error:`, error.message);
    return { status: "error", message: error.message };
  }
}

module.exports = addSaldoReferral;
