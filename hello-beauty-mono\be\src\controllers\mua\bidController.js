const transaction = require("../../models/transaction");
const bid = require("../../models/bid");
const mua = require("../../models/mua");
const { decodeJwtClient } = require("../../helper/jwt");
const listBid = async (req, res) => {
  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    
    // Check MUA verification status
    const searchMua = await mua.findById(u.id);
    if (!searchMua.isApproved) {
      return res.status(200).json({
        message: "List bid belum diverifikasi",
        data: [],
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    // if location is not provided, return all bid
    let bid
    if (!req.query.location || req.query.location==='undefined') {
      bid = await transaction
        .find({
          isOpen: true,
        })
        .skip(skip)
        .limit(limit)
        .sort({ _id: -1 });
    } else {
      bid = await transaction
        .find({
          isOpen: true,
          locationId: req.query.location,
        })
        .skip(skip)
        .limit(limit)
        .sort({ _id: -1 });
    }

    const remapBid = bid.map((item) => {
      return {
        trxId: item.trxId,
        address: item.address,
        locationName: item.locationName,
        packageName: item.packageName,
        packageItemName: item.packageItemName,
        pax: item.pax,
        isOpen: item.isOpen,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        bookingDate: item.bookingDate,
        bookingTime: item.bookingTime,
        note: item.note,
        muaShare: item.muaShare,
        status: item.status,
        customerName: item.customerName,
        customerPhone: item.customerPhone,
        totalPrice: item.totalPrice,
        paymentMethod: item.paymentMethod,
        paymentStatus: item.paymentStatus,
        locationId: item.locationId
      };
    });

    return res.status(200).json({
      message: "List bid",
      data: remapBid,
      pagination: {
        page,
        limit,
        total: await transaction.countDocuments({ isOpen: true })
      }
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
};
const checkBid = async (req, res) => {
  try {
    const trxId = req.params.id;
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const searchBid = await bid.find({ trxId: trxId, muaId: u.id });
    
    if (!searchBid.length) {
      return res.status(404).json({ message: "Bid not found" });
    }

    return res.status(200).json({ message: "Bid found" });
  } catch (e) {
    console.log(e);
    return res.status(500).json({ message: "Terjadi Kesalahan" });
  }
};


const myBid = async (req, res) => {
  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);

   const myBid = await bid.find({ muaId: u.id })
      .populate({
        path: 'idTrx', // Pastikan ini sesuai dengan field referensi di model bid
        model: 'transaction'   // Nama model transaksi yang Anda gunakan
      })
      .limit(10)
      .sort({ createdAt: -1 });

    return res.status(200).json({ message: "List bid", data: myBid });
  } catch (e) {
    console.log(e);
    
    return res.status(500).json({ message: "Terjadi Kesalahan" });
  }
};

const myBidSelected = async (req, res) => {
  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);

   const myBid = await bid.find({ muaId: u.id, selected: true })
      .populate({
        path: 'idTrx', // Pastikan ini sesuai dengan field referensi di model bid
        model: 'transaction'   // Nama model transaksi yang Anda gunakan
      })
      .limit(10)
      .sort({ createdAt: -1 });

    return res.status(200).json({ message: "List bid", data: myBid });
  } catch (e) {
    console.log(e);
    
    return res.status(500).json({ message: "Terjadi Kesalahan" });
  }
};


/**
 * Submits a bid for a transaction.
 *
 * @param {Object} req - The request object.
 * @param {Object} req.body - The request body.
 * @param {string} req.body.trxId - The transaction ID.
 * @param {string} req.body.note - The note for the bid.
 * @param {Object} req.headers - The request headers.
 * @param {string} req.headers.authorization - The authorization token.
 * @param {Object} res - The response object.
 * @returns {Promise<Object>} The response object with a status and message.
 */
const ajukanBid = async (req, res) => {
  try {
    const { trxId, note } = req.body;
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    console.log(u.id,'jwt nya');
    

    // check mua approval
    // const searchMua = await mua.findOne(u.id);
    const searchMua = await mua.findById(u.id);
    console.log(searchMua, "searchMua");
    
    if (!searchMua.isApproved) {
      return res.status(400).json({ message: "Akun belum diverifikasi" });
    }

    const searchTrx = await transaction.findOne({ trxId });
    if (!searchTrx) {
      return res.status(404).json({ message: "Bid not found" });
    }

    if (!note) {
      return res.status(400).json({ message: "Note is required" });
    }

    //   check in bid
    const searchBidBefore = await bid.findOne({ trxId, muaId: u.id });
    if (searchBidBefore) {
      return res.status(400).json({ message: "Bid sudah dilakukan" });
    }
    const newBid = new bid({
      muaId: u.id,
      muaName: u.name,
      trxId,
      idTrx: searchTrx._id,
      note,
    });

    await newBid.save();
    return res.status(201).json({ message: "Bid berhasil diajukan" });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};

module.exports = {
  listBid,
  ajukanBid,
  checkBid,
  myBid,
  myBidSelected
};
