<template>
  <div>
    <div v-if="!loading" class="p-3 grid md:grid-cols-1 grid-cols-1 gap-3">
      <div class="bg-white rounded-lg border relative w-full">
        <div class="p-4 flex items-center justify-between">
          <div
            class="flex items-center"
            @click.prevent="router.push('/customer')"
          >
            <icon
              class="cursor-pointer text-xl"
              name="bitcoin-icons:arrow-left-filled"
              @click="router.go(-1)"
            />
            <h1 class="font-bold ml-2">Detail Customer</h1>
          </div>
        </div>

        <div v-if="detail.name" class="grid grid-cols-2 p-4 gap-3">
          <div class="mb-2">
            <label class="block text-xs font-semibold"><PERSON><PERSON></label>
            <span>{{ detail.name }}</span>
          </div>

          <div class="mb-2">
            <label class="block text-xs font-semibold">Email</label>
            <span> {{ detail.email }} </span>
          </div>
          <div class="mb-2">
            <label class="block text-xs font-semibold">Whatsapp</label>
            <span>
              <a :href="'https://wa.me/' + detail.phone">
                <icon name="ri:whatsapp-fill" class="text-sm text-green-600" />

                {{ detail.phone }}</a
              >
            </span>
          </div>

          <div class="mb-2">
            <label class="block text-xs font-semibold">Point</label>
            <span>{{ detail.point }}</span>
          </div>
          <div class="mb-2">
            <label class="block text-xs font-semibold">Kode Referral</label>
            <span>{{ detail.referralCode }}</span>
          </div>
          <div class="mb-2">
            <label class="block text-xs font-semibold">Saldo Referral</label>
            <div>
              <span>{{ useRupiah(detail.saldoReferral) }}</span>
            </div>
          </div>

          <div class="mb-2 col-span-2"></div>

          <div class="col-span-2">
            <!-- Tabs -->
            <div class="flex border-b mb-4">
              <button
                class="px-4 py-2 focus:outline-none"
                :class="
                  activeTab === 'order'
                    ? 'border-b-2 border-primary font-bold text-primary'
                    : 'text-gray-500'
                "
                @click="activeTab = 'order'"
              >
                Riwayat Order
              </button>
              <button
                class="px-4 py-2 focus:outline-none"
                :class="
                  activeTab === 'mutasi'
                    ? 'border-b-2 border-primary font-bold text-primary'
                    : 'text-gray-500'
                "
                @click="activeTab = 'mutasi'"
              >
                Mutasi Referral
              </button>
            </div>
            <!-- Tab Content -->
            <div v-if="activeTab === 'mutasi'">
              <div class="flex justify-between items-center mb-2">
                <h1 class="text-lg font-semibold">Mutasi Referral</h1>
                <button
                  class="btn-primary text-xs"
                  @click="showTarikSaldo = true"
                >
                  Tarik Saldo Referral
                </button>
              </div>
              <mutasi-referral :list="listMutasi" />
            </div>
            <div v-else>
              <div class="flex justify-between items-center mb-2">
                <h1 class="text-lg font-semibold">Riwayat Order</h1>
              </div>
              <!-- Order Table -->
              <div>
                <table class="w-full text-sm mt-4">
                  <thead>
                    <tr class="bg-gray-100 font-semibold">
                      <td class="p-2">ID</td>
                      <td class="p-2">Alamat</td>
                      <td class="p-2">Booking</td>
                      <td class="p-2">Paket</td>
                      <td class="p-2">Total Order</td>
                      <td class="p-2">Bid</td>
                      <td class="p-2">Status</td>
                      <td class="p-2">Payment</td>
                      <td class="p-2">Rating</td>
                      <td class="p-2">Waktu dibuat</td>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(d, i) in orderList"
                      :key="i"
                      class="hover:bg-gray-50 border-b text-gray-600"
                    >
                      <td class="px-2 py-3">
                        <Nuxt-Link
                          :to="`/order/${d.trxId}`"
                          class="text-sm underline text-primary"
                        >
                          #{{ d.trxId }}
                        </Nuxt-Link>
                        <p class="text-sm">{{ d.name }}</p>
                        <p class="text-sm">{{ d.phone }}</p>
                      </td>
                      <td class="px-2 py-3">
                        <p class="text-sm w-20">{{ d.address }}</p>
                        <p class="text-sm w-32 font-semibold">
                          {{ d.locationName }}
                        </p>
                      </td>
                      <td class="px-2 py-3">
                        <p class="text-xs font-semibold w-28">
                          {{
                            useMoment(d.bookingDate).format("ddd,DD MMM YYYY")
                          }}
                        </p>
                        <p class="text-xs">
                          Pukul {{ d.bookingTime || "00:00" }}
                        </p>
                      </td>
                      <td class="px-2 py-3">
                        <p class="text-sm font-semibold">{{ d.packageName }}</p>
                        <p class="text-xs w-24 break-all">
                          {{ d.packageItemName }}
                        </p>
                      </td>
                      <td class="px-2 py-3">
                        <p class="text-sm">{{ d.pax || 1 }} pax</p>
                        <p class="text-xs">@{{ useRupiah(d.packagePrice) }}</p>
                        <p class="text-sm font-semibold">
                          {{ useRupiah(d.totalHarga) }}
                        </p>
                      </td>
                      <td>
                        <span
                          :class="
                            d.isOpen
                              ? 'bg-green-100 text-green-600'
                              : 'bg-red-100 text-red-600'
                          "
                          class="text-[10px] font-semibold uppercase px-2 py-1 rounded-full"
                        >
                          {{ d.isOpen ? "Open" : "Close" }}
                        </span>
                      </td>
                      <td class="px-2 py-3">
                        <status-order :status="d.status" />
                      </td>
                      <td class="px-2 py-3">
                        <status-payment :status="d.statusPayment" />
                      </td>
                      <td class="px-2 py-3">
                        <span>
                          <icon
                            v-for="i in 5"
                            :key="i"
                            name="solar:star-bold"
                            :class="
                              i <= d.rating
                                ? 'text-orange-500'
                                : 'text-gray-300'
                            "
                          />
                        </span>
                      </td>
                      <td class="px-2 py-3">
                        <p class="text-sm">
                          {{ useMoment(d.createdAt).format("DD MMM, HH:mm") }}
                        </p>
                      </td>
                    </tr>
                    <tr v-if="orderErrorMsg && !orderLoading">
                      <td
                        class="p-2 text-center text-gray-500 py-6"
                        colspan="10"
                      >
                        <icon
                          class="text-2xl block"
                          name="icon-park-twotone:data"
                        />
                        {{ orderErrorMsg }}
                      </td>
                    </tr>
                    <tr v-if="orderLoading">
                      <td
                        class="p-2 text-center text-gray-500 py-6"
                        colspan="10"
                      >
                        Memuat
                        <icon
                          class="text-2xl"
                          name="svg-spinners:3-dots-bounce"
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="p-3">
                  <div class="flex justify-end gap-2 items-center mx-auto">
                    <button
                      v-if="orderPage > 1"
                      class="btn-secondary"
                      @click.prevent="
                        orderPage--,
                          orderPage < 1 ? (orderPage = 1) : '',
                          getOrderList()
                      "
                    >
                      Prev
                    </button>
                    <form @submit.prevent="getOrderList()">
                      <input
                        v-model="orderPage"
                        class="text-sm py-2 w-10 text-center border rounded-lg"
                        type="number"
                      />
                    </form>
                    <button
                      class="btn-secondary"
                      @click.prevent="orderPage++, getOrderList()"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <tarik-saldo-referral
      :show="showTarikSaldo"
      :data="detail"
      @closed="closePanel"
      @refresh="closePanel, getData()"
    />
  </div>
</template>

<script setup>
const route = useRoute();
const router = useRouter();
const detail = ref({});
const loading = ref(false);
const errorMsg = ref("");
const showTarikSaldo = ref(false);
const activeTab = ref("");

// Order history tab state
const orderList = ref([]);
const orderLoading = ref(false);
const orderErrorMsg = ref("");
const orderPage = ref(1);

definePageMeta({
  middleware: "auth-admin",
});

const closePanel = () => {
  showTarikSaldo.value = false;
};

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    detail.value = [];
    const { data } = await adminGet(`/user/${route.params.id}`);
    loading.value = false;
    detail.value = data.data;
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

const getOrderList = async () => {
  try {
    orderLoading.value = true;
    orderErrorMsg.value = "";
    orderList.value = [];
    const { data } = await adminGet(
      `/order?page=${orderPage.value}&limit=30&search=${detail.value.phone}`
    );
    orderLoading.value = false;
    orderList.value = data.data;
    if (data.data.length === 0) {
      orderErrorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    orderLoading.value = false;
    orderErrorMsg.value = error.response.data.message;
  }
};
const loadingMutasi = ref(false);
const listMutasi = ref([]);
const getMutasiList = async () => {
  try {
    loadingMutasi.value = true;
    const { data } = await adminGet(
      `/referral/mutasi?userId=${detail.value._id}`
    );
    loadingMutasi.value = false;
    listMutasi.value = data.data;
  } catch (error) {
    loadingMutasi.value = false;
    // $toast.error(error.response.data.message);
  }
};

watch(activeTab, (val) => {
  if (val === "order") {
    getOrderList();
  } else {
    getMutasiList();
  }
});

onMounted(() => {
  // getPortofolio();
  // set title document
  document.title = "Detail Customer";
  getData();
  setTimeout(() => {
    activeTab.value = "order";
  }, 500);
});
</script>
