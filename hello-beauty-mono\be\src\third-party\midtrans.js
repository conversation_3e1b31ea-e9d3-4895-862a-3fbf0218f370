require('dotenv').config();
const endpoint = "https://app.midtrans.com"
const SERVER_KEY = process.env.MIDTRANS_SERVER_KEY
const AUTH_STRING = Buffer.from(SERVER_KEY).toString('base64')
const FE_URL = process.env.URL_FRONTEND

const createSnapToken = async (order) => {
  try {
    const url = endpoint+'/snap/v1/transactions';
    // format order
    // order = {
    //   user: {
    //     first_name: '<PERSON>',
    //     last_name: '<PERSON><PERSON>',
    //     email: '',
    //   },
    //   trx_id: '12345',
    //   pay_id: '12345',
    //   total_bayar: 100000,
    //   is_dp: false,
    //   product_name: 'Paket A',
    // }
    console.log(order, "ini order");
    
    const options = {
      method: 'POST',
      headers: {accept: 'application/json', 'content-type': 'application/json', authorization: `Basic ${AUTH_STRING}`
      },
      body: JSON.stringify({
        transaction_details: {order_id: order.pay_id, gross_amount: order.total_bayar, },
        credit_card: {secure: true},
        item_details: [
          {
            id: order.trx_id,
            price: order.total_belanja,
            quantity: 1,
            name: order.product_name
          },
          {
            id: "admin_fee",
            price: order.total_admin,
            quantity: 1,
            name: "Admin Fee"
          }
        ],
        enabled_payments: [order.payment],
        customer_details:{
            first_name: order.user.first_name,
            email: order.user.email,
            phone: order.user.phone
          },
           callbacks: {
            finish: FE_URL +'/pay/'+order.pay_id
          }
      })
    };
    
    const f = await fetch(url, options)
    const r = await f.json()
    return r

  } catch (error) {
    return error
  }
}

module.exports = {
  createSnapToken
}
