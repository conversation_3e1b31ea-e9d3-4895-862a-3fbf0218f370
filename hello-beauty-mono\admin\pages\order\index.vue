<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Order</h1>
        <nuxt-link to="/order/add" class="btn-primary flex items-center">
          <icon name="jam:plus" class="text-xl" />
          Tambah Order
        </nuxt-link>
      </div>
      <form
        class="grid grid-cols-2 md:grid-cols-4 p-3 gap-2 mt-4"
        @submit.prevent="(page = 1), getData()"
      >
        <input
          v-model="search.search"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Cari..."
          type="text"
        />
        <input
          v-model="search.bookingDate"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Booking Date"
          type="date"
        />
        <select
          v-model="search.locationId"
          class="p-2 border border-gray-200 rounded-lg"
        >
          <option value="">Lokasi</option>
          <option
            v-for="location in listLocation"
            :key="location._id"
            :value="location._id"
          >
            {{ location.name }}
          </option>
        </select>
        <select
          v-model="search.status"
          class="p-2 border border-gray-200 rounded-lg"
        >
          <option value="">Status Order</option>
          <option value="PENDING">Pending</option>
          <option value="FIND_MUA">Pemilihan MUA</option>
          <option value="SELECTED_MUA">MUA terpilih</option>
          <option value="ON_PROGRESS">On Progress</option>
          <option value="DONE">Selesai</option>
        </select>
        <select
          v-model="search.statusPayment"
          class="p-2 border border-gray-200 rounded-lg"
        >
          <option value="">Status Payment</option>
          <option value="DP_UNPAID">DP Belum Lunas</option>
          <option value="DP_PAID">DP Lunas</option>
          <option value="PAID">Lunas</option>
          <option value="FAILED">Gagal</option>
        </select>
        <select
          v-model="search.rating"
          class="p-2 border border-gray-200 rounded-lg"
        >
          <option value="">Semua Rating</option>
          <option value="1">Rating 1</option>
          <option value="2">Rating 2</option>
          <option value="3">Rating 3</option>
          <option value="4">Rating 4</option>
          <option value="5">Rating 5</option>
        </select>
        <div class="grid grid-cols-2 gap-2">
          <button class="btn-primary" type="submit">Cari</button>
          <button
            type="button"
            class="btn-secondary"
            @click.prevent="
              (search = {
                trxId: '',
                isOpen: '',
                search: '',
                bookingDate: '',
                locationId: '',
                statusPayment: '',
                status: '',
                rating: '',
              }),
                (page = 1),
                getData()
            "
          >
            Reset
          </button>
        </div>
      </form>

      <table class="w-full text-sm mt-4">
        <thead>
          <tr class="bg-gray-100 font-semibold">
            <td class="p-2 cursor-pointer" @click="sort('trxId')">
              <div class="flex items-center">
                <p>ID</p>
                <span v-if="sortBy === 'trxId'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('address')">
              <div class="flex items-center">
                <p>Alamat</p>
                <span v-if="sortBy === 'address'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('bookingDate')">
              <div class="flex items-center">
                <p>Booking</p>
                <span v-if="sortBy === 'bookingDate'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('packageName')">
              <div class="flex items-center">
                <p>Paket</p>
                <span v-if="sortBy === 'packageName'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('totalHarga')">
              <div class="flex items-center">
                <p>Total Order</p>
                <span v-if="sortBy === 'totalHarga'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="cursor-pointer" @click="sort('isOpen')">
              <div class="flex items-center">
                <p>Bid</p>
                <span v-if="sortBy === 'isOpen'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="cursor-pointer" @click="sort('status')">
              <div class="flex items-center">
                <p>Status</p>
                <span v-if="sortBy === 'status'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="cursor-pointer" @click="sort('statusPayment')">
              <div class="flex items-center">
                <p>Payment</p>
                <span v-if="sortBy === 'statusPayment'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="cursor-pointer" @click="sort('rating')">
              <div class="flex items-center">
                <p>Rating</p>
                <span v-if="sortBy === 'rating'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('createdAt')">
              <div class="flex items-center">
                <p>Waktu dibuat</p>
                <span v-if="sortBy === 'createdAt'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <!-- <td /> -->
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(d, i) in list"
            :key="i"
            class="hover:bg-gray-50 border-b text-gray-600"
          >
            <td class="px-2 py-3">
              <Nuxt-Link
                :to="`/order/${d.trxId}`"
                class="text-sm underline text-primary"
              >
                #{{ d.trxId }}
              </Nuxt-Link>
              <p class="text-sm">
                {{ d.name }}
              </p>
              <p class="text-sm">
                {{ d.phone }}
              </p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm w-20">
                {{ d.address }}
              </p>
              <p class="text-sm w-32 font-semibold">
                {{ d.locationName }}
              </p>
            </td>
            <td class="px-2 py-3">
              <p class="text-xs font-semibold w-28">
                {{ useMoment(d.bookingDate).format("ddd,DD MMM YYYY") }}
              </p>
              <p class="text-xs">Pukul {{ d.bookingTime || "00:00" }}</p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm font-semibold">
                {{ d.packageName }}
              </p>
              <p class="text-xs w-24 break-all">
                {{ d.packageItemName }}
              </p>
            </td>

            <td class="px-2 py-3">
              <p class="text-sm">{{ d.pax || 1 }} pax</p>
              <p class="text-xs">@{{ useRupiah(d.packagePrice) }}</p>
              <p class="text-sm font-semibold">
                {{ useRupiah(d.totalHarga) }}
              </p>
            </td>
            <td>
              <span
                :class="
                  d.isOpen
                    ? 'bg-green-100 text-green-600'
                    : 'bg-red-100 text-red-600'
                "
                class="text-[10px] font-semibold uppercase px-2 py-1 rounded-full"
              >
                {{ d.isOpen ? "Open" : "Close" }}
              </span>
            </td>

            <td class="px-2 py-3">
              <status-order :status="d.status" />
            </td>
            <td class="px-2 py-3">
              <status-payment :status="d.statusPayment" />
            </td>
            <td class="px-2 py-3">
              <span>
                <icon
                  v-for="i in 5"
                  :key="i"
                  name="solar:star-bold"
                  :class="i <= d.rating ? 'text-orange-500' : 'text-gray-300'"
                />
              </span>
            </td>

            <td class="px-2 py-3">
              <p class="text-sm">
                {{ useMoment(d.createdAt).format("DD MMM, HH:mm") }}
              </p>
            </td>
            <!-- <td>
              <div class="flex gap-2">
                <NuxtLink
                  :to="`/order/${d.trxId}`"
                  class="btn-secondary text-xs"
                >
                  <icon class="text-xs" name="ph:eye-bold" />
                  Detail
                </NuxtLink>
              </div>
            </td> -->
          </tr>
        </tbody>

        <tbody v-if="errorMsg && !loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              <icon class="text-2xl block" name="icon-park-twotone:data" />
              {{ errorMsg }}
            </td>
          </tr>
        </tbody>

        <!-- tbody loding -->
        <tbody v-if="loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              Memuat
              <icon class="text-2xl" name="svg-spinners:3-dots-bounce" />
            </td>
          </tr>
        </tbody>
      </table>

      <div class="p-3">
        <div class="flex justify-end gap-2 items-center mx-auto">
          <button
            v-if="page > 1"
            class="btn-secondary"
            @click.prevent="page--, page < 1 ? (page = 1) : '', getData()"
          >
            Prev
          </button>
          <form @submit="getData()">
            <input
              v-model="page"
              class="text-sm py-2 w-10 text-center border rounded-lg"
              type="number"
            />
          </form>

          <button class="btn-secondary" @click.prevent="page++, getData()">
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Order",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "Order",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);
const search = ref({
  trxId: "",
  isOpen: "",
  search: "",
  bookingDate: "",
  locationId: "",
  statusPayment: "",
  status: "",
  rating: "",
});
const sortBy = ref("");
const sortOrder = ref("asc");

const sort = (column) => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === "asc" ? "desc" : "asc";
  } else {
    sortBy.value = column;
    sortOrder.value = "asc";
  }
  getData();
};

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(
      `/order?page=${page.value}&limit=30&trxId=${search.value.trxId}&isOpen=${search.value.isOpen}&search=${search.value.search}&bookingDate=${search.value.bookingDate}&locationId=${search.value.locationId}&statusPayment=${search.value.statusPayment}&status=${search.value.status}&sort_by=${sortBy.value}&sort_order=${sortOrder.value}&rating=${search.value.rating}`
    );
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

const listLocation = ref([]);
const loadingLocation = ref(false);

const getLocation = async () => {
  try {
    loadingLocation.value = true;
    const { data } = await adminGet(`/location?page=1&limit=999`);
    loadingLocation.value = false;
    listLocation.value = data.data;
  } catch (error) {
    loadingLocation.value = false;
  }
};

onMounted(() => {
  getData();
  getLocation();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
