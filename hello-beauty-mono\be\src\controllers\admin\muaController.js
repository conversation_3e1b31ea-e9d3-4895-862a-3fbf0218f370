const mua = require("../../models/mua");
const portofolioMua = require('../../models/portofolio-mua');
const location = require('../../models/location');
const approval = require('../../models/approval');
const fs = require('fs');
const bcrypt = require('bcrypt');
const package = require("../../models/package");

const listMua = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search || '';
    const isApproved = req.query.isApproved;

    const query = {
      $or: [
        { name: { $regex: search, $options: 'i' } },
        { muaName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ]
    };

    if (isApproved !== undefined && isApproved !== '') {
      query.isApproved = isApproved === 'true';
    }

    // dynamic sorting
    const sortBy = req.query.sort_by || 'createdAt';
    const sortOrder = req.query.sort_order === 'desc' ? -1 : 1;

    let muas = await mua.find(query).skip(skip).limit(limit).sort({ [sortBy]: sortOrder });
    // delete password
    muas = muas.map(mua => {
      mua.password = undefined;
      return mua;
    });
  
    return res.status(200).json({
      message: "List MUA",
      data: muas
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

// approve or reject MUA
const actionMua = async (req, res) => {
  try {
    const body = req.body;
    const email = req.query.email;
    await mua.findByIdAndUpdate({"email":email}, {
      isApproved: body.isApproved
    },{new: true});
    return res.status(201).json({ message: 'Berhasil mengubah MUA'})
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

// detail MUA
const detailMua = async (req, res) => {
  try {
    const id = req.params.id;
    let muaData = await mua.findOne({
      _id: id
    });

    muaData = muaData.toObject();
    delete muaData.password
    
    return res.status(200).json({
      message: "Detail MUA",
      data: muaData
    });
  }
  catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
}


const changePassword = async (req, res) => {
  try {
    const { muaId, password } = req.body;

    const encryptedPassword = await bcrypt.hash(password, 10); // Fixed bcrypt usage

    const updatedMua = await mua.findByIdAndUpdate(
      muaId, // Correctly use muaId as the identifier
      { password: encryptedPassword }, // Only update the password
      { new: true }
    );

    if (!updatedMua) {
      return res.status(404).json({
        message: "MUA not found"
      });
    }

    return res.status(200).json({
      message: "Password updated successfully",
      data: updatedMua
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
}

const detailPortofolio = async (req, res) => {
  try {
    const id = req.params.id;
    const portofolioData = await portofolioMua.find({
      muaId: id
    });
    return res.status(200).json({
      message: "Detail Portofolio",
      data: portofolioData
    });
  }
  catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
}

const editRating = async (req, res) => {
  try {
    const { rating, muaId } = req.body;
    const updatedMua = await mua.findByIdAndUpdate(muaId, { rating }, { new: true });
    return res.status(200).json({
      message: "Rating updated successfully",
      data: updatedMua
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
}

const editMua = async (req, res) => {
  try {
    const body = req.body;
    const muaId = req.params.id;

    if (!body.locationId) {
      return res.status(400).json({
        message: "Location is required"
      });
    }

    // cek lokasi
    const locationCheck = await location.findOne({
      _id: body.locationId
    });

    // check package
    const packageCheck = await package.findOne({
      _id: body.packageId
    });

    if (!packageCheck) {
      return res.status(400).json({
        message: "Package not found"
      });
    }

    if (!locationCheck) {
      return res.status(400).json({
        message: "Location not found"
      });
    }

    // Fetch existing MUA data
    const existingMua = await mua.findById(muaId);
    if (!existingMua) {
      return res.status(400).json({
        message: "Mua not found"
      });
    }

    // Update only provided fields
    const dataUpdateMua = {
      ...existingMua.toObject(),
      name: body.name || existingMua.name,
      muaName: body.mua_name || existingMua.muaName,
      email: body.email || existingMua.email,
      phone: body.phone || existingMua.phone,
      approval: {
        ...existingMua.approval,
        name: body.name || existingMua.approval?.name,
        profileName: body.mua_name || existingMua.approval?.profileName,
        serviceType: body.serviceType || existingMua.approval?.serviceType,
        email: body.email || existingMua.approval?.email,
        phone: body.phone || existingMua.approval?.phone,
        instagram: body.instagram || existingMua.approval?.instagram,
        address: body.address || existingMua.approval?.address,
        locationId: body.locationId || existingMua.approval?.locationId,
        locationName: locationCheck.name || existingMua.approval?.locationName,
        linkHb: body.linkHb || existingMua.approval?.linkHb,
        lamaMua: body.lamaMua || existingMua.approval?.lamaMua
      }
    };

    const updatedMua = await mua.findByIdAndUpdate(muaId, dataUpdateMua, { new: true });

    if (updatedMua) {
      return res.status(200).json({
        message: "Mua updated successfully",
      });
    } else {
      return res.status(400).json({
        message: "Mua not found"
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

const editApprovalMua = async (req, res) => {
  try {
    const { muaId, isApproved } = req.body;
    const updatedMua = await mua.findByIdAndUpdate(muaId, { isApproved }, { new: true });
    return res.status(200).json({
      message: "Approval status updated successfully",
      data: updatedMua
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
}

const deleteMua = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find and delete the MUA
    const deletedMua = await mua.findByIdAndDelete(id);
    
    if (!deletedMua) {
      return res.status(404).json({
        message: "MUA tidak ditemukan"
      });
    }

    // Delete MUA's portfolio
    await portofolioMua.deleteMany({
      muaId: id
    });

    // Delete approval
    await approval.deleteMany({
      muaId: id
    });

    return res.status(200).json({
      message: "MUA berhasil dihapus"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: "Terjadi kesalahan pada server"
    });
  }
};


const hapusPortofolioMua = async (req, res) => {
  try {
    const id = req.params.id;
    const portofolioData = await portofolioMua.findById(id);
    if (!portofolioData) {
      return res.status(400).json({
        message: "Portofolio not found"
      });
    }
    // delete file by filePath
    const path = "./src/public"+portofolioData.filePath;

    // abaikan error jika file tidak ditemukan
    if (fs.existsSync(path)) {
      fs.unlinkSync(path);
    }
    await portofolioMua.findByIdAndDelete(id);

    return res.status(200).json({
      message: "Portofolio deleted successfully",
    });
  }
  catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
}



module.exports = {
  listMua,
  actionMua,
  detailMua,
  detailPortofolio,
  editRating,
  editMua,
  hapusPortofolioMua,
  changePassword,
  editApprovalMua,
  deleteMua
}
