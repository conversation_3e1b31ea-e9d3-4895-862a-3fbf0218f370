<template>
  <div class="bg-white">
    <div
      v-if="$nuxt.isOffline"
      class="fixed top-0 w-full text-white text-center py-2 text-xs"
    >
      <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.
    </div>
    <div class="flex">
      <div>
        <sidebar-admin class="md:block" />
      </div>
      <div
        class="w-full mx-auto transition-all duration-500 ease-in-out bg-slate-50 min-h-screen ml-[220px]"
      >
        <NuxtPage />
      </div>
    </div>
  </div>
</template>
<script setup>
const isFull = ref(false);

onMounted(() => {
  window.addEventListener('resize', () => {
    if (window.innerWidth < 768) {
      isFull.value = false;
    } else {
      isFull.value = true;
    }
  });
});
</script>
<style>
html,
body {
  background: white;
}

body::-webkit-scrollbar,
.scroll__::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

body::-webkit-scrollbar-track,
.scroll__::-webkit-scrollbar-track {
  background: #c8d0d4;
}

body::-webkit-scrollbar-thumb,
.scroll__::-webkit-scrollbar-thumb {
  background-color: #00345e;
  border-radius: 20px;
  border: 3px solid #00345e;
}

.page-enter-active {
  transition: all 0.2s ease-in;
}

.page-leave-active {
  transition: all 0.2s ease-out;
}

.page-enter-from, .page-leave-to
  /* .slide-fade-leave-active below version 2.1.8 */ {
  transform: translateY(10px);
  opacity: 0;
}

.select2 {
  width: 100% !important;
}
</style>
