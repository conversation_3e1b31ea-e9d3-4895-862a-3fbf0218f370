"use client"
import Link from 'next/link'
import {useState, useEffect} from 'react'
import {api} from '../../_helper/api'
import ErrorSpan from '../../_component/errorSpan'
import { Icon } from '@iconify/react'

export default function EditProfile() {
  const [email, setEmail] = useState('')
  const [name, setName] = useState('')
  const [phone, setPhone] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [loadingMe, setLoadingMe] = useState(true)

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const res = await api('GET', '/me');
        const user = res.data; // Ensure correct data structure
        setName(user.name);
        setEmail(user.email);
        setPhone(user.phone);
      } catch (error) {
        setError('Failed to fetch user data');
      } finally {
        setLoadingMe(false);
      }
    };
    fetchUserData();
  }, []);

  const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
  }

  const validatePhone = (phone) => {
    const re = /^[0-9]+$/;
    return re.test(String(phone));
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!name || !email || !phone) {
      setError('Semua Form Wajib di isi');
      return;
    }
    if (!validateEmail(email)) {
      setError('Format Email Salah');
      return;
    }
    if (!validatePhone(phone)) {
      setError('Format Whatsapp Salah');
      return;
    }
    try {
      setError('')
      setLoading(true)
      const res = await api('PUT', '/me', {name, email, phone})
      setLoading(false)
      window.location.href = '/profile';
    } catch (error) {
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div className="h-14 flex items-center px-4">
        <Link href="/profile">
        <img src="/icons/arrow-left.svg" alt="Back" className="" />
        </Link>
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="mt-28">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Edit Profile<span className="text-hb-pink">.</span></h3>
      </div>

      <form className="" onSubmit={handleSubmit}>
      <div className="grid grid-cols-1  px-4 mt-20">
        <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/sms.svg" alt="Name" className="mr-2" />
          <input disabled={loading || loadingMe} required type="text" placeholder="Nama Lengkap" value={name} onChange={(e) => setName(e.target.value)} className="w-full  focus:outline-none"  />
        </div>
        <div  className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/form-nama.svg" alt="Email" className="mr-2" />
          <input disabled={loading || loadingMe} required type="email" placeholder="Email " value={email} onChange={(e) => setEmail(e.target.value)}  className="w-full  focus:outline-none"  />
        </div>

         <div  className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/call.svg" alt="Phone" className="mr-2" />
          <input disabled={loading || loadingMe} required type="text" inputmode="numeric" placeholder="Nomor Whatsapp" value={phone} onChange={(e) => setPhone(e.target.value)}  className="w-full  focus:outline-none"  />
        </div>
      </div>
      <div className="px-4">
        {
          error && <ErrorSpan msg={error} />
        }

      </div>
      <div className=" px-6">
        <button type="submit"  className="btn-primary flex items-center justify-center">
          {
            (loading || loadingMe) && <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin mr-2" />
          }
          Simpan
        </button>
        </div>
      </form>

        <div className="h-40"></div>
      </div>
  );
}
