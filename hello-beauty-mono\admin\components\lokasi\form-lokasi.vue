<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">
              {{ payload._id ? 'Edit' : 'Tambah' }} Lokasi
            </h2>
          </div>
          <div class="grid grid-cols-2 gap-4 !text-sm">
            <div class="col-span-2">
              <label class="text-sm font-semibold block"><PERSON><PERSON></label>
              <input
                v-model="payload.name"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div class="col-span-2">
              <label class="text-sm font-semibold block">Status</label>

              <select
                v-model="payload.is_active"
                required
                :disabled="loading"
                class="form-input text-sm"
              >
                <option value="" disabled selected>Pilih Status</option>
                <option :value="true">Aktif</option>
                <option :value="false">Non Aktif</option>
              </select>
            </div>
          </div>

          <div class="h-4" />
          <div class="flex gap-2">
            <button
              class="btn-primary text-sm flex items-center gap-1"
              :disabled="loading"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? 'Menyimpan' : 'Simpan' }}
              </span>
            </button>
            <button
              class="btn-secondary text-sm flex items-center gap-1"
              :disabled="loading"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(['closed', 'refresh']);

const props = defineProps({
  show: Boolean,
  data: Object
});

const showPanel = ref(false);

const payload = ref({
  is_active: ''
});
const loading = ref(false);

watch(
  () => props.show,
  val => {
    showPanel.value = !!val;

    if (props.data) {
      payload.value = { ...props.data };
      payload.value.is_active = props.data.isActive;
    }
  },
  { deep: true }
);

const save = async () => {
  try {
    loading.value = true;
    const p = {
      name: payload.value.name,
      is_active: payload.value.is_active
    };

    let res;
    if (props.data._id) {
      res = await adminPut(`/location?id=${props.data._id}`, p);
    } else {
      res = await adminPost('/location', p);
    }
    loading.value = false;

    $toast.success(res.data.message);
    emit('refresh');
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
