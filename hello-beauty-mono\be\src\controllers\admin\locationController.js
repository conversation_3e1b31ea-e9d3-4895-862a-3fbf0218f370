const location = require("../../models/location");

const listLocation = async (req, res) => {
  try {
    // with pagination, page and limit
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const locations = await location.find().skip(skip).limit(limit);
    return res.status(200).json({ message: "List lokasi", data: locations });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "Internal server error" });
  }
};

const createLocation = async (req, res) => {
  try {
    const body = req.body;
    const newLocation = await new location({
      name: body.name,
      isActive: body.is_active,
    });
    await newLocation.save();
    return res.status(201).json({ message: 'Berhasil menambahkan lokasi'})

  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

const updateLocation = async (req, res) => {
  try {
    const body = req.body;
    const id = req.query.id;
    console.log(id);
    
    await location.findOneAndUpdate(
      {"_id":id},
      {
        name: body.name,
        isActive: body.is_active,
      },
      {new: true});

    return res.status(201).json({ message: 'Berhasil mengubah lokasi'})

  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

const deleteLocation = async (req, res) => {
  try {
    const id = req.query.id;
    await location.findByIdAndDelete(id);
    return res.status(200).json({ message: 'Berhasil menghapus lokasi'})
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

module.exports = {
  createLocation,
  listLocation,
  updateLocation,
  deleteLocation
}
