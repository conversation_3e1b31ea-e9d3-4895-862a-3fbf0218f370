"use client";
import LoadingFull from "@/app/_component/loadingFull";
import convertRp from "@/app/_helper/convertorp";
import { Icon } from "@iconify/react";
import Link from "next/link";

export default function PaymentSuccess() {
 
  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div>
        <div className="h-14 flex items-center px-4">
          <Link href="/">
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
          <img src="/logo.png" alt="Logo" className="mx-auto h-8" />
        </div>

        <div className="mt-12">
          <h3 className="text-2xl font-semibold text-center mb-6">
            Pembayaran<br/>Sukses<span className="text-hb-pink">.</span>
          </h3>

          <Icon icon="solar:check-circle-bold-duotone" className="text-[160px] mx-auto text-green-500" />
        </div>
      </div>
    </div>
  );
}
