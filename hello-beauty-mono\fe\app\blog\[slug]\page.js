"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import decodeHTMLEntities from "@/app/_helper/decodeHtml";
import Link from "next/link";
import LoadingFull from "@/app/_component/loadingFull";

export default function DetailBlog() {
  const [article, setArticle] = useState([]);
  const [loading, setLoading] = useState(true);
  const params = useParams();

  const getArticle = async () => {
    try {
      setLoading(true);
      const slug = params.slug;
      const res = await fetch(
        "/api/wp/post/" + slug
      );
      setLoading(false);
      const data = await res.json();
      if (data.length > 0) {
        const a = {
          title: data[0].title.rendered,
          content: data[0].content.rendered,
          category: data[0]._embedded["wp:term"][0][0].name,
        };
        setArticle(a);
      }
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    getArticle();
  }, []);
  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4  max-w-[480px]">
          <button
            onClick={() => {
              window.history.back();
            }}
          >
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </button>
          <Link href="/" className="mx-auto">
            <img src="/logo.png" alt="Logo" className="h-8" />
          </Link>
        </div>
        <div>
          <h3 className="text-2xl font-semibold text-center mb-6"></h3>
          {loading ? (
            <div>
              <LoadingFull />
            </div>
          ) : (
            <div className="p-5">
              <div className="uppercase text-sm tracking-[.2rem] text-center">
                {decodeHTMLEntities(article.category)}
              </div>
              <h1 className="text-3xl mt-4 font-bold text-center">
                {article.title}
              </h1>
              <div className="mt-6">
                <div
                  dangerouslySetInnerHTML={{ __html: article.content }}
                  className="text-sm leading-6"
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
