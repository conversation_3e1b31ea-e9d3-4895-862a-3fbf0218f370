<template>
  <div>
    <div class="form-input h-10 relative">
      <input
        :id="id"
        v-model="selectedFile"
        placeholder="Pilih File"
        class="outline-none w-full"
      />
      <button
        type="button"
        class="h-10 border absolute rounded-r-lg top-0 right-0 bg-primary px-8 text-white"
        @click="showPanel = true"
      >
        Pilih
      </button>
    </div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <div class="p-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">Media Manager</h2>
          </div>

          <div class="grid grid-cols-3 gap-2 my-4">
            <div v-for="(p, i) in preview" :key="i" class="border relative p-4">
              <img :src="p" alt="" class="h-14 object-contain mx-auto" />
              <div
                class="absolute top-0 left-0 w-full h-full bg-black text-white flex items-center justify-center text-xs opacity-40"
              >
                Uploading....
              </div>
            </div>
          </div>
          <loader-full v-if="loading" />
          <div class="w-full">
            <input
              v-model="search"
              placeholder="Cari file...."
              class="border outline-none focus:border-black rounded-md w-full p-2 mb-2"
            />
            <div class="grid grid-cols-3 gap-2">
              <label
                class="w-full min-h-32 border-2 border-dashed rounded-xl relative flex items-center justify-center"
                for="inputFile"
              >
                <div class="text-center">
                  <icon
                    name="jam:upload"
                    class="text-2xl text-gray-500 mx-auto"
                  />
                  <p class="text-gray-500 text-sm">Upload File</p>
                </div>
                <input
                  id="inputFile"
                  :disabled="loading"
                  accept="image/*"
                  class="hidden"
                  multiple
                  required
                  type="file"
                  @change.prevent="setFile"
                />
              </label>
              <div
                v-for="c in filteredFile"
                :key="c.id"
                class="relative border rounded-lg"
                @click.prevent="toFile(c.url)"
              >
                <img
                  :src="c.url"
                  class="h-28 object-contain mx-auto"
                  :alt="c.filename"
                />
                <div class="text-[10px] rounded-md px-2 text-gray-500">
                  {{ moment(c.created_at).format('DD MMM YYYY, HH:mm:ss') }}
                </div>
                <div
                  class="text-[10px] font-bold rounded-md px-2 text-gray-800"
                >
                  {{ c.name }}
                </div>
              </div>

              <div
                v-if="search.length > 2 && !filteredFile.length"
                class="md:col-span-6 col-span-3 text-gray-500"
              >
                Belum ada file
              </div>
              <!-- <div v-if="">

              </div> -->
            </div>
          </div>

          <div
            v-show="textToast"
            id="toast"
            class="fixed top-0 left-0 w-full transition-all"
          >
            <div class="bg-green-500 text-white p-3 text-center text-xs">
              {{ textToast }}
            </div>
          </div>

          <div
            v-show="textError"
            id="toast"
            class="fixed top-0 left-0 w-full transition-all"
          >
            <div class="bg-red-500 text-white p-3 text-center text-xs">
              {{ textError }}
            </div>
          </div>

          <button
            class="btn-secondary text-sm mt-4 flex items-center gap-1"
            :disabled="loading"
            type="button"
            @click.prevent="showPanel = false"
          >
            Tutup
          </button>
        </div>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
import moment from 'moment';

// const { $toast } = useNuxtApp();

const props = defineProps({
  id: String
});

const emit = defineEmits(['closed', 'refresh']);

const showPanel = ref(false);
const selectedFile = ref('');

const loading = ref(false);

const files = ref([]);
const preview = ref([]);
const url = ref('');
const textToast = ref('');

// const brandHost = ref('')
const search = ref('');

const textError = ref('');

const listFile = async () => {
  const { data } = await adminGet('/media');
  files.value = data.data;
};

const setFile = e => {
  const { files } = e.target;
  for (let index = 0; index < files.length; index++) {
    funcPreview(files[index]);
    setTimeout(() => {
      upload(files[index], index);
    }, 2000 * index);
  }
};

const filteredFile = computed(() => {
  const l = files.value.filter(f =>
    f.filename.toLowerCase().includes(search.value.toLowerCase())
  );
  if (search.value.length > 2) {
    return l;
  }
  return files.value;
});

const upload = async (e, index) => {
  const file = e;
  const formData = new FormData();
  formData.append('file', file);
  // upload file to server with axios
  const up = await adminFile('/upload', formData);
  if (up.data.status) {
    url.value = up.data.data;
    // remove file from preview
    preview.value.splice(index, 1);
    textToast.value = 'Berhasil upload';
    listFile();
  } else {
    textError.value = up.data.message;
  }
};

const funcPreview = v => {
  // untuk preview
  const reader = new FileReader();
  reader.onload = function (event) {
    const dataURL = event.target.result;
    preview.value.push(dataURL);
  };
  reader.readAsDataURL(v);
};

// watch toast and remove after 2s
watch(textToast, val => {
  if (val) {
    setTimeout(() => {
      textToast.value = '';
    }, 2000);
  }
});

watch(textError, val => {
  if (val) {
    setTimeout(() => {
      textError.value = '';
    }, 2000);
  }
});

const toFile = url => {
  navigator.clipboard.writeText(url);
  textToast.value = `Select :${url}`;
  selectedFile.value = url;
  showPanel.value = false;
  emit('closed', url);
};

onMounted(() => {
  listFile();
});
</script>
