/**
 * @jest-environment node
 */

const { cleanInvalidData } = require('../cleanInvalidData');
const User = require('../../models/user');
const Mua = require('../../models/mua');

// Mock the models
jest.mock('../../models/user');
jest.mock('../../models/mua');

describe('cleanInvalidData', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should clean invalid user data', async () => {
    // Mock user data
    const mockUsers = [
      { _id: '1', email: 'invalid@email', phone: '123' },
      { _id: '2', email: '<EMAIL>', phone: '+6281234567890' }
    ];

    // Mock Mua data
    const mockMuas = [
      { _id: '1', email: 'invalid@email', phone: '123' },
      { _id: '2', email: '<EMAIL>', phone: '+6281234567890' }
    ];

    // Mock find() to return mock data
    User.find.mockResolvedValue(mockUsers);
    Mua.find.mockResolvedValue(mockMuas);

    // Mock findByIdAndDelete
    User.findByIdAndDelete.mockResolvedValue({});
    Mua.findByIdAndDelete.mockResolvedValue({});

    await cleanInvalidData();

    // Verify that invalid users were deleted
    expect(User.findByIdAndDelete).toHaveBeenCalledWith('1');
    expect(User.findByIdAndDelete).not.toHaveBeenCalledWith('2');

    // Verify that invalid MUAs were deleted
    expect(Mua.findByIdAndDelete).toHaveBeenCalledWith('1');
    expect(Mua.findByIdAndDelete).not.toHaveBeenCalledWith('2');
  });

  it('should handle errors gracefully', async () => {
    // Mock find() to throw an error
    User.find.mockRejectedValue(new Error('Database error'));

    // Mock console.error
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    await cleanInvalidData();

    // Verify that error was logged
    expect(consoleSpy).toHaveBeenCalledWith('Error in data validation and cleanup:', expect.any(Error));

    // Restore console.error
    consoleSpy.mockRestore();
  });
}); 
