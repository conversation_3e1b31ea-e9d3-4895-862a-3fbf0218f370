const express = require("express");
const { loginAdmin } = require("../controllers/admin/authAdminController");
const {
  createAdmin,
  updateAdmin,
  listAdmin,
  deleteAdmin,
  detailAdmin,
} = require("../controllers/admin/adminController");
const {
  listLocation,
  createLocation,
  updateLocation,
  deleteLocation,
} = require("../controllers/admin/locationController");
const {
  createPayment,
  updatePayment,
  deletePayment,
  listPayment
} = require("../controllers/admin/paymentMethodController");
const {
  listPackage,
  createPackage,
  updatePackage,
  deletePackage,
  detailPackage,
} = require("../controllers/admin/packageController");
const { listOrder, createOrder, getCheckin, getCheckout, listBooking, checkoutManual, editOrder } = require("../controllers/admin/orderController");
const { detailOrder } = require("../controllers/user/orderController");
const { detailPay, confirmPayment, successPayment, listPay } = require("../controllers/admin/payController");
const {
  listBid,
  updateBid,
  selectedMua,
  cancelMua,
  historyPemilihanMua,
} = require("../controllers/admin/bidController");
const { listMua, actionMua, detailMua, detailPortofolio, editRating, hapusPortofolioMua, deleteMua, editMua, editApprovalMua, changePassword } = require("../controllers/admin/muaController");
const { listUser, detailUser, deleteUser } = require("../controllers/admin/userController");
const { uploadPortofolio, listPortofolio, updatePortofolio, deletePortofolio } = require("../controllers/admin/portofolioController");
const { listApproval, actionApprove } = require("../controllers/admin/approvalController");
const { getTotalCustomer, getTotalMua, getSummary, getGraph, getGraphRevenue } = require("../controllers/admin/statisticController");
const { tarikReferral, addSaldoReferral, listMutasiReferral } = require("../controllers/admin/referralController");
const { saveSetting, getSetting } = require("../controllers/admin/settingController");
const {
  listVoucher,
  detailVoucher,
  createVoucher,
  updateVoucher,
  deleteVoucher,
  toggleVoucherStatus,
  getVoucherStats
} = require("../controllers/admin/voucherController");
const checkSessionAdmin = require("../middlewares/check-auth-admin");

const router = express.Router();

// admin
const adminRouter = express.Router();
adminRouter.get("/", listAdmin);
adminRouter.post("/", createAdmin);
adminRouter.put("/", updateAdmin);
adminRouter.delete("/", deleteAdmin);
adminRouter.get("/detail", detailAdmin);

// location
const locationRouter = express.Router();
locationRouter.get("/", listLocation);
locationRouter.post("/", createLocation);
locationRouter.put("/", updateLocation);
locationRouter.delete("/", deleteLocation);

// payment
const paymentRouter = express.Router();
paymentRouter.get("/", listPayment);
paymentRouter.post("/", createPayment);
paymentRouter.put("/", updatePayment);
paymentRouter.delete("/", deletePayment);
paymentRouter.get("/success", successPayment)

// package
const packageRouter = express.Router();
packageRouter.get("/", listPackage);
packageRouter.get("/:id", detailPackage);
packageRouter.post("/", createPackage);
packageRouter.put("/", updatePackage);
packageRouter.delete("/", deletePackage);

// order
const orderRouter = express.Router();
orderRouter.get("/", listOrder);
orderRouter.get("/:id", detailOrder);
orderRouter.post("/", createOrder);
orderRouter.put("/", editOrder)
orderRouter.get("/checkin/:id", getCheckin);
orderRouter.get("/checkout/:id", getCheckout);
orderRouter.get("/booking/nearest", listBooking)
orderRouter.get("/checkout-manual/:id", checkoutManual)

// auth
const authRouter = express.Router();
authRouter.post("/login", loginAdmin);

// pay
const payRouter = express.Router();
payRouter.get("/:id", detailPay);
payRouter.get("/", listPay);
payRouter.post("/confirm/:id", confirmPayment)

// bid
const bidRouter = express.Router();
bidRouter.get("/:id", listBid);
bidRouter.put("/:id", updateBid);
bidRouter.post("/select", selectedMua);
bidRouter.post("/cancel", cancelMua);
bidRouter.get("/history/:id", historyPemilihanMua);

// mua
const muaRouter = express.Router();
muaRouter.get("/", listMua);
muaRouter.put("/", actionMua);
muaRouter.get("/:id", detailMua);
muaRouter.get("/portofolio/:id", detailPortofolio);
muaRouter.post("/rating", editRating);
muaRouter.delete("/portofolio/:id", hapusPortofolioMua);
muaRouter.delete("/:id", deleteMua);
muaRouter.put("/:id", editMua)
muaRouter.put("/approval", editApprovalMua)
muaRouter.post("/change-password", changePassword)

// user
const userRouter = express.Router();
userRouter.get("/", listUser);
userRouter.get("/:id", detailUser);
userRouter.delete("/:id", deleteUser);
// portofolio
const portofolioRouter = express.Router();
portofolioRouter.get("/", listPortofolio);
portofolioRouter.post("/", uploadPortofolio);
portofolioRouter.put("/", updatePortofolio);
portofolioRouter.delete("/:id", deletePortofolio);

// approval
const approvalRouter = express.Router();
approvalRouter.get("/", listApproval);
approvalRouter.put("/:id", actionApprove);

// statistics
const statisticsRouter = express.Router();
statisticsRouter.get("/summary", getSummary);
statisticsRouter.get("/customer", getTotalCustomer);
statisticsRouter.get("/mua", getTotalMua);
statisticsRouter.get("/graph", getGraph);
statisticsRouter.get("/revenue", getGraphRevenue);

// referral
const referralRouter = express.Router();
referralRouter.post('/tarik-saldo', tarikReferral)
referralRouter.post('/tambah-saldo', addSaldoReferral)
referralRouter.get('/mutasi', listMutasiReferral) // by userId

// setting
const settingRouter = express.Router();
settingRouter.get("/", getSetting);
settingRouter.post("/", saveSetting);

// voucher
const voucherRouter = express.Router();
voucherRouter.get("/", listVoucher);
voucherRouter.get("/stats", getVoucherStats);
voucherRouter.get("/:id", detailVoucher);
voucherRouter.post("/", createVoucher);
voucherRouter.put("/:id", updateVoucher);
voucherRouter.delete("/:id", deleteVoucher);
voucherRouter.patch("/:id/toggle", toggleVoucherStatus);

router.use("/auth",  authRouter);
router.use("/admin",checkSessionAdmin, adminRouter);
router.use("/location",checkSessionAdmin, locationRouter);
router.use("/payment",checkSessionAdmin, paymentRouter);
router.use("/package",checkSessionAdmin, packageRouter);
router.use("/order",checkSessionAdmin, orderRouter);
router.use("/pay",checkSessionAdmin, payRouter);
router.use("/bid",checkSessionAdmin, bidRouter);
router.use("/mua",checkSessionAdmin, muaRouter);
router.use("/user",checkSessionAdmin, userRouter);
router.use("/portofolio",checkSessionAdmin, portofolioRouter);
router.use("/approval",checkSessionAdmin, approvalRouter);
router.use("/statistics",checkSessionAdmin, statisticsRouter);
router.use("/referral",checkSessionAdmin, referralRouter);
router.use("/setting",checkSessionAdmin, settingRouter);
router.use("/voucher", checkSessionAdmin, voucherRouter);

module.exports = router;
