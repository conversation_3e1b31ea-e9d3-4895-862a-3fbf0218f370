<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <!-- Header -->
      <div class="px-4 pt-4 flex justify-between items-center">
        <div class="flex items-center space-x-3">
          <button
            @click="router.push('/order')"
            class="text-gray-600 hover:text-primary transition-colors p-2 rounded-lg hover:bg-gray-100"
          >
            <icon name="bitcoin-icons:arrow-left-filled" class="text-xl" />
          </button>
          <h1 class="font-bold text-lg">Detail Pesanan {{ detail.trxId }}</h1>
        </div>
        <button
          class="btn-primary flex items-center text-sm"
          @click.prevent="openEditOrder"
        >
          <icon name="iconamoon:edit" class="mr-2" />
          Edit <PERSON>
        </button>
      </div>

      <!-- Main Content -->
      <div class="p-4">
        <!-- Skeleton Loading -->
        <div v-if="loading" class="space-y-6">
          <!-- Header Skeleton -->
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
            <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
          </div>

          <!-- Grid Skeleton -->
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Left Column Skeleton -->
            <div class="space-y-6">
              <!-- Customer Info Skeleton -->
              <div class="space-y-4">
                <div class="space-y-2">
                  <div class="w-48 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-40 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="pt-3 border-t">
                  <div
                    class="w-16 h-3 bg-gray-200 rounded animate-pulse mb-2"
                  ></div>
                  <div
                    class="w-full h-4 bg-gray-200 rounded animate-pulse"
                  ></div>
                  <div
                    class="w-3/4 h-4 bg-gray-200 rounded animate-pulse mt-1"
                  ></div>
                </div>
              </div>

              <!-- Package Info Skeleton -->
              <div class="space-y-4">
                <div class="space-y-2">
                  <div class="w-48 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="pt-3 border-t">
                  <div
                    class="w-24 h-3 bg-gray-200 rounded animate-pulse mb-2"
                  ></div>
                  <div class="w-40 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="pt-3 border-t">
                  <div
                    class="w-16 h-3 bg-gray-200 rounded animate-pulse mb-2"
                  ></div>
                  <div
                    class="w-3/4 h-4 bg-gray-200 rounded animate-pulse"
                  ></div>
                </div>
              </div>
            </div>

            <!-- Right Column Skeleton -->
            <div class="space-y-6">
              <!-- Order Summary Skeleton -->
              <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                <div class="flex justify-between">
                  <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="flex justify-between">
                  <div class="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="flex justify-between">
                  <div class="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="border-t pt-3 flex justify-between">
                  <div class="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-40 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>

              <!-- Status Info Skeleton -->
              <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                <div class="flex justify-between">
                  <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="flex justify-between">
                  <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>

              <!-- MUA Share Skeleton -->
              <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                <div class="flex justify-between">
                  <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Job Bidding Skeleton -->
          <div class="mt-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
                <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div class="flex space-x-3">
                <div
                  class="w-32 h-8 bg-gray-200 rounded-lg animate-pulse"
                ></div>
                <div
                  class="w-32 h-8 bg-gray-200 rounded-lg animate-pulse"
                ></div>
              </div>
            </div>
            <div class="h-32 bg-gray-50 rounded-lg animate-pulse"></div>
          </div>

          <!-- Payment Details Skeleton -->
          <div class="mt-6">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
              <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div class="h-48 bg-gray-50 rounded-lg animate-pulse"></div>
          </div>

          <!-- Check-in/Check-out Skeleton -->
          <div class="grid grid-cols-2 gap-6 mt-6">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="w-40 h-4 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div class="h-64 bg-gray-50 rounded-lg animate-pulse"></div>
            </div>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
                  <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="w-40 h-4 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div class="h-64 bg-gray-50 rounded-lg animate-pulse"></div>
            </div>
          </div>
        </div>

        <!-- Actual Content -->
        <div v-else>
          <div class="grid grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
              <!-- Customer Information -->
              <div>
                <div class="flex items-center space-x-3 mb-4">
                  <icon name="mdi:user-outline" class="text-gray-600" />
                  <h2 class="font-semibold text-gray-800 text-sm">
                    Informasi Pelanggan
                  </h2>
                </div>
                <div class="space-y-4">
                  <div class="space-y-2">
                    <p class="text-gray-800 font-medium text-base">
                      {{ detail.name }}
                    </p>
                    <div
                      class="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors cursor-pointer text-sm"
                    >
                      <icon name="mdi:phone" class="text-base" />
                      <a
                        :href="'tel:' + detail.phone"
                        class="hover:underline"
                        >{{ detail.phone }}</a
                      >
                    </div>
                    <div
                      class="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors cursor-pointer text-sm"
                    >
                      <icon name="mdi:email" class="text-base" />
                      <a
                        :href="'mailto:' + detail.email"
                        class="hover:underline"
                        >{{ detail.email }}</a
                      >
                    </div>
                  </div>
                  <div class="pt-3 border-t">
                    <h3 class="text-xs font-medium text-gray-500 mb-2">
                      Alamat
                    </h3>
                    <div class="space-y-1 text-sm">
                      <p class="text-gray-800">{{ detail.address }}</p>
                      <p class="text-gray-600">{{ detail.locationName }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Order Summary -->
              <div>
                <div class="flex items-center space-x-3 mb-4">
                  <icon
                    name="material-symbols:receipt-outline"
                    class="text-gray-600"
                  />
                  <h2 class="font-semibold text-gray-800 text-sm">
                    Ringkasan Pesanan
                  </h2>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 space-y-3 text-sm">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600">Harga Paket</span>
                    <span class="font-medium">{{
                      useRupiah(detail.packagePrice)
                    }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600">Jumlah</span>
                    <span class="font-medium">{{ detail.pax }} Orang</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600">Subtotal</span>
                    <span class="font-medium">{{
                      useRupiah(detail.packagePrice * detail.pax)
                    }}</span>
                  </div>
                  <div
                    v-if="detail.voucherCode"
                    class="flex justify-between items-center text-red-500"
                  >
                    <div class="flex items-center space-x-2">
                      <icon name="mdi:tag-outline" />
                      <span>Diskon ({{ detail.voucherCode }})</span>
                    </div>
                    <span>-{{ useRupiah(detail.discountAmount) }}</span>
                  </div>
                  <div class="border-t pt-3 flex justify-between items-center">
                    <span class="font-semibold text-base">Total</span>
                    <span class="font-semibold text-primary">{{
                      useRupiah(detail.totalHarga)
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- Bid URL -->
              <div v-if="detail.isOpen">
                <div class="flex items-center space-x-3 mb-4">
                  <icon name="material-symbols:link" class="text-gray-600" />
                  <h2 class="font-semibold text-gray-800 text-sm">
                    Link Bidding
                  </h2>
                </div>
                <div class="flex items-center space-x-3">
                  <a
                    :href="linkBid"
                    target="_blank"
                    class="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  >
                    {{ linkBid }}
                    <icon name="ph:link" class="ml-2" />
                  </a>
                  <button
                    @click.prevent="copyText(linkBid)"
                    class="inline-flex items-center px-3 py-1.5 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  >
                    <icon name="ph:copy" class="mr-2" />
                    Salin
                  </button>
                </div>
              </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
              <!-- Package Information -->
              <div>
                <div class="flex items-center space-x-3 mb-4">
                  <icon name="mdi:package-outline" class="text-gray-600" />
                  <h2 class="font-semibold text-gray-800 text-sm">
                    Detail Paket
                  </h2>
                </div>
                <div class="space-y-4">
                  <div class="space-y-2">
                    <p class="text-gray-800 font-medium text-base">
                      {{ detail.packageName }}
                    </p>
                    <p class="text-gray-600 text-sm">
                      {{ detail.packageItemName }}
                    </p>
                  </div>
                  <div class="pt-3 border-t">
                    <h3 class="text-xs font-medium text-gray-500 mb-2">
                      Waktu Booking
                    </h3>
                    <div
                      class="flex items-center space-x-2 text-gray-800 text-sm"
                    >
                      <icon name="mdi:calendar" class="text-base" />
                      <p>
                        {{
                          useMoment(detail.bookingDate).format("DD MMM YYYY")
                        }}
                        pukul {{ detail.bookingTime }}
                      </p>
                    </div>
                  </div>
                  <div class="pt-3 border-t">
                    <h3 class="text-xs font-medium text-gray-500 mb-2">
                      Catatan
                    </h3>
                    <p class="text-gray-800 text-sm">
                      {{ detail.note || "-" }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Status Information -->
              <div>
                <div class="flex items-center space-x-3 mb-4">
                  <icon name="mdi:information-outline" class="text-gray-600" />
                  <h2 class="font-semibold text-gray-800 text-sm">
                    Status Informasi
                  </h2>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 space-y-3 text-sm">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600">Status Pesanan</span>
                    <status-order :status="detail.status" />
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600">Status Pembayaran</span>
                    <status-payment :status="detail.statusPayment" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-6">
            <!-- MUA Share and Rating -->
            <div>
              <div class="grid grid-cols-2 gap-6">
                <!-- Revenue Share -->
                <div>
                  <div class="flex items-center space-x-3 mb-4">
                    <icon
                      name="mdi:account-cash-outline"
                      class="text-gray-600"
                    />
                    <h2 class="font-semibold text-gray-800 text-sm">
                      Pendapatan
                    </h2>
                  </div>
                  <div class="bg-gray-50 rounded-lg p-4 space-y-3 text-sm">
                    <div class="flex justify-between items-center">
                      <span class="text-gray-600">Bagian MUA</span>
                      <span class="font-medium text-primary">{{
                        useRupiah(detail.muaShare)
                      }}</span>
                    </div>
                    <div
                      v-if="detail.referralCode"
                      class="pt-3 border-t space-y-3"
                    >
                      <div class="flex justify-between items-center">
                        <span class="text-gray-600">Kode Referral</span>
                        <span class="font-medium">
                          <nuxt-link
                            :to="`/customer/${detail.referralCode}?tab=referral`"
                            class="text-primary hover:underline"
                            >{{ detail.referralCode }}</nuxt-link
                          >
                        </span>
                      </div>
                      <div class="flex justify-between items-center">
                        <span class="text-gray-600">Komisi Referral</span>
                        <span class="font-medium text-primary">{{
                          useRupiah(detail.komisiReferral || 0)
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Rating and Review -->
                <div v-if="detail.status === 'DONE'">
                  <div class="flex items-center space-x-3 mb-4">
                    <icon name="mdi:star-outline" class="text-gray-600" />
                    <h2 class="font-semibold text-gray-800 text-sm">Ulasan</h2>
                  </div>
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center space-x-3">
                      <span
                        class="px-3 py-1.5 bg-primary-light text-primary font-semibold rounded-full text-base"
                      >
                        {{ detail.rating }}/5
                      </span>
                      <div class="flex space-x-1">
                        <icon
                          v-for="i in 5"
                          :key="i"
                          name="solar:star-bold"
                          class="text-xl"
                          :class="
                            detail.rating >= i
                              ? 'text-orange-400'
                              : 'text-gray-300'
                          "
                        />
                      </div>
                    </div>
                    <p class="text-gray-600 italic mt-3 text-sm">
                      "{{ detail.review }}"
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Job Bidding Section -->
          <div class="mt-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <icon name="hugeicons:job-search" class="text-gray-600" />
                <h2 class="font-semibold text-gray-800 text-sm">Job Bidding</h2>
              </div>
              <div class="flex space-x-3">
                <button
                  @click.prevent="showHistory = true"
                  class="inline-flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                >
                  <icon name="mdi:history" class="mr-2" />
                  Riwayat Pembatalan
                </button>
                <button
                  v-if="dataCancel && detail.status !== 'DONE'"
                  @click.prevent="openCancel = true"
                  class="inline-flex items-center px-3 py-1.5 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                >
                  <icon name="mdi:close-circle-outline" class="mr-2" />
                  Batalkan Pilihan
                </button>
                <button
                  @click.prevent="toggleBiddingStatus"
                  class="inline-flex items-center px-3 py-1.5 bg-primary text-white rounded-lg hover:bg-primary-dark transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  :disabled="loadingBidding"
                >
                  <icon
                    v-if="loadingBidding"
                    name="svg-spinners:270-ring-with-bg"
                    class="mr-2"
                  />
                  <icon
                    v-else
                    :name="detail.isOpen ? 'mdi:lock' : 'mdi:lock-open'"
                    class="mr-2"
                  />
                  {{ detail.isOpen ? "Tutup Penawaran" : "Buka Penawaran" }}
                </button>
              </div>
            </div>
            <div>
              <job-bid :id="route.params.id" @setSelectedMua="setDataCancel" />
            </div>
          </div>

          <!-- Payment Details Section -->
          <div class="mt-6">
            <div class="flex items-center space-x-3 mb-4">
              <icon name="hugeicons:money-03" class="text-gray-600" />
              <h2 class="font-semibold text-gray-800 text-sm">
                Detail Pembayaran
              </h2>
            </div>
            <div>
              <payment-detail :id="route.params.id" />
            </div>
          </div>

          <!-- Check-in/Check-out Grid -->
          <div class="grid grid-cols-2 gap-6 mt-6">
            <!-- Check-in -->
            <div v-if="dataCheckin">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <icon
                    name="fluent:briefcase-24-regular"
                    class="text-gray-600"
                  />
                  <h2 class="font-semibold text-gray-800 text-sm">Check-in</h2>
                </div>
                <span class="text-xs text-gray-500">
                  {{
                    useMoment(dataCheckin.createdAt).format("dddd, DD MMM YYYY")
                  }}
                  pukul
                  {{ useMoment(dataCheckin.createdAt).format("HH:mm") }}
                </span>
              </div>
              <div>
                <img
                  :src="dataCheckin.pictureUrl"
                  class="w-full h-64 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-all duration-200"
                  @click="showModal(dataCheckin.pictureUrl)"
                />
              </div>
            </div>

            <!-- Check-out -->
            <div v-if="dataCheckin">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <icon
                    name="fluent:briefcase-24-regular"
                    class="text-gray-600"
                  />
                  <h2 class="font-semibold text-gray-800 text-sm">Check-out</h2>
                </div>
                <div v-if="!dataCheckout">
                  <button
                    @click="checkoutManual()"
                    class="inline-flex items-center px-3 py-1.5 bg-primary text-white rounded-lg hover:bg-primary-dark transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                    :disabled="!dataCheckin || loadingCheckout"
                  >
                    <span v-if="loadingCheckout">
                      <LoaderFull class="w-4 h-4 mr-2" />
                    </span>
                    Check-out Manual
                  </button>
                </div>
                <span v-if="dataCheckout" class="text-xs text-gray-500">
                  {{
                    useMoment(dataCheckout.createdAt).format(
                      "dddd, DD MMM YYYY"
                    )
                  }}
                  pukul
                  {{ useMoment(dataCheckout.createdAt).format("HH:mm") }}
                </span>
              </div>
              <div>
                <img
                  v-if="dataCheckout"
                  :src="dataCheckout.pictureUrl"
                  class="w-full h-64 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-all duration-200"
                  @click="showModal(dataCheckout.pictureUrl)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <LoaderFull v-if="loading" /> -->

    <!-- Modals -->
    <cancel-mua
      :show="openCancel"
      :data="dataCancel"
      @closed="handleCancelClosed"
      @refresh="router.go(0)"
    />

    <add-saldo-referral
      :show="showSaldoReferral"
      :data="route.params.id"
      @closed="closeSaldoReferral"
      @refresh="router.go(0)"
    />

    <modal v-if="showPictureModal" @close="showPictureModal = false">
      <div class="fixed inset-0 flex items-center justify-center z-50">
        <div class="relative max-w-4xl w-full">
          <button
            @click="showPictureModal = false"
            class="absolute -top-10 right-0 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-colors"
          >
            <icon name="ph:x" class="text-xl" />
          </button>
          <img
            :src="modalPictureUrl"
            class="w-full h-[80vh] object-contain rounded-lg"
          />
        </div>
      </div>
      <div class="fixed inset-0 bg-black opacity-50"></div>
    </modal>

    <edit-order
      :show="showEditOrder"
      :data="detail"
      @closed="closeEditOrder"
      @refresh="getData"
    />

    <open-history
      :show="showHistory"
      :trxId="route.params.id"
      @closed="closeHistory"
    />
  </div>
</template>

<script setup>
import LoaderFull from "~/components/loader-full.vue";
import EditOrder from "~/components/order/edit-order.vue";
import OpenHistory from "~/components/order/open-history.vue";

const route = useRoute();
definePageMeta({
  middleware: "auth-admin",
});

const openCancel = ref(false);
const showHistory = ref(false);
const dataCancel = ref({});
const showEditOrder = ref(false);

const { $toast } = useNuxtApp();

const linkBid = computed(() => {
  return `https://dev-fe.hellobeauty.id/bid/${route.params.id}`;
});

const copyText = (text) => {
  navigator.clipboard.writeText(text);
  $toast.success("Link copied to clipboard");
};
const router = useRouter();
const detail = ref({});
const loading = ref(true);
const errorMsg = ref("");

const showSaldoReferral = ref(false);

const closeSaldoReferral = () => {
  showSaldoReferral.value = false;
};

const handleCancelClosed = () => {
  openCancel.value = false;
};

const setDataCancel = (v) => {
  dataCancel.value = v;
};

const openEditOrder = () => {
  showEditOrder.value = true;
};

const closeEditOrder = () => {
  showEditOrder.value = false;
};

const closeHistory = () => {
  showHistory.value = false;
};

const getData = async () => {
  try {
    errorMsg.value = "";
    detail.value = [];
    const { data } = await adminGet(`/order/${route.params.id}`);
    // Add artificial delay to show skeleton loading
    await new Promise((resolve) => setTimeout(resolve, 1000));
    detail.value = data.data;
    detail.value.statusOrder = data.data.statusOrder;
    detail.value.statusPayment = data.data.statusPayment;
    loading.value = false;
  } catch (error) {
    errorMsg.value = error.response.data.message;
    loading.value = false;
  }
};

useHead({
  title: "Detail Order",
});

const dataCheckin = ref([]);

const getCheckin = async () => {
  try {
    dataCheckin.value = {};
    const { data } = await adminGet(`/order/checkin/${route.params.id}`);
    dataCheckin.value = data.data;
  } catch (error) {
    errorMsg.value = error.response.data.message;
  }
};

const dataCheckout = ref([]);

const getCheckout = async () => {
  try {
    dataCheckout.value = {};
    const { data } = await adminGet(`/order/checkout/${route.params.id}`);
    dataCheckout.value = data.data;
  } catch (error) {
    errorMsg.value = error.response.data.message;
  }
};

const showPictureModal = ref(false);
const modalPictureUrl = ref("");

const showModal = (url) => {
  modalPictureUrl.value = url;
  showPictureModal.value = true;
};

const loadingCheckout = ref(false);

const checkoutManual = async () => {
  if (!dataCheckin.value) {
    $toast.error("Check-in is required before manual checkout.");
    return;
  }
  try {
    loadingCheckout.value = true;
    const { data } = await adminGet(
      `/order/checkout-manual/${route.params.id}`
    );
    $toast.success(data.message);
    Promise.all([getData(), getCheckin(), getCheckout()]);
  } catch (error) {
    $toast.error(error.response.data.message);
  } finally {
    loadingCheckout.value = false;
  }
};

const loadingBidding = ref(false);

const toggleBiddingStatus = async () => {
  try {
    loadingBidding.value = true;
    const { data } = await adminPut(`/bid/${route.params.id}`, {
      trxId: route.params.id,
      isOpen: !detail.value.isOpen,
    });
    loading.value = true;
    $toast.success(data.message);
    // Reload all data after successful status change
    await getData();
  } catch (error) {
    $toast.error(error.response.data.message);
  } finally {
    loadingBidding.value = false;
  }
};

onMounted(() => {
  Promise.all([getData(), getCheckin(), getCheckout()]);
  document.title = "Detail Order " + route.params.id;
});
</script>

