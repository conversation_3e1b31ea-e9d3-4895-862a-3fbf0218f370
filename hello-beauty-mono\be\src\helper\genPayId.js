const generatePayId = ()=> {
    // Get the current date
    const date = new Date();

    // Extract the last 4 digits of the day
    const day = ('0' + date.getDate()).slice(-2);

    // Generate a random 4 character string
    const randomString = Math.random().toString(36).substring(2, 6).toUpperCase();

    // Get the current month and year
    const month = ('0' + (date.getMonth() + 1)).slice(-2); // Months are 0-based, so we add 1
    const year = date.getFullYear().toString().slice(-2);  // Get last 2 digits of the year

    // Combine to form the PayId
    const PayId = `${day}${month}${year}-${randomString}`;
    return PayId;
}

module.exports = generatePayId;
