"use client";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const BottomMenu = () => {
  //
  const menus = [
    {
      label: "Beranda",
      icon: "tabler:smart-home",
      link: "/mua",
    },
    {
      label: "Bid",
      icon: "tabler:gavel",
      link: "/mua/bid",
    },
    {
      label: "My Bid",
      icon: "tabler:receipt",
      link: "/mua/my-bid",
    },
    {
      label: "Profil",
      icon: "tabler:user-square-rounded",
      link: "/mua/profil",
    },
  ];

  const currentPath = usePathname();

  return (
    <div className="fixed bottom-0 left-0 w-full bg-white">
      <div className="max-w-[480px] mx-auto">
        <div className="grid grid-cols-4">
          {menus.map((menu, index) => {
            return (
              <Link
                href={menu.link}
                key={index}
                className={`text-center py-2 flex items-center border-t-2 justify-center text-gray-500 ${currentPath === menu.link ? "text-hb-pink border-t-hb-pink" : ""}`}
              >
                <div>
                  <Icon icon={menu.icon} className="text-2xl mx-auto" />
                  <div className="text-[10px]">{menu.label}</div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};
export default BottomMenu;
