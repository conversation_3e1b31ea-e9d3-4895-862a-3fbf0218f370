<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">
              Ubah Status Penawaran
            </h2>
          </div>
          <div>
            <select v-model="payload.isOpen" class="form-input">
              <option :value="true">Terbuka</option>
              <option :value="false">Tertutup</option>
            </select>
          </div>
          <!-- <div
            v-if="payload.isOpen"
            class="text-sm text-primary mt-4 p-3 bg-primary-light"
          >
            Penawaran dapat dilakukan di link
            <span class="underline font-semibold block">
              https://king-prawn-app-qvrcu.ondigitalocean.app/bid/{{
                payload.trxId
              }}
            </span>
          </div> -->
          <div class="h-4" />
          <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);
const route = useRoute();
const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);

const payload = ref({
  trxId: route.params.id,
  isOpen: false,
});
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    if (props.data) {
      payload.value = {
        trxId: props.data.trxId,
        isOpen: props.data.isOpen,
      };
    }
  },
  { deep: true },
);

const save = async () => {
  try {
    loading.value = true;
    const p = {
      ...payload.value,
    };

    const res = await adminPut(`/bid/${route.params.id}`, p);
    loading.value = false;

    $toast.success(res.data.message);
    emit("refresh");
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
