<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <div v-if="data">
          <form v-if="showDetail" class="p-4">
            <div
              class="flex gap-2 items-center mb-4"
              @click.prevent="$emit('closed')"
            >
              <icon name="line-md:arrow-left" />
              <h2 class="font-bold">Review MUA</h2>
            </div>
            <div class="grid grid-cols-2 gap-3 !text-sm">
              <div class="mb-2">
                <label class="block text-xs font-semibold"><PERSON><PERSON></label>
                <span>{{ data.name }}</span>
              </div>

              <div class="mb-2">
                <label class="block text-xs font-semibold">MUA</label>
                <span>{{ data.profileName }}</span>
              </div>

              <div class="mb-2">
                <label class="block text-xs font-semibold"
                  >Email / Whatsapp</label
                >
                <span> {{ data.email }} / {{ data.phone }} </span>
              </div>

              <div class="mb-2">
                <label class="block text-xs font-semibold">Instagram</label>
                <a
                  :href="`https://instagram.com/${data.instagram}`"
                  target="_blank"
                  class="text-primary"
                >
                  {{ data.instagram }}
                  <icon
                    name="iconamoon:arrow-top-right-1-bold"
                    class="text-sm"
                  />
                </a>
              </div>

              <div class="mb-2 col-span-2">
                <label class="block text-sm font-semibold">Layanan</label>
                <div class="mt-2">
                  <div class="flex flex-wrap gap-2">
                    <div
                      v-for="(d, i) in data.serviceType"
                      :key="i"
                      class="bg-primary-light rounded-full px-2 text-primary"
                    >
                      {{ d }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="mb-2">
                <label class="block text-sm font-semibold">Alamat</label>
                <span>
                  {{ data.address }}
                </span>
                <span class="block">
                  {{ data.locationName }}
                </span>
              </div>

              <div class="mb-2">
                <label class="block text-sm font-semibold"
                  >Akun Sebelumnya</label
                >
                <a :href="data.linkHb" target="_blank" class="text-primary">
                  {{ data.linkHb }}
                  <icon
                    name="iconamoon:arrow-top-right-1-bold"
                    class="text-sm"
                  />
                </a>
              </div>

              <div class="mb-2">
                <label class="block text-sm font-semibold"
                  >Lama Menjadi MUA</label
                >
                <span> {{ data.lamaMua || "-" }} tahun </span>
              </div>

              <div class="mb-2">
                <label class="block text-sm font-semibold">Ikut Acara HB</label>
                <span>
                  {{ data.hasParticipated ? "Pernah" : "Tidak Pernah" }}
                </span>
              </div>
              <div class="mb-2">
                <label class="block text-sm font-semibold">Job HB </label>
                <span>
                  {{ data.hasJob ? "Pernah" : "Tidak Pernah" }}
                  {{ data.hasJob ? `( ${data.jumlah_job} x )` : "" }}
                </span>
              </div>

              <div class="mb-2">
                <label class="block text-sm font-semibold"
                  >Menjadi Partner / Kolaborasi</label
                >
                <span>
                  {{ data.hasCollaboration ? "" : "Tidak Pernah" }}
                </span>
                <span v-if="data.hasCollaboration" class="block text-primary">
                  {{ data.collaboration }}
                </span>
              </div>
              <div class="mb-2 col-span-2">
                <label class="block text-sm font-semibold"
                  >Sertifikat Training</label
                >
                <div class="w-full">
                  <div v-for="(d, i) in data.trainings" :key="i" class="mb-2">
                    <a
                      :href="d"
                      target="_blank"
                      class="text-primary flex items-center border-b py-1 gap-2 justify-between"
                    >
                      <div>Training {{ i + 1 }}</div>
                      <icon
                        name="iconamoon:arrow-top-right-1-bold"
                        class="text-sm"
                      />
                    </a>
                  </div>
                  <div v-if="data && !data.trainings">
                    <span class="text-sm">Tidak ada training</span>
                  </div>
                </div>
              </div>
              <div class="mb-2 col-span-2">
                <label class="block text-sm font-semibold"
                  >Sertifikat Nasional</label
                >
                <div class="w-full">
                  <div
                    v-for="(d, i) in data.certificates"
                    :key="i"
                    class="mb-2"
                  >
                    <a
                      :href="d"
                      target="_blank"
                      class="text-primary flex items-center border-b py-1 gap-2 justify-between"
                    >
                      <div>Sertifikat {{ i + 1 }}</div>
                      <icon
                        name="iconamoon:arrow-top-right-1-bold"
                        class="text-sm"
                      />
                    </a>
                  </div>
                  <div v-if="!data.certificates">
                    <span class="text-sm">Tidak ada sertifikat</span>
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-2 col-span-2">
                <button
                  class="btn-success"
                  @click.prevent="(showDetail = false), (status = 'approved')"
                >
                  <icon name="lets-icons:check-fill" class="text-xl" />
                  Setujui
                </button>
                <button
                  class="btn-danger"
                  @click.prevent="(showDetail = false), (status = 'rejected')"
                >
                  <icon name="lets-icons:close-round-fill" class="text-xl" />
                  Tolak
                </button>
              </div>
            </div>
          </form>

          <form v-else class="p-4">
            <button
              class="font-semibold flex items-center gap-2"
              @click.prevent="showDetail = true"
            >
              <icon name="line-md:arrow-left" />
              {{ status === "approved" ? "Setujui MUA" : "Tolak MUA" }}
            </button>
            <div v-if="status === 'rejected'" class="mb-4">
              <label class="text-sm">Alasan</label>
              <textarea v-model="note" class="h-32 form-input" />
            </div>
            <div v-if="status === 'approved'" class="mb-4">
              <label class="text-sm">MUA Paket</label>
              <select v-model="packageId" class="form-input">
                <option value="" disabled selected>Pilih Paket</option>
                <option v-for="d in packages" :key="d._id" :value="d._id">
                  {{ d.name }}
                </option>
              </select>
            </div>

            <div class="flex gap-2">
              <button
                v-if="status === 'approved'"
                class="btn-success"
                @click.prevent="actionApproval()"
              >
                <icon name="lets-icons:check-fill" class="text-xl" />
                Setujui
              </button>
              <button
                v-if="status === 'rejected'"
                class="btn-danger"
                @click.prevent="actionApproval()"
              >
                <icon name="lets-icons:close-round-fill" class="text-xl" />
                Tolak
              </button>
            </div>
          </form>
        </div>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);

const props = defineProps({
  show: Boolean,
  data: {
    type: Object,
    default: () => ({
      name: "",
      email: "",
      phone: "",
      instagram: "",
      address: "",
      locationName: "",
      linkHb: "",
      lamaMua: "",
      hasParticipated: false,
      hasJob: false,
      jumlah_job: 0,
      hasCollaboration: false,
      collaboration: "",
      serviceType: [],
      trainings: [],
      certificates: [],
    }),
  },
});

const showPanel = ref(false);

const payload = ref({});
const loading = ref(false);

const status = ref("");
const showDetail = ref(true);
const packages = ref([]);
const packageId = ref("");
const note = ref("");
const errorMsg = ref("");

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    getPackages();
    if (props.data) {
      payload.value = { ...props.data };
    }
  },
  { deep: true }
);

const getPackages = async () => {
  try {
    const { data } = await adminGet(`/package?page=1&limit=10`);
    packages.value = data.data;
    errorMsg.value = "Data tidak ditemukan";
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};

const actionApproval = async () => {
  try {
    loading.value = true;

    if (!status.value) {
      $toast.error("Pilih status terlebih dahulu");
      return;
    }

    if (status.value === "rejected" && !note.value) {
      $toast.error("Alasan harus diisi");
      return;
    }

    if (status.value === "approved" && !packageId.value) {
      $toast.error("Pilih paket terlebih dahulu");
      return;
    }

    const p = {
      status: status.value,
      note: note.value,
      packageId: packageId.value,
    };
    const res = await adminPut("/approval/" + props.data._id, p);
    loading.value = false;
    $toast.success(res.data.message);
    emit("refresh");
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
