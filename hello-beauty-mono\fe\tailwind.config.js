/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    fontFamily:{
      'sans': ['"Nunito Sans"', 'sans-serif'],
      'serif': ['"Nunito Sans"', 'serif'],
      'mono': ['"Nunito Sans"', 'monospace'],
    },
    extend: {
      colors:{
        pinkStart: '#FF9AB3', // Light pink
        pinkMiddle: '#FF6D94', // Medium pink
        pinkEnd: '#FF4181', // Dark pink
        hb:{
          'pink':'#F24664',
          'pink-light':'#FFC8C8',
          'pink-light-2':'#FFF1F1',
        }
      },
      backgroundImage: {
        'pink-gradient': 'linear-gradient(to right, #FF9AB3, #FF6D94, #FF4181)',
      }
    },
  },
  plugins: [],
};
