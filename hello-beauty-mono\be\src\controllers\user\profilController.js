const user = require('../../models/user')
const {decodeJwtClient} = require('../../helper/jwt')
const multer = require('multer');
const path = require('path');
const moment = require('moment');
const fs = require('fs');
const bcrypt = require('bcrypt');
const { logFunction, logError, logSuccess } = require('../../utils/logger');

const getProfile = async (req, res) => {
  const fnName = 'getProfile';
  logFunction(fnName, { userId: req.headers.authorization ? 'authenticated' : 'anonymous' });

  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const searchUser = await user.findById(u.id);
    
    if(!searchUser) {
      logFunction(fnName, { error: 'User not found', userId: u.id }, 'warn');
      return res.status(404).json({
        message: "User not found"
      });
    }

    const returnResponse = {
      name: searchUser.name,
      email: searchUser.email,
      phone: searchUser.phone,
      id: searchUser._id,
      referralCode: searchUser.referralCode,
      photo: searchUser.photo,
      saldoReferral: searchUser.saldoReferral
    };

    logSuccess(fnName, { userId: u.id });
    return res.status(200).json({
      message: "Success",
      data: returnResponse
    });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

const updatePhoto = async (req, res) => {
  const fnName = 'updatePhoto';
  logFunction(fnName, { userId: req.headers.authorization ? 'authenticated' : 'anonymous' });

  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const searchUser = await user.findById(u.id);
    
    if(!searchUser) {
      logFunction(fnName, { error: 'User not found', userId: u.id }, 'warn');
      return res.status(404).json({
        message: "User not found"
      });
    }

    const currentMonthYear = moment().format('MM-YYYY');
    const uploadDir = `./src/public/uploads/${currentMonthYear}/user/${u.id}`;
    
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true,mode: 0o777 });
      logFunction(fnName, { action: 'Created upload directory', path: uploadDir });
    }

    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, uploadDir);
      },
      filename: function (req, file, cb) {
        cb(null, file.fieldname + '-' + Date.now() + path.extname(file.originalname));
      }
    });

    const upload = multer({
      storage: storage,
      fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png/;
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = filetypes.test(file.mimetype);
        if (mimetype && extname) {
          return cb(null, true);
        } else {
          logFunction(fnName, { error: 'Invalid file type', fileType: file.mimetype }, 'warn');
          cb('Error: Images only!');
        }
      }
    }).single('file');

    upload(req, res, async (err) => {
      if (err) {
        if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          logFunction(fnName, { error: 'Unexpected file field' }, 'warn');
          return res.status(400).json({
            message: 'Unexpected field'
          });
        }
        logError(fnName, err);
        res.status(500).json({
          message: 'Internal server error',
          error: err.message
        });
      } else {
        if (!req.file) {
          logFunction(fnName, { error: 'No file uploaded' }, 'warn');
          return res.status(400).json({
            message: 'No file uploaded'
          });
        }
        const { filename, path } = req.file;
        const fullUrl = `https://assets.hellobeauty.id/uploads/${currentMonthYear}/user/${u.id}/${filename}`;
        const currentPath = path.replace('src/public', '');
        searchUser.photo = fullUrl;
        await searchUser.save();
        
        logSuccess(fnName, { 
          userId: u.id,
          fileName: filename,
          filePath: currentPath
        });
        
        res.status(201).json({
          message: 'Photo updated successfully',
          data: {
            fileName: filename,
            filePath: currentPath,
            fullUrl
          }
        });
      }
    });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

const updateProfile = async (req, res) => {
  const fnName = 'updateProfile';
  logFunction(fnName, { 
    userId: req.headers.authorization ? 'authenticated' : 'anonymous',
    updates: {
      name: req.body.name,
      email: req.body.email,
      phone: req.body.phone
    }
  });

  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const searchUser = await user.findById(u.id);
    
    if(!searchUser) {
      logFunction(fnName, { error: 'User not found', userId: u.id }, 'warn');
      return res.status(404).json({
        message: "User not found"
      });
    }

    const {name, phone, email} = req.body;

    // validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      logFunction(fnName, { error: 'Invalid email format', email }, 'warn');
      return res.status(400).json({
        message: "Email tidak valid"
      });
    }

    // validate phone number
    if (!phone || phone.trim().length < 10) {
      logFunction(fnName, { error: 'Invalid phone number', phone }, 'warn');
      return res.status(400).json({
        message: "Nomor telepon tidak valid"
      });
    }

    let formattedPhone = phone;
    if (phone.startsWith("62")) {
      formattedPhone = "08" + phone.slice(2);
    }

    searchUser.name = name;
    searchUser.email = email;
    searchUser.phone = formattedPhone;
    await searchUser.save();
    
    logSuccess(fnName, { 
      userId: u.id,
      updates: {
        name,
        email,
        phone: formattedPhone
      }
    });
    
    return res.status(201).json({
      message: "Profile updated successfully"
    });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

const changePassword = async (req, res) => {
  const fnName = 'changePassword';
  logFunction(fnName, { userId: req.headers.authorization ? 'authenticated' : 'anonymous' });

  try {
    const body = req.body;
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const id = u.id;
    
    if(!id) {
      logFunction(fnName, { error: 'User ID not found' }, 'warn');
      return res.status(400).json({ message: 'ID tidak ditemukan' });
    }

    const search = await user.findById(id);
    if(!search) {
      logFunction(fnName, { error: 'User not found', userId: id }, 'warn');
      return res.status(400).json({ message: 'Admin tidak ditemukan' });
    }

    const compare = await bcrypt.compare(body.old_password, search.password);
    if(!compare) {
      logFunction(fnName, { error: 'Incorrect old password', userId: id }, 'warn');
      return res.status(400).json({ message: 'Password lama tidak sesuai' });
    }

    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
    if(!regex.test(body.new_password)) {
      logFunction(fnName, { error: 'Password does not meet requirements', userId: id }, 'warn');
      return res.status(400).json({ 
        message: 'Password harus terdiri dari 8 karakter, 1 huruf besar, 1 huruf kecil, dan 1 angka' 
      });
    }

    const encPassword = await bcrypt.hashSync(body.new_password, 10);
    const updatePassword = await user.findByIdAndUpdate(id, { password: encPassword });
    await updatePassword.save();

    logSuccess(fnName, { userId: id });
    return res.status(201).json({ message: 'Berhasil mengubah password' });

  } catch (error) {
    logError(fnName, error);
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  getProfile,
  updatePhoto,
  updateProfile,
  changePassword
};
