@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap');


html, body {
    scroll-behavior: smooth;
    overflow-x: hidden !important;
}

.hero-bg {
    background-image: url('/images/hero-1.png');
    background-size: cover;
    background-position: center;
}

.hero-bg-2 {
    background-image: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 1)), url('/images/images-3.jpeg');
    background-position: top left;
    background-repeat: no-repeat;
}

.dot-bg {
    background-image: linear-gradient(rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 1)), url('/images/dot-grid.png');
    background-position: top;
    background-repeat: no-repeat;
    background-size: cover;
}

.form-bg {
    background-image: url('/images/images-5.jpeg');
    background-size: cover;
    background-position: center
}

.grid-bg {
    background-image: url('/images/col-grid.png');
    background-position: top;
    background-repeat: no-repeat;
    background-size: auto;
}

@layer components {
    .btn-primary {
        @apply px-6 py-2 w-full text-white  rounded-full bg-gradient-to-b from-[#EF81BB] via-pinkMiddle to-[#EE5D53] shadow-lg hover:scale-105 transform transition;
    }

    .btn-secondary {
        @apply px-6 py-2  w-full text-pinkStart font-semibold rounded-full border border-pinkStart hover:bg-pink-gradient hover:text-white transition-all;

    }

    .form-input {
        @apply bg-white border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight outline-none;
    }

}

@layer utilities {
    .text-outline-transparent {
        color: transparent;
        -webkit-text-stroke: 1px black;
    }
}

.swiper-pagination-bullet-active {
    background: #FF5E5E !important;
}

/*input[type="date"]::-webkit-calendar-picker-indicator {*/
/*  display: none;*/
/*  -webkit-appearance: none;*/
/*}*/
