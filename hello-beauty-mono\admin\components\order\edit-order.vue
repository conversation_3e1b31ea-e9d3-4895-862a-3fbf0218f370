<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <form class="p-4" @submit.prevent="submitForm">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">Edit Order</h2>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Name</label
              >
              <input
                v-model="form.name"
                type="text"
                class="form-input"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Email</label
              >
              <input
                v-model="form.email"
                type="email"
                class="form-input"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Phone</label
              >
              <input
                v-model="form.phone"
                type="text"
                class="form-input"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Alamat</label
              >
              <input
                v-model="form.address"
                type="text"
                class="form-input"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Location ID</label
              >
              <select v-model="form.locationId" class="form-input" required>
                <option
                  v-for="location in listLocation"
                  :key="location._id"
                  :value="location._id"
                  :selected="location._id === form.locationId"
                >
                  {{ location.name }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Booking Date</label
              >
              <input
                v-model="form.booking_date"
                type="date"
                class="form-input"
                required
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Booking Time</label
              >
              <input
                v-model="form.booking_time"
                type="time"
                class="form-input"
                required
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Note</label
              >
              <input v-model="form.note" type="text" class="form-input" />
            </div>
          </div>
          <div class="h-4" />
          <div v-if="errorMessage" class="text-red-500 text-sm mb-2">
            {{ errorMessage }}
          </div>
          <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
import { useMoment } from "@/composables/useMoment";
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);
const route = useRoute();
const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);
const loadingLocation = ref(false);
const listLocation = ref([]);
const listPaket = ref([]);
const selectedPackage = ref({});
const listItem = computed(() => {
  const p = listPaket.value.filter((item) => item.id === form.value.packageId);
  return p[0] ? p[0].items : [];
});

const form = ref({
  trxId: "",
  name: "",
  email: "",
  phone: "",
  alamat: "",
  packageId: "",
  itemId: "",
  locationId: "",
  harga_paket: 0,
  harga_paket_share: 0,
  pax: 0,
  dp: 0,
  note: "",
  booking_date: "",
  booking_time: "",
});

const loading = ref(false);
const errorMessage = ref("");

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    getLocation();
    getPaket();
    setPayload();
  },
  { deep: true }
);

watch(
  selectedPackage,
  () => {
    form.value.itemId = "";
  },
  { deep: true }
);

watch(
  () => form.value.itemId,
  (newItemId) => {
    const selectedItem = listItem.value.find((item) => item.id === newItemId);
    if (selectedItem) {
      form.value.harga_paket = selectedItem.price;
      form.value.harga_paket_share = selectedItem.priceShare;
    }
  },
  { deep: true }
);

const close = () => {
  emit("closed");
};

const submitForm = async () => {
  loading.value = true;
  errorMessage.value = "";
  try {
    await adminPut(`/order`, form.value);
    $toast.success("Order updated successfully");
    emit("refresh");
    // close(); // Do not close the panel on error
  } catch (error) {
    errorMessage.value = error.response.data.message;
    $toast.error(error.response.data.message);
  } finally {
    loading.value = false;
  }
};

const setPayload = () => {
  const newData = props.data;
  form.value = {
    trxId: newData.trxId || "",
    name: newData.name || "",
    email: newData.email || "",
    phone: newData.phone || "",
    address: newData.address || "",
    packageId: newData.packageId || "",
    itemId: newData.itemId || "",
    locationId: newData.locationId || "",
    harga_paket: newData.harga_paket || 0,
    harga_paket_share: newData.harga_paket_share || 0,
    pax: newData.pax || 0,
    dp: newData.dp || 0,
    note: newData.note || "",
    booking_date: useMoment(newData.bookingDate).format("YYYY-MM-DD") || "",
    booking_time: newData.bookingTime,
  };
};

const getLocation = async () => {
  try {
    loadingLocation.value = true;
    const { data } = await adminGet(`/location?page=1&limit=999`);
    loadingLocation.value = false;
    listLocation.value = data.data;
  } catch (error) {
    loadingLocation.value = false;
  }
};

const getPaket = async () => {
  try {
    listPaket.value = [];
    const { data } = await adminGet(`/package?page=1&limit=999`);
    listPaket.value = data.data;
  } catch (error) {
    console.log(error);
  }
};
</script>
