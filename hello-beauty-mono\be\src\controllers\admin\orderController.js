const transaction = require("../../models/transaction");
const user = require("../../models/user");
const package = require("../../models/package");
const payment = require("../../models/payment");
const location = require("../../models/location");

const { sendWhatsapp } = require("../../helper/kirimi");
const { toRp } = require("../../helper/rupiah");
const generateTrxId = require("../../helper/genId");
const generatePayId = require("../../helper/genPayId");
const mua = require("../../models/mua");
const checkin = require("../../models/checkin");
const checkout = require("../../models/checkout");
const moment = require("moment");

/**
 * Payload for creating an order:
 * {
 *   "name": "string", // required
 *   "email": "string", // required
 *   "phone": "string", // required
 *   "alamat": "string", // required
 *   "packageId": "string", // required
 *   "itemId": "string", // required
 *   "locationId": "string", // required
 *   "harga_paket": "number", // required
 *   "harga_paket_share": "number", // required
 *   "pax": "number", // required
 *   "dp": "number", // required
 *   "note": "string", // optional
 *   "ref": "string", // optional
 *   "booking_date": "string", // required, format: YYYY-MM-DD
 *   "booking_time": "string", // required, format: HH:mm
 *   "share_wa": "boolean" // optional
 * }
 */

/**
 * Payload for editing an order:
 * {
 *   "trxId": "string", // required
 *   "name": "string", // required
 *   "email": "string", // required
 *   "phone": "string", // required
 *   "alamat": "string", // required
 *   "packageId": "string", // required
 *   "itemId": "string", // required
 *   "locationId": "string", // required
 *   "harga_paket": "number", // required
 *   "harga_paket_share": "number", // required
 *   "pax": "number", // required
 *   "dp": "number", // required
 *   "note": "string", // optional
 *   "booking_date": "string", // required, format: YYYY-MM-DD
 *   "booking_time": "string" // required, format: HH:mm
 * }
 */

const listOrder = async (req, res) => {
  try {
    // with pagination, page and limit
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // create query object
    const query = {};

    if (req.query.search) {
      const search = req.query.search;
      query.$or = [
        { trxId: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { packageName: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { referralCode: { $regex: search, $options: 'i' } }
      ];
    }

    // filter by bookingDate
    if (req.query.bookingDate) {
      query.bookingDate = new Date(req.query.bookingDate);
    }

    // filter by rating
    if(req.query.rating) {
      query.rating = req.query.rating
    }

    // filter by status
    if (req.query.status) {
      query.status = req.query.status;
    }

    // filter by statusPayment
    if (req.query.statusPayment) {
      query.statusPayment = req.query.statusPayment;
    }

    // filter by locationId
    if (req.query.locationId) {
      query.locationId = req.query.locationId;
    }

    // dynamic sorting
    const sortBy = req.query.sort_by || '_id';
    const sortOrder = req.query.sort_order === 'desc' ? 1 : -1;

    // order by created_at desc
    let transactions = await transaction
      .find(query)
      .sort({ [sortBy]: sortOrder })
      .skip(skip)
      .limit(limit);
    transactions = transactions.map((trx) => {
      return {
        ...trx.toObject(),
        status: trx.status.toUpperCase(),
        statusPayment: trx.statusPayment.toUpperCase(),
      };
    });

    return res.status(200).json({ message: "List order", data: transactions });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "Internal server error" });
  }
};

const detailOrder = async (req, res) => {
  try {
    const id = req.params.id;

    const checkTrx = await transaction.findOne({
      trxId: id,
    });

    if (!checkTrx) {
      return res.status(400).json({ message: "Transaksi tidak ditemukan" });
    }

    return res
      .status(200)
      .json({ message: "Detail transaksi", data: checkTrx });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};

const createOrder = async (req, res) => {
  try {
    const body = req.body;
    let u

    // check phone
    if (!body.phone) {
      return res.status(400).json({
        message: "Nomor telepon harus diisi",
      });
    }

    // check packageId
    if (!body.packageId) {
      return res.status(400).json({
        message: "Paket belum dipilih",
      });
    }

    // check user
    const checkUser = await user.findOne({
      phone:body.phone
    })

    // // if user not found, create new user
    if (!checkUser) {
      const newUser = new user({
        name: body.name,
        email: body.email,
        phone: body.phone,
        password: "-"
      });
      await newUser.save(); // Simpan newUser ke database
      u = { ...newUser.toObject(), id: newUser._id }; // Gunakan toObject() untuk menghindari masalah dengan Mongoose documents
    } else {
      u = { ...checkUser, id: checkUser._id };
    }
    // check lokasi
    if (!body.locationId) {
      return res.status(400).json({
        message: "Lokasi belum dipilih",
      });
    }

    const checkLokasi = await location.findOne({
      _id: body.locationId,
    });

    if (!checkLokasi) {
      return res.status(400).json({
        message: "Lokasi tidak ditemukan",
      });
    }

    // check package
    const checkPackage = await package.findOne({
      _id: body.packageId,
    });

    if (!checkPackage) {
      return res.status(400).json({
        message: "Paket tidak ditemukan",
      });
    }

    let selectedItem = checkPackage.items.filter(
      (item) => item._id == body.itemId,
    );

    if (selectedItem.length == 0) {
      return res.status(400).json({
        message: "Paket Item tidak ditemukan",
      });
    }
    selectedItem = selectedItem[0];

    // format booking_date
    body.booking_date = moment(body.booking_date, "YYYY-MM-DD").format("YYYY-MM-DD");

    // create transaction
    const trxId = await generateTrxId(); // await the promise
    const persenDp = parseInt(body.dp);
    const userId = u.id

    const newTrx = new transaction({
      trxId: trxId,
      userId: userId || "-",
      locationId: checkLokasi._id,
      locationName: checkLokasi.name,
      packageId: body.packageId,
      packageName: checkPackage.name,
      packageItemName: selectedItem.name,
      packagePrice: body.harga_paket,
      muaShare: body.harga_paket_share*body.pax,
      name: body.name,
      phone: body.phone,
      email: body.email,
      address: body.alamat,
      pax: body.pax,
      totalHarga: body.harga_paket * body.pax,
      dp: persenDp,
      bookingNumber: 0,
      status: "PENDING",
      statusPayment: "DP_UNPAID",
      note: body.note,
      ref: body.ref,
      bookingDate: body.booking_date,
      bookingTime: body.booking_time,
      adminId: "",
      point: 1,
    });

    await newTrx.save();

    // create transaction pay
    if (persenDp !== 100) {
      // bayar dp
      const bayarDp = new payment({
        trxId: trxId,
        payId: generatePayId(),
        userId: userId || "-",
        paidAt: "",
        totalBayar: (body.harga_paket * body.pax * persenDp) / 100,
        totalHarga: body.harga_paket * body.pax,
        status: "UNPAID",
        percent: persenDp,
        isDp: true,
      });

      // pelunasan
      const pelunasan = new payment({
        trxId: trxId,
        payId: generatePayId(),
        userId: userId || "-",
        paidAt: "",
        totalBayar: (body.harga_paket * body.pax * (100 - persenDp)) / 100,
        totalHarga: body.harga_paket * body.pax,
        status: "UNPAID",
        percent: 100 - persenDp,
        isDp: false,
      });
      await bayarDp.save();
      await pelunasan.save();
    } else {
      // bayar full
      const bayarFull = new payment({
        trxId: trxId,
        payId: generatePayId(),
        userId: userId || "-",
        paidAt: "",
        totalBayar: body.harga_paket * body.pax,
        totalHarga: body.harga_paket * body.pax,
        status: "UNPAID",
        percent: 100,
        isDp: true,
      });
      await bayarFull.save();
    }
    if(body.share_wa) {
       const msg = `Halo, ${body.name}!\n\nTerima kasih telah memesan paket ${checkPackage.name} ${selectedItem.name} di ${checkLokasi.name}.\n\nDetail transaksi:\nID\n*${trxId}*\n\nPaket\n*${checkPackage.name} - ${selectedItem.name}*\n\nHarga\n*${toRp(selectedItem.price)}*\n\nJumlah\n*${body.pax} pax*\n\nTotal\n*${toRp(selectedItem.price * body.pax)}*\n\nDP (${persenDp}%) \n*${toRp(persenDp/100*(selectedItem.price * body.pax))}*.\n\nTerima kasih!\n\nDetail dan Pembayaran\n\n*https://hellobeauty.id/booking/${trxId}*`;
       await sendWhatsapp(body.phone, msg)
    }
    return res.status(200).json({
      message: "Order berhasil dibuat",
      data: newTrx,
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message,
    });
  }
};

const editOrder = async (req, res) => {
  try {
    const body = req.body;

    const checkTrx = await transaction.findOne({
      trxId: body.trxId,
    });

    if (!checkTrx) {
      return res.status(400).json({ message: "Transaksi tidak ditemukan" });
    }

    // if status==='DONE' or 'CANCEL', can't edit
    if (checkTrx.status === "DONE" || checkTrx.status === "CANCEL") {
      return res.status(400).json({
        message: "Transaksi sudah selesai atau dibatalkan",
      });
    }

    // check phone
    if (!body.phone) {
      return res.status(400).json({
        message: "Nomor telepon harus diisi",
      });
    }

    // check user
    const checkUser = await user.findOne({
      phone: body.phone,
    });

    if (!checkUser) {
      return res.status(400).json({
        message: "User tidak ditemukan",
      });
    }

    // check lokasi
    if (!body.locationId) {
      return res.status(400).json({
        message: "Lokasi belum dipilih",
      });
    }

    const checkLokasi = await location.findOne({
      _id: body.locationId,
    });

    if (!checkLokasi) {
      return res.status(400).json({
        message: "Lokasi tidak ditemukan",
      });
    }

    // format booking_date
    body.booking_date = moment(body.booking_date, "YYYY-MM-DD").format("YYYY-MM-DD");

    // update transaction
    checkTrx.name = body.name;
    checkTrx.phone = body.phone;
    checkTrx.email = body.email;
    checkTrx.address = body.address;
    checkTrx.locationId = checkLokasi._id;
    checkTrx.locationName = checkLokasi.name;
    checkTrx.bookingDate = body.booking_date;
    checkTrx.bookingTime = body.booking_time;
    checkTrx.note = body.note;

    await checkTrx.save();

    return res.status(200).json({
      message: "Order berhasil diubah",
      data: checkTrx,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Terjadi Kesalahan",
    });
  }
};

const selectMua = async (req, res) => {
  try {
    const body = req.body;
    const trxId = req.params.id;

    const checkTrx = await transaction.findOne({
      trxId: trxId,
    });
    if(!checkTrx){
      return res.status(400).json({
        message:"Transaksi tidak ditemukan"
      })
    }

    // search mua
    const checkMua = await mua.findOne({
      _id: body.muaId,
    });

    if (!checkMua) {
      return res.status(400).json({
        message: "MUA tidak ditemukan",
      });
    }

    // update transaction
    checkTrx.selectedMuaId = checkMua._id;
    checkTrx.selectedMuaName = checkMua.muaName;
    checkTrx.selectedMuaPhone = checkMua.muaPhone;

    await checkTrx.save();

    const msgToMua = `Halo, ${checkMua.muaName}!\n\nKamu terpilih untuk melayani booking dengan ID #${trxId}.\n\nSilakan cek detail booking di dashboard kamu ya.\n\nTerima kasih!`;
    await sendWhatsapp(checkMua.muaPhone, msgToMua);
    
    return res.status(200).json({
      message: "MUA berhasil dipilih",
      data: checkTrx,
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message,
    });
  }
}

const listBooking = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to the start of today

    const transactions = await transaction
      .find({ bookingDate: { $gte: today } })
      .sort({ bookingDate: 1 })
      .skip(skip)
      .limit(limit);

    return res.status(200).json({ message: "List order", data: transactions });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "Internal server error" });
  }
}

const getCheckin = async(req,res) => {
  try {
    
    const trxId = req.params.id
    console.log(trxId)
    const cek = await checkin.findOne({
      trxId: trxId
    })
    return res.status(200).json({
      message: "Checkin data",
      data: cek
    })
  } catch (error) {
    return res.status(500).json({
      message: error.message
    })
  }
}

const getCheckout = async(req,res) => {
  try {
    
    const trxId = req.params.id
    console.log(trxId)
    const cek = await checkout.findOne({
      trxId: trxId
    })
    return res.status(200).json({
      message: "Checkout data",
      data: cek
    })
  } catch (error) {
    return res.status(500).json({
      message: error.message
    })
  }
}

const checkoutManual = async(req,res) => {
  try {
    const trxId = req.params.id
    const checkTrx = await transaction.findOne({
      trxId: trxId
    })
    console.log(checkTrx);
    
    if(!checkTrx){
      return res.status(400).json({
        message:"Transaksi tidak ditemukan"
      })
    }

    if(!checkTrx.selectedMuaId){
      return res.status(400).json({
        message:"MUA belum dipilih"
      })
    }

    if(checkTrx.status === 'DONE'){
      return res.status(400).json({
        message:"Transaksi sudah selesai"
      })
    }

    if(!checkTrx.selectedMuaName){
      return res.status(400).json({
        message:"MUA belum dipilih"
      })
    }

    // insert checkout
    const checkoutSave = new checkout({
      muaId: checkTrx.selectedMuaId,
      muaName: checkTrx.selectedMuaName,
      pictureUrl: '',
      trxId: trxId,
      isManual: true
    })

    await checkoutSave.save();

    // update transaction
    await transaction.findOneAndUpdate(
      { trxId: trxId },
      { status: 'DONE' }
    );

    return res.status(201).json({
      message: 'Checkout berhasil'
    })

  } catch (error) {
    return res.status(500).json({
      message: error.message
    })
  }
}

module.exports = {
  listOrder,
  detailOrder,
  createOrder,
  selectMua,
  getCheckin,
  listBooking,
  getCheckout,
  checkoutManual,
  editOrder
};
