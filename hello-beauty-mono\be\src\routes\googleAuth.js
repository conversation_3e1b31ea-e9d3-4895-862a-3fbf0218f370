const express = require('express');
const router = express.Router();
const googleAuthController = require('../controllers/googleAuthController');
const authMiddleware = require('../middlewares/check-auth');

// Google OAuth routes for regular users
router.get('/google', googleAuthController.googleAuth);
router.get('/google/callback', googleAuthController.googleCallback);

// Update phone number for Google users
router.put('/update-phone', authMiddleware, googleAuthController.updatePhoneNumber);

module.exports = router;