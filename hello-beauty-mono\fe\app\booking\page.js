"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { api } from "../_helper/api";
import convertToK from "../_helper/convertok";
import { Icon } from "@iconify/react";

export default function Home() {
  const [location, setLocation] = useState("");
  const [listLocation, setListLocation] = useState([]);
  const [listPackage, setListPackage] = useState([]);
  const [packageType, setPackageType] = useState("");
  const [loadingLocation, setLoadingLocation] = useState(false);
  const [loadingPackage, setLoadingPackage] = useState(false);

  const getLocation = async () => {
    try {
      setLoadingLocation(true);
      const res = await api("GET", "/location");
      setLoadingLocation(false);
      setListLocation(res.data);
      if (res.data.length > 0) {
        setLocation(res.data[0]._id);
      }
    } catch (error) {
      setLoadingLocation(false);
      console.log(error);
    }
  };

  const getPackages = async () => {
    try {
      setLoadingPackage(true);
      const res = await api("GET", "/package");
      setLoadingPackage(false);
      setListPackage(res.data);
      
      // Get packageId from URL
      const urlParams = new URLSearchParams(window.location.search);
      const packageId = urlParams.get('packageId');
      
      if (packageId && res.data.length > 0) {
        setPackageType(packageId);
      } else if (res.data.length > 0) {
        setPackageType(res.data[0]._id);
      }
    } catch (error) {
      setLoadingPackage(false);
      console.log(error);
    }
  };

  useEffect(() => {
    Promise.all([getLocation(), getPackages()]);
  }, []);

  return (
    <div className="max-w-[480px] mx-auto">
      <div className="h-14 flex items-center px-4">
        <Link href="/">
          <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link>
        <img src="/logo.png" alt="Logo" className="mx-auto h-8" />
      </div>
      <div>
        <img
          src="/images/images-3.jpeg"
          className="w-full z-50"
          data-aos="fade-up"
        />
      </div>

      <div className="p-4 -mt-10 relative z-50">
        <div
          data-aos="fade-up"
          className="w-full p-3 border flex gap-1 items-center bg-white rounded-lg relative"
        >
          <img src="/icons/form-map.svg" />
          <select
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="w-full focus:outline-none"
          >
            <option selected disabled>
              Pilih Lokasi
            </option>
            {listLocation.map(
              (item, index) =>
                // show only isActive location
                item.isActive && (
                  <option key={index} value={item._id}>
                    {item.name}
                  </option>
                ),
            )}
          </select>
        </div>
      </div>
      {loadingPackage && (
        <div className="p-4 text-center">
          <Icon
            icon="svg-spinners:180-ring-with-bg"
            className="text-4xl mx-auto text-hb-pink"
          />
        </div>
      )}
      <div className="px-4 -mt-10 dot-bg" data-aos="fade-up">
        <h3 className="text-2xl font-semibold text-center mb-6 pt-12">
          Paket Makeup<span className="text-hb-pink">.</span>
        </h3>

        <div
          data-aos="fade-up"
          className="grid cursor-pointer bg-white grid-cols-3 gap-2 p-3 rounded-t-xl border"
        >
          {listPackage.map((item, index) => (
            <div
              key={index}
              className={`text-center py-2 rounded-xl ${packageType === item._id ? "bg-hb-pink-light-2 text-hb-pink" : ""}`}
              onClick={() => {
                setPackageType(item._id);
                window.history.pushState({}, '', `?packageId=${item._id}`);
              }}
            >
              {item.name}
            </div>
          ))}
        </div>
        <div className="p-6 bg-white rounded-b-xl border-b border-x">
          <div className="grid grid-cols-1 gap-2">
            
             <Link href={`/portofolio?packageId=${packageType}`} className="flex gap-1 text-hb-pink justify-end items-center"> 
                      <span className="text-xs">
                        Lihat Portofolio
                      </span>
                      <Icon icon="mingcute:arrow-right-up-circle-line" className="text-xl"/>
                      </Link>
            {listPackage.map(
              (item, index) =>
                item._id === packageType &&
                item.items.map((price, index) => (
                  <Link
                    key={index}
                    href={`/booking/form?packageId=${item._id}&itemId=${price._id}&locationId=${location}`}
                    className="border border-hb-pink rounded-lg flex items-center cursor-pointer hover:bg-hb-pink-light-2"
                  >
                    <h4 className="p-3 flex items-center gap-1 text-xs text-gray-700 w-8/12">
                      {/* <img src="/icons/coin.svg" className="h-4" /> */}
                      {price.name}
                    </h4>
                    <div className="text-hb-pink w-4/12 text-end pr-4 font-semibold">
                      {convertToK(price.price)}
                    </div>
                  </Link>
                )),
            )}
            {/* <div className="border border-hb-pink rounded-lg  grid grid-cols-2">
        <h4 className="p-3 flex items-center gap-1">
          <img src="/icons/coin.svg" className="h-4" />
        Basic
        </h4>
        <div className="bg-hb-pink p-3 text-white rounded-r-md">
          1200K
        </div>
      </div>
        <div className="border border-hb-pink rounded-lg  grid grid-cols-2">
        <h4 className="p-3 flex items-center gap-1">
          <img src="/icons/coin.svg" className="h-4" />
        Wedding
        </h4>
        <div className="bg-hb-pink p-3 text-white rounded-r-md">
          4500K
        </div>
      </div>
        <div className="border border-hb-pink rounded-lg  grid grid-cols-2">
        <h4 className="p-3 flex items-center gap-1">
          <img src="/icons/coin.svg" className="h-4" />
        Birthday
        </h4>
        <div className="bg-hb-pink p-3 text-white rounded-r-md">
          1000K
        </div>
      </div> */}
          </div>
        </div>
      </div>

      <div className="p-4 w-full">
        <Link
          href={`/booking/form?locationId=${location}&packageId=${packageType}`}
          className="btn-primary flex justify-center text-center"
        >
          ISI FORM BOOKING
        </Link>
      </div>
    </div>
  );
}
