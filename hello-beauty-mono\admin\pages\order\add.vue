<template>
  <div>
    <div  class="p-3 grid md:grid-cols-1 grid-cols-1 gap-3">
      <div class="bg-white rounded-lg border relative w-full">
        <div class="p-4 flex items-center justify-between">
          <div class="flex items-center" @click.prevent="router.push('/order')">
            <icon
              class="cursor-pointer text-xl"
              name="bitcoin-icons:arrow-left-filled"
              @click="router.go(-1)"
            />
            <h1 class="font-bold ml-2">Tambah Order</h1>
          </div>
        </div>


        <form class="px-4 pb-4" @submit.prevent="saveOrder">
          <div class="mb-4">
            <h3 class="font-semibold ">
              Customer
            </h3>
            <div class="grid grid-cols-2 gap-3 mt-2">
              <div class="">
                <label class="text-sm text-gray-600 block">
                  Nama
                </label>
                <input v-model="form.name" required :disabled="loadingForm" class="form-input">
              </div>
               <div class="">
                <label class="text-sm text-gray-600 block">
                  Email
                </label>
                <input v-model="form.email" required :disabled="loadingForm" type="email" class="form-input">
              </div>
               <div class="">
                <label class="text-sm text-gray-600 block">
                  Nomor Whatsapp
                </label>
                <input v-model="form.phone" required :disabled="loadingForm" class="form-input">
                <span class="text-xs text-gray-600">* Pastikan Nomor Whatsapp benar</span>
              </div>
            </div>
          </div>

           <div class="py-4 border-t">
            <h3 class="font-semibold ">
              Booking Detail
            </h3>
            <div class="grid grid-cols-2 gap-3 mt-2">
              <div class="">
                <label class="text-sm text-gray-600 block">
                  Tanggal Booking
                </label>
                <input v-model="form.booking_date" required :disabled="loadingForm" type="date" class="form-input">
              </div>
               <div class="">
                <label class="text-sm text-gray-600 block">
                  Jam Booking
                </label>
                <input v-model="form.booking_time" required :disabled="loadingForm" type="time" class="form-input">
              </div>
               <div class="">
                <label class="text-sm text-gray-600 block">
                 Alamat
                </label>
                <input v-model="form.alamat" required :disabled="loadingForm" class="form-input">
              </div>
               <div class="">
                <label class="text-sm text-gray-600 block">
                 Lokasi
                </label>
                <select v-model="form.locationId" required :disabled="loadingForm" class="form-input">
                  <option value="" selected disabled>Pilih Lokasi</option>
                  <option v-for="item in listLocation" :key="item.id" :value="item._id">{{ item.name }}</option>
                </select>
              </div>
               <div class="col-span-2">
                <label class="text-sm text-gray-600 block">
                 Note
                </label>
                <textarea v-model="form.note" required :disabled="loadingForm" class="form-input"/>
              </div>

              
            </div>
          </div>

           <div class="py-4 border-t">
            <h3 class="font-semibold ">
              Paket
            </h3>
            <div class="grid grid-cols-3 gap-3 mt-2">
               <div class="">
                <label class="text-sm text-gray-600 block">
                 Paket
                </label>
                <select v-model="form.packageId" required :disabled="loadingForm" class="form-input" @change.prevent="setPaket()">
                  <option value="" selected disabled>Pilih Paket</option>
                  <option v-for="item in listPaket" :key="item._id" :value="item._id">{{ item.name }}</option>
                </select>
              </div>
                <div class="">
                <label class="text-sm text-gray-600 block">
                 Jenis
                </label>
                <select v-model="form.itemId" required :disabled="loadingForm" class="form-input">
                  <option value="" selected disabled>Pilih Jenis</option>
                  <option v-for="item in listItem" :key="item.id" :value="item._id">{{ item.name }}</option>
                </select>
              </div>
                <div class="">
                <label class="text-sm text-gray-600 block">
                  Jumlah Pax
                </label>
                <input v-model="form.pax" required :disabled="loadingForm" min="1" max="30" type="number" class="form-input">
              </div>
            </div>
          </div>

          <div class="py-4 border-t">
            <h3 class="font-semibold ">
              Harga
            </h3>
            <div class="grid grid-cols-3 gap-3 mt-2">
              <div class="">
                <label class="text-sm text-gray-600 block">
                  Harga Paket / Pax
                </label>
                <rupiah-input v-model="form.harga_paket" required :disabled="loadingForm" class="form-input"/>
              </div>
              <div class="">
                <label class="text-sm text-gray-600 block">
                  Sharing MUA / Pax
                </label>
                <rupiah-input v-model="form.harga_paket_share" required :disabled="loadingForm" class="form-input"/>
              </div>
               <div class="">
                <label class="text-sm text-gray-600 block">
                  Down Payment
                </label>
                <select v-model="form.dp" required type="time" class="form-input">
                  <option value="">Pilih DP</option>
                  <option value="25">30%</option>
                  <option value="50">50%</option>
                  <option value="100">100%</option>
                </select>
              </div>
              
            </div>
          </div>
          <div>
            <!-- rangkuman -->
            <div class="py-4 border-t">
              <div class="grid grid-cols-2 gap-3 mt-2">
                <div class="">
                  <label class="text-sm text-gray-600 block">
                    Total Harga
                  </label>
                  <div class="text-lg font-semibold">{{useRupiah(form.harga_paket * form.pax)}}</div>
                </div>
                <div class="">
                  <label class="text-sm text-gray-600 block">
                    Total DP
                  </label>
                  <div class="text-lg font-semibold">{{useRupiah(form.harga_paket * form.pax * (form.dp / 100))}} </div>
                </div>
              </div>
              </div>

          </div>

          <!-- input checkbox share to whatsapp customer -->
          <div class="py-4 border-t">
           <div class="flex">
             <label class="text-sm text-gray-600 block">
                  <input v-model="form.share_wa" type="checkbox" class="form-checkbox">
                   Share Booking to Whatsapp Customer
                </label>
              </div>
            </div>  

          <button class="btn-primary">Simpan</button>
        </form>
      </div>
    </div>
  </div>
</template>
<script setup>
const router = useRouter()
const { $toast } = useNuxtApp()
const form = ref({
  locationId: '',
  packageId: '',
  itemId: '',
  pax:1,
  harga_paket: 0,
  harga_paket_share: 0,
  dp: '',
})

const selectedPackage = ref({})

const setPaket = () => {
  selectedPackage.value = listPaket.value.find(item => item._id === form.value.packageId)
}

const selectedItem = computed(() => {
  if(!selectedPackage.value) return []
  if(!form.value.itemId) return []
  return listItem.value.find(item => item._id === form.value.itemId)
})

const listLocation = ref([]);
const loadingLocation = ref(false);
const listPaket = ref([]);


watch(selectedPackage, () => {
  form.value.itemId = ''
}, {deep: true})

watch(selectedItem, () => {
  form.value.harga_paket = selectedItem.value.price
  form.value.harga_paket_share = selectedItem.value.priceShare
}, {deep: true})


const listItem = computed(()=>{
  // filter by form.packageId
  const p = listPaket.value.filter(item => item._id === form.value.packageId)
  if(p[0]) {
    return p[0].items
  } else {
    return []
  }
})

const getLocation = async () => {
  try {
    loadingLocation.value = true;
    loadingLocation.value = [];
    const { data } = await adminGet(`/location?page=1&limit=999`);
    loadingLocation.value = false;
    listLocation.value = data.data;
 
  } catch (error) {
    loadingLocation.value = false;
  }
};

const getPaket = async () => {
  try {
    listPaket.value = [];
    const { data } = await adminGet(`/package?page=1&limit=999`);
    listPaket.value = data.data;
  } catch (error) {
    console.log(error);
    
  }
};

const loadingForm = ref(false)

const saveOrder = async () => {
  try {
    loadingForm.value = true
    const res = await adminPost('/order', form.value)
    loadingForm.value = false
    $toast.success(res.data.message)
    router.push('/order')
  } catch (error) {
    console.log(error);
  }
}

onMounted(() => {
  Promise.all([getLocation(), getPaket()])
});
</script>
