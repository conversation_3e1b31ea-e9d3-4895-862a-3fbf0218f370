const multer = require('multer');
const path = require('path');
const moment = require('moment');
const fs = require('fs');
const {decodeJwtClient} = require('../../helper/jwt')
const checkin = require('../../models/checkin');
const transaction = require('../../models/transaction');

const checkFileType = (file, cb) => {
  const filetypes = /jpeg|jpg|png|gif|pdf/;
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = filetypes.test(file.mimetype);
  

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb('Error: Images only!');
  }
}

const uploadCheckIn = async (req,res) => {
  try{
    const auth =  req.headers.authorization
    const u = await decodeJwtClient(auth)

    // check dulu
  
    const cekCheckin = await checkin.find({
      muaId: u.id,
      trxId: req.params.trxId
    })

    if(cekCheckin.length) {
      return res.status(400).json({
        message:"Sudah Check-in sebelumnya !"
      })
    }


    const currentMonthYear = moment().format('MM-YYYY');
    const uploadDir = `./src/public/uploads/${currentMonthYear}/mua-checkin/${u.id}`;
    if (!fs.existsSync(uploadDir)){
      fs.mkdirSync(uploadDir, { recursive: true,mode: 0o777 });
    }

    // check filesize 
    
    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, uploadDir)
      },
      filename: function (req, file, cb) {
        cb(null, file.fieldname + '-' + Date.now() + path.extname(file.originalname))
      }
    })
  
  const upload = multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
      checkFileType(file, cb);
    }
  }).single('file');
  
  upload(req, res, async (err) => {
    if (err) {
      res.status(500).json({
        message: 'Internal server error',
        error: err.message
      });
    } else {
      const { filename } = req.file;
      const fullUrl = `https://assets.hellobeauty.id/uploads/${currentMonthYear}/mua-checkin/${u.id}/${filename}`;
      // const currentPath = path.replace('src/public', '');

      // insert checkIn
      // other params is trxId, checkInTime
      const checkInSave = new checkin({
        muaId: u.id,
        muaName: u.name,
        pictureUrl: fullUrl,
        trxId: req.body.trxId
      })
      await checkInSave.save();

      // update transaction
      await transaction.findOneAndUpdate(
        { trxId: req.body.trxId },
        { status: 'ON_PROGRESS' }
      );

      return res.status(201).json({
        message: 'Uploaded successfully'
      });
    }
  });
  } catch(err) {
    res.status(500).json({
      message: 'Internal server error',
      error: err.message
    });
  }
  
}

const checkCheckin = async (req,res) => {
  try {
    const auth =  req.headers.authorization
    const u = await decodeJwtClient(auth)

    const findCheckin = await checkin.find({
      muaId: u.id,
      trxId: req.query.trxId
    })

    if(!findCheckin.length) {
      return res.status(404).json({
        message:"Data Checkin tidak ditemukan",
      })
    }
     return res.json({
        message:"Checkin Data",
        data: findCheckin[0]
      })

  } catch(err) {
     res.status(500).json({
      message: 'Internal server error',
      error: err.message
    });
  }
}




module.exports = {
  uploadCheckIn,
  checkCheckin
};
