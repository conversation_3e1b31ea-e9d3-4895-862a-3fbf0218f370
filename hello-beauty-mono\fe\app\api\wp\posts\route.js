import axios from 'axios';
import { NextResponse } from 'next/server';
import https from 'https';

const agent = new https.Agent({  
  rejectUnauthorized: false // Bypass certificate validation
});

export async function GET(req) {
  try {
    const response = await axios.get('https://daily.hellobeauty.id/wp-json/wp/v2/posts?_embed', { httpsAgent: agent });
    
    return NextResponse.json(response.data);
  } catch (error) {
    console.error('Error fetching data:', error);
    return NextResponse.json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}
