<template>
  <client-only>
    <VueSidePanel
      v-model="showPanel"
      hide-close-btn
      lock-scroll
      side="right"
      width="640px"
      @closed="$emit('closed')"
    >
      <div class="p-4">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-sm font-bold">History Pembatalan</h2>
        </div>
        <div v-if="loading" class="flex justify-center items-center">
          <LoaderFull />
        </div>
        <div v-else>
          <div v-if="histories.length === 0" class="text-center py-8">
            <icon name="mdi:history" class="text-gray-400 text-4xl mb-2" />
            <p class="text-gray-500 text-sm">Tidak ada riwayat pembatalan</p>
          </div>
          <div
            v-else
            v-for="history in histories"
            :key="history.id"
            class="mb-4 p2 border rounded-lg p-3"
          >
            <p class="text-sm font-bold">
              {{ history.muaId.name }} /
              {{ history.muaId.approval.profileName }}
            </p>
            <p class="text-sm text-red-500">Alasan : {{ history.alasan }}</p>
            <p class="text-sm text-gray-500">
              {{ useMoment(history.createdAt).format("DD MMM YYYY, HH:mm:ss") }}
            </p>
          </div>
        </div>
      </div>
    </VueSidePanel>
  </client-only>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import LoaderFull from "~/components/loader-full.vue";
import { useMoment } from "@/composables/useMoment";

const props = defineProps({
  show: Boolean,
  trxId: String,
});

const emit = defineEmits(["closed"]);

const showPanel = ref(false);
const loading = ref(false);
const histories = ref([]);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    if (val) {
      getHistory();
    }
  },
  { deep: true }
);

const getHistory = async () => {
  try {
    loading.value = true;
    const { data } = await adminGet(`/bid/history/${props.trxId}`);
    histories.value = data.data;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (props.show) {
    getHistory();
  }
});
</script>
