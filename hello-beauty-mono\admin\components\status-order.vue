<template>
  <div class="mb-2">
    <span
      :class="colorStatus"
      class="text-[10px] font-semibold px-2 py-1 rounded-full"
    >
      {{ changeText(props.status) }}
    </span>
  </div>
</template>

<script setup>
const props = defineProps({
  status: {
    type: String,
    required: true,
  },
});
// - Pending = PENDING
// - Pemilihan MUA = FIND_MUA
// - MUA terpilih = SELECTED_MUA, mua terpillih
// - On Progress = ON_PROGRESS, ketika MUA Checkin
// - Selesai = DONE, ketika MUA checkout

const changeText = (status) => {
  if (status === "PENDING") {
    return "PENDING";
  }
  if (status === "FIND_MUA") {
    return "Pemilihan MUA";
  }

  if (status === "SELECTED_MUA") {
    return "MUA terpilih";
  }

  if (status === "ON_PROGRESS") {
    return "On Progress";
  }

  if (status === "DONE") {
    return "Selesai";
  }

  return status;
};
const colorStatus = computed(() => {
  if (props.status === "PENDING") {
    return "text-yellow-600 bg-yellow-100";
  }
  if (props.status === "FIND_MUA") {
    return "text-blue-400 bg-blue-100";
  }

  if (props.status === "SELECTED_MUA") {
    return "text-blue-600 bg-blue-100";
  }

  if (props.status === "ON_PROGRESS") {
    return "text-purple-600 bg-purple-100";
  }

  if (props.status === "DONE") {
    return "text-green-600 bg-green-100";
  }

  return "text-gray-600 bg-gray-100";
});
</script>
