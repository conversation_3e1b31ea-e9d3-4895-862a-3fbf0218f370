const express = require('express');
const router = express.Router();
const googleAuthController = require('../controllers/googleAuthController');
const authMuaMiddleware = require('../middlewares/check-auth-mua');

// Google OAuth routes for MUA users
router.get('/google', googleAuthController.googleAuthMua);
router.get('/google/callback', googleAuthController.googleCallbackMua);

// Update phone number for Google MUA users
router.put('/update-phone', authMuaMiddleware, googleAuthController.updatePhoneNumber);

module.exports = router;