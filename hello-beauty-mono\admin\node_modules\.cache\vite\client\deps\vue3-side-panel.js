// ../node_modules/vue3-side-panel/dist/vue3-side-panel.esm.js
import { defineComponent, openBlock, createElementBlock, createElementVNode, ref, onMounted, onBeforeMount, onBeforeUnmount, watch, nextTick, computed, resolveComponent, createBlock, Teleport, normalizeClass, createVNode, Transition, withCtx, withDirectives, normalizeStyle, vShow, renderSlot, createCommentVNode } from "vue";

// ../node_modules/vue3-side-panel/node_modules/body-scroll-lock/lib/bodyScrollLock.esm.js
function _toConsumableArray(arr) {
  if (Array.isArray(arr)) {
    for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {
      arr2[i] = arr[i];
    }
    return arr2;
  } else {
    return Array.from(arr);
  }
}
var hasPassiveEvents = false;
if (typeof window !== "undefined") {
  passiveTestOptions = {
    get passive() {
      hasPassiveEvents = true;
      return void 0;
    }
  };
  window.addEventListener("testPassive", null, passiveTestOptions);
  window.removeEventListener("testPassive", null, passiveTestOptions);
}
var passiveTestOptions;
var isIosDevice = typeof window !== "undefined" && window.navigator && window.navigator.platform && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === "MacIntel" && window.navigator.maxTouchPoints > 1);
var locks = [];
var documentListenerAdded = false;
var initialClientY = -1;
var previousBodyOverflowSetting = void 0;
var previousBodyPosition = void 0;
var previousBodyPaddingRight = void 0;
var allowTouchMove = function allowTouchMove2(el) {
  return locks.some(function(lock) {
    if (lock.options.allowTouchMove && lock.options.allowTouchMove(el)) {
      return true;
    }
    return false;
  });
};
var preventDefault = function preventDefault2(rawEvent) {
  var e = rawEvent || window.event;
  if (allowTouchMove(e.target)) {
    return true;
  }
  if (e.touches.length > 1) return true;
  if (e.preventDefault) e.preventDefault();
  return false;
};
var setOverflowHidden = function setOverflowHidden2(options) {
  if (previousBodyPaddingRight === void 0) {
    var _reserveScrollBarGap = !!options && options.reserveScrollBarGap === true;
    var scrollBarGap = window.innerWidth - document.documentElement.clientWidth;
    if (_reserveScrollBarGap && scrollBarGap > 0) {
      var computedBodyPaddingRight = parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right"), 10);
      previousBodyPaddingRight = document.body.style.paddingRight;
      document.body.style.paddingRight = computedBodyPaddingRight + scrollBarGap + "px";
    }
  }
  if (previousBodyOverflowSetting === void 0) {
    previousBodyOverflowSetting = document.body.style.overflow;
    document.body.style.overflow = "hidden";
  }
};
var restoreOverflowSetting = function restoreOverflowSetting2() {
  if (previousBodyPaddingRight !== void 0) {
    document.body.style.paddingRight = previousBodyPaddingRight;
    previousBodyPaddingRight = void 0;
  }
  if (previousBodyOverflowSetting !== void 0) {
    document.body.style.overflow = previousBodyOverflowSetting;
    previousBodyOverflowSetting = void 0;
  }
};
var setPositionFixed = function setPositionFixed2() {
  return window.requestAnimationFrame(function() {
    if (previousBodyPosition === void 0) {
      previousBodyPosition = {
        position: document.body.style.position,
        top: document.body.style.top,
        left: document.body.style.left
      };
      var _window = window, scrollY = _window.scrollY, scrollX = _window.scrollX, innerHeight = _window.innerHeight;
      document.body.style.position = "fixed";
      document.body.style.top = -scrollY;
      document.body.style.left = -scrollX;
      setTimeout(function() {
        return window.requestAnimationFrame(function() {
          var bottomBarHeight = innerHeight - window.innerHeight;
          if (bottomBarHeight && scrollY >= innerHeight) {
            document.body.style.top = -(scrollY + bottomBarHeight);
          }
        });
      }, 300);
    }
  });
};
var restorePositionSetting = function restorePositionSetting2() {
  if (previousBodyPosition !== void 0) {
    var y = -parseInt(document.body.style.top, 10);
    var x = -parseInt(document.body.style.left, 10);
    document.body.style.position = previousBodyPosition.position;
    document.body.style.top = previousBodyPosition.top;
    document.body.style.left = previousBodyPosition.left;
    window.scrollTo(x, y);
    previousBodyPosition = void 0;
  }
};
var isTargetElementTotallyScrolled = function isTargetElementTotallyScrolled2(targetElement) {
  return targetElement ? targetElement.scrollHeight - targetElement.scrollTop <= targetElement.clientHeight : false;
};
var handleScroll = function handleScroll2(event, targetElement) {
  var clientY = event.targetTouches[0].clientY - initialClientY;
  if (allowTouchMove(event.target)) {
    return false;
  }
  if (targetElement && targetElement.scrollTop === 0 && clientY > 0) {
    return preventDefault(event);
  }
  if (isTargetElementTotallyScrolled(targetElement) && clientY < 0) {
    return preventDefault(event);
  }
  event.stopPropagation();
  return true;
};
var disableBodyScroll = function disableBodyScroll2(targetElement, options) {
  if (!targetElement) {
    console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");
    return;
  }
  if (locks.some(function(lock2) {
    return lock2.targetElement === targetElement;
  })) {
    return;
  }
  var lock = {
    targetElement,
    options: options || {}
  };
  locks = [].concat(_toConsumableArray(locks), [lock]);
  if (isIosDevice) {
    setPositionFixed();
  } else {
    setOverflowHidden(options);
  }
  if (isIosDevice) {
    targetElement.ontouchstart = function(event) {
      if (event.targetTouches.length === 1) {
        initialClientY = event.targetTouches[0].clientY;
      }
    };
    targetElement.ontouchmove = function(event) {
      if (event.targetTouches.length === 1) {
        handleScroll(event, targetElement);
      }
    };
    if (!documentListenerAdded) {
      document.addEventListener("touchmove", preventDefault, hasPassiveEvents ? { passive: false } : void 0);
      documentListenerAdded = true;
    }
  }
};
var enableBodyScroll = function enableBodyScroll2(targetElement) {
  if (!targetElement) {
    console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");
    return;
  }
  locks = locks.filter(function(lock) {
    return lock.targetElement !== targetElement;
  });
  if (isIosDevice) {
    targetElement.ontouchstart = null;
    targetElement.ontouchmove = null;
    if (documentListenerAdded && locks.length === 0) {
      document.removeEventListener("touchmove", preventDefault, hasPassiveEvents ? { passive: false } : void 0);
      documentListenerAdded = false;
    }
  }
  if (isIosDevice) {
    restorePositionSetting();
  } else {
    restoreOverflowSetting();
  }
};

// ../node_modules/vue3-side-panel/dist/vue3-side-panel.esm.js
var script$1 = defineComponent({
  name: "SidePanelCloseButton",
  emits: ["close"]
});
var _hoisted_1 = createElementVNode(
  "span",
  { class: "vsp-close__x" },
  null,
  -1
  /* HOISTED */
);
var _hoisted_2 = [
  _hoisted_1
];
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", {
    class: "vsp-close",
    onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$emit("close"))
  }, [..._hoisted_2]);
}
script$1.render = render$1;
script$1.__file = "src/components/SidePanelCloseButton.vue";
var script = defineComponent({
  name: "VueSidePanel",
  components: {
    SidePanelCloseButton: script$1
  },
  props: {
    idName: {
      type: String,
      default: "vsp-container"
    },
    hideCloseBtn: {
      type: Boolean,
      default: false
    },
    noClose: {
      type: Boolean,
      default: false
    },
    side: {
      type: String,
      validator: (value) => ["top", "right", "bottom", "left"].includes(value),
      default: "right"
    },
    rerender: {
      type: Boolean,
      default: false
    },
    zIndex: {
      type: [Number, String],
      default: "auto"
    },
    width: {
      type: String,
      default: "auto"
    },
    height: {
      type: String,
      default: "auto"
    },
    lockScroll: {
      type: Boolean,
      default: false
    },
    lockScrollHtml: {
      type: Boolean,
      default: true
    },
    modelValue: {
      type: Boolean,
      default: false,
      required: true
    },
    overlayColor: {
      type: String,
      default: "black"
    },
    overlayOpacity: {
      type: Number,
      default: 0.5
    },
    overlayDuration: {
      type: Number,
      default: 500
    },
    panelColor: {
      type: String,
      default: "white"
    },
    panelDuration: {
      type: Number,
      default: 300
    },
    transitionName: {
      type: String,
      default: void 0
    },
    headerClass: {
      type: String,
      default: ""
    },
    bodyClass: {
      type: String,
      default: ""
    },
    footerClass: {
      type: String,
      default: ""
    }
  },
  emits: ["update:modelValue", "closed", "opened"],
  setup(props, { emit, attrs }) {
    let teleportContainer = void 0;
    const panel = ref(null);
    const overlay = ref(null);
    const footer = ref(null);
    const header = ref(null);
    const body = ref(null);
    const footerHeight = ref(0);
    const bodyScrollHeight = ref(0);
    const headerHeight = ref(0);
    const panelHeight = ref(0);
    const windowHeight = ref(0);
    const zIndex = ref();
    const isBodyAlreadyLocked = ref(false);
    const calculateRightSize = async () => {
      if (window?.innerHeight > 0)
        windowHeight.value = window.innerHeight;
      footerHeight.value = footer.value ? footer.value.clientHeight : 0;
      headerHeight.value = header.value ? header.value.clientHeight : 0;
      bodyScrollHeight.value = body.value ? body.value.scrollHeight : 0;
      panelHeight.value = panel.value ? panel.value.clientHeight : 0;
    };
    const closePanel = () => emit("update:modelValue", false);
    const lockUnlockBodyScroll = (elem, lock) => {
      if (lock) {
        setTimeout(() => {
          disableBodyScroll(elem, { reserveScrollBarGap: true });
          if (props.lockScrollHtml)
            document.documentElement.style.overflow = "hidden";
        }, 0);
        return;
      }
      enableBodyScroll(elem);
      if (props.lockScrollHtml)
        document.documentElement.style.removeProperty("overflow");
    };
    const getMaxZIndex = () => Math.max(...Array.from(document.querySelectorAll("body *"), (el) => parseFloat(window.getComputedStyle(el).zIndex)).filter((zIndex2) => !Number.isNaN(zIndex2)), 0);
    onMounted(() => {
      zIndex.value = props.zIndex === "auto" ? getMaxZIndex() : props.zIndex;
    });
    onBeforeMount(() => {
      const alreadyCreatedTarget = document.getElementById(props.idName);
      if (!!alreadyCreatedTarget)
        return;
      teleportContainer = document.createElement("div");
      teleportContainer.setAttribute("id", props.idName);
      document.body.appendChild(teleportContainer);
    });
    onBeforeUnmount(() => {
      const { modelValue, lockScroll } = props;
      if (lockScroll && panel.value && modelValue)
        lockUnlockBodyScroll(panel.value, false);
      if (teleportContainer)
        document.body.removeChild(teleportContainer);
      window.removeEventListener("resize", calculateRightSize);
    });
    watch(() => [header.value, footer.value, props.height, props.width, props.side, props.modelValue], () => {
      nextTick(() => calculateRightSize());
    });
    watch(() => [props.modelValue, panel.value], (newP, oldP) => {
      const wasShown = oldP ? oldP[0] : false;
      const [isShown, panelEl] = newP;
      const isOpening = isShown;
      const isClosing = wasShown && !isShown;
      if (!panelEl)
        return;
      if (isOpening)
        isBodyAlreadyLocked.value = !!document.body.style.overflow;
      if (isShown) {
        if (props.lockScroll)
          lockUnlockBodyScroll(panelEl, true);
        calculateRightSize();
        window.addEventListener("resize", calculateRightSize);
        return;
      }
      if (!props.lockScroll || !isClosing || isBodyAlreadyLocked.value)
        return;
      setTimeout(() => {
        if (panelEl)
          lockUnlockBodyScroll(panelEl, false);
      }, props.panelDuration);
      window.removeEventListener("resize", calculateRightSize);
    }, { immediate: true });
    const bodyHeight = computed(() => {
      if (!panelHeight.value)
        return;
      const panelMaxHeight = bodyScrollHeight.value + headerHeight.value + footerHeight.value;
      let height = panelHeight.value - headerHeight.value - footerHeight.value;
      if (["top", "bottom"].includes(props.side) && props.height === "auto") {
        height = windowHeight.value >= panelMaxHeight ? bodyScrollHeight.value : windowHeight.value - headerHeight.value - footerHeight.value;
      }
      return height;
    });
    const overlayStyles = computed(() => ({
      zIndex: zIndex.value,
      animationDuration: `${props.overlayDuration}ms`,
      "--overlay-opacity": props.overlayOpacity,
      opacity: props.modelValue ? props.overlayOpacity : 0,
      backgroundColor: props.overlayColor,
      pointerEvents: !props.modelValue ? "none" : "all"
    }));
    const panelStyles = computed(() => ({
      width: ["left", "right"].includes(props.side) ? props.width : void 0,
      maxWidth: "100%",
      ...["top", "bottom"].includes(props.side) ? {
        // minHeight: props.height,
        height: props.height,
        maxHeight: "100%"
      } : {},
      zIndex: zIndex.value,
      backgroundColor: props.panelColor,
      animationDuration: `${props.panelDuration}ms`,
      ...Object.assign({}, attrs.style)
    }));
    return {
      body,
      panel,
      overlay,
      overlayStyles,
      header,
      footer,
      closePanel,
      panelStyles,
      bodyHeight
    };
  }
});
function render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_SidePanelCloseButton = resolveComponent("SidePanelCloseButton");
  return openBlock(), createBlock(Teleport, {
    to: `#${_ctx.idName}`
  }, [
    createElementVNode(
      "div",
      {
        class: normalizeClass(["vsp-wrapper", [_ctx.modelValue && "vsp-wrapper--active"]])
      },
      [
        createVNode(Transition, {
          name: "overlay",
          persisted: ""
        }, {
          default: withCtx(() => [
            withDirectives(createElementVNode(
              "div",
              {
                ref: "overlay",
                class: "vsp-overlay",
                style: normalizeStyle(_ctx.overlayStyles),
                onClick: _cache[0] || (_cache[0] = () => _ctx.noClose ? void 0 : _ctx.closePanel())
              },
              null,
              4
              /* STYLE */
            ), [
              [vShow, _ctx.modelValue]
            ])
          ]),
          _: 1
          /* STABLE */
        }),
        createVNode(Transition, {
          name: _ctx.transitionName || `slide-${_ctx.side}`,
          onAfterEnter: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("opened")),
          onAfterLeave: _cache[2] || (_cache[2] = ($event) => _ctx.$emit("closed"))
        }, {
          default: withCtx(() => [
            (_ctx.rerender ? _ctx.modelValue : true) ? withDirectives((openBlock(), createElementBlock(
              "div",
              {
                key: 0,
                ref: "panel",
                class: normalizeClass(["vsp", [`vsp--${_ctx.side}-side`, _ctx.$attrs.class]]),
                style: normalizeStyle(_ctx.panelStyles)
              },
              [
                !!_ctx.$slots.header ? (openBlock(), createElementBlock(
                  "div",
                  {
                    key: 0,
                    ref: "header",
                    class: normalizeClass([_ctx.headerClass, "vsp__header"])
                  },
                  [
                    renderSlot(_ctx.$slots, "header", { close: _ctx.closePanel })
                  ],
                  2
                  /* CLASS */
                )) : createCommentVNode("v-if", true),
                createElementVNode(
                  "div",
                  {
                    ref: "body",
                    class: normalizeClass([_ctx.bodyClass, "vsp__body"]),
                    style: normalizeStyle({ height: `${_ctx.bodyHeight}px` })
                  },
                  [
                    renderSlot(_ctx.$slots, "default", { close: _ctx.closePanel }),
                    !_ctx.hideCloseBtn ? (openBlock(), createBlock(_component_SidePanelCloseButton, {
                      key: 0,
                      onClose: _ctx.closePanel
                    }, null, 8, ["onClose"])) : createCommentVNode("v-if", true)
                  ],
                  6
                  /* CLASS, STYLE */
                ),
                !!_ctx.$slots.footer ? (openBlock(), createElementBlock(
                  "div",
                  {
                    key: 1,
                    ref: "footer",
                    class: normalizeClass([_ctx.footerClass, "vsp__footer"])
                  },
                  [
                    renderSlot(_ctx.$slots, "footer")
                  ],
                  2
                  /* CLASS */
                )) : createCommentVNode("v-if", true)
              ],
              6
              /* CLASS, STYLE */
            )), [
              [vShow, _ctx.rerender ? true : _ctx.modelValue]
            ]) : createCommentVNode("v-if", true)
          ]),
          _: 3
          /* FORWARDED */
        }, 8, ["name"])
      ],
      2
      /* CLASS */
    )
  ], 8, ["to"]);
}
script.render = render;
script.__file = "src/components/SidePanel.vue";
var VueSidePanelPlugin = {
  install(app) {
    app.component("VueSidePanel", script);
  }
};
export {
  script as VueSidePanel,
  VueSidePanelPlugin as default
};
//# sourceMappingURL=vue3-side-panel.js.map
