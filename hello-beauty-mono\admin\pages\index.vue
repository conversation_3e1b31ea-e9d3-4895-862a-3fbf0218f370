<template>
  <div class="p-4 bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto">
      <div class="flex justify-between items-center mb-8">
        <h1 class="font-bold text-2xl text-primary">Dashboard</h1>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div class="dashboard-card compact">
          <div class="dashboard-card-icon bg-primary-light">
            <Icon name="mingcute:user-4-fill" class="text-primary text-2xl" />
          </div>
          <div>
            <div class="dashboard-card-title">Total Customer</div>
            <div class="dashboard-card-value">
              {{ useAngka(summary.customer) }}
            </div>
          </div>
        </div>
        <div class="dashboard-card compact">
          <div class="dashboard-card-icon bg-primary-light">
            <Icon name="tabler:flower" class="text-primary text-2xl" />
          </div>
          <div>
            <div class="dashboard-card-title">Total MUA</div>
            <div class="dashboard-card-value">{{ useAngka(summary.mua) }}</div>
          </div>
        </div>
        <div class="dashboard-card compact">
          <div class="dashboard-card-icon bg-primary-light">
            <Icon name="solar:round-graph-bold" class="text-primary text-2xl" />
          </div>
          <div>
            <div class="dashboard-card-title">Transaksi Hari ini</div>
            <div class="dashboard-card-value">
              {{ useAngka(summary.transactionToday) }}
            </div>
          </div>
        </div>
        <div class="dashboard-card compact">
          <div class="dashboard-card-icon bg-primary-light">
            <Icon name="solar:round-graph-bold" class="text-primary text-2xl" />
          </div>
          <div>
            <div class="dashboard-card-title">Transaksi Kemarin</div>
            <div class="dashboard-card-value">
              {{ useAngka(summary.transactionYesterday) }}
            </div>
          </div>
        </div>
        <div class="dashboard-card compact">
          <div class="dashboard-card-icon bg-primary-light">
            <Icon name="ph:money-wavy-fill" class="text-primary text-2xl" />
          </div>
          <div>
            <div class="dashboard-card-title flex items-center gap-2">
              <span
                class="bg-primary-light text-xs px-2 py-1 rounded-full text-primary font-semibold"
              >
                {{ useAngka(summary.paymentToday) }}
              </span>
              Revenue Hari Ini
            </div>
            <div class="dashboard-card-value text-lg">
              {{ useRupiah(summary.paymentTodayAmount) }}
            </div>
          </div>
        </div>
        <div class="dashboard-card compact">
          <div class="dashboard-card-icon bg-primary-light">
            <Icon name="ph:money-wavy-fill" class="text-primary text-2xl" />
          </div>
          <div>
            <div class="dashboard-card-title flex items-center gap-2">
              <span
                class="bg-primary-light text-xs px-2 py-1 rounded-full text-primary font-semibold"
              >
                {{ useAngka(summary.paymentYesterday) }}
              </span>
              Revenue Kemarin
            </div>
            <div class="dashboard-card-value text-lg">
              {{ useRupiah(summary.paymentYesterdayAmount) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Statistic Section -->
      <div class="mb-8">
        <div class="section-title">Statistik</div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <statistic-chart />
          <statistic-revenue />
        </div>
      </div>

      <!-- Booking & Payment Section -->
      <div>
        <div class="section-title">Aktivitas Terbaru</div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <booking-today />
          <LatestPayment />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Dashboard",
  meta: [
    {
      name: "description",
      content: "Dashboard",
    },
    {
      name: "keywords",
      content: "Dashboard",
    },
  ],
});

const summary = ref({
  customer: 0,
  mua: 0,
  transactionToday: 0,
  transactionYesterday: 0,
  paymentToday: 0,
  paymentTodayAmount: 0,
  paymentYesterday: 0,
  paymentYesterdayAmount: 0,
});

const getSummary = async () => {
  const { data } = await adminGet("/statistics/summary");
  summary.value = data.data;
};

onMounted(() => {
  getSummary();
});
</script>

<style scoped>
.dashboard-card {
  @apply flex items-center gap-3 bg-white rounded-lg p-3;
}
.dashboard-card.compact {
  @apply shadow-none border border-gray-100;
}
.dashboard-card-icon {
  @apply h-9 w-9 flex items-center justify-center rounded-full bg-primary-light mr-2;
}
.dashboard-card-title {
  @apply text-xs font-medium text-gray-500 mb-0;
}
.dashboard-card-value {
  @apply text-lg font-bold text-gray-900;
}
.section-title {
  @apply text-sm font-semibold text-gray-600 mb-2;
}
</style>
