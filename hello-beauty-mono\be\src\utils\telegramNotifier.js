const axios = require('axios');
const logger = require('../config/logger');

/**
 * A utility to send notifications to a Telegram bot
 */
class TelegramNotifier {
  constructor() {
    this.botToken = process.env.TELEGRAM_BOT_TOKEN || '**********************************************';
    this.chatId = process.env.TELEGRAM_CHAT_ID || '215742545';
    this.enabled = !!this.botToken && !!this.chatId;
    
    if (!this.enabled) {
      logger.warn('TelegramNotifier: Bot token or chat ID not configured');
    }
  }

  /**
   * Send a message to Telegram
   * @param {string} message - The message to send
   * @param {Object} data - Optional data to include in the message
   * @returns {Promise<void>}
   */
  async sendMessage(message, data = {}) {
    if (!this.enabled) {
      logger.debug('TelegramNotifier: Notifications disabled, not sending message');
      return;
    }

    try {
      let formattedMessage = `🔔 *${message}*\n\n`;
      
      // Format data as key-value pairs if provided
      if (Object.keys(data).length > 0) {
        formattedMessage += '```\n';
        for (const [key, value] of Object.entries(data)) {
          const formattedValue = typeof value === 'object' 
            ? JSON.stringify(value, null, 2) 
            : value;
          formattedMessage += `${key}: ${formattedValue}\n`;
        }
        formattedMessage += '```';
      }

      const url = `https://api.telegram.org/bot${this.botToken}/sendMessage`;
      await axios.post(url, {
        chat_id: this.chatId,
        text: formattedMessage,
        parse_mode: 'Markdown'
      });
      
      logger.debug('TelegramNotifier: Message sent successfully');
    } catch (error) {
      logger.error('TelegramNotifier: Failed to send message', { error: error.message });
    }
  }

  /**
   * Send a payment notification to Telegram
   * @param {string} title - The notification title
   * @param {Object} paymentData - Payment data
   * @returns {Promise<void>}
   */
  async sendPaymentNotification(title, paymentData) {
    await this.sendMessage(`PAYMENT: ${title}`, paymentData);
  }

  /**
   * Send an error notification to Telegram
   * @param {string} title - The notification title
   * @param {Error|string} error - The error object or message
   * @param {Object} contextData - Additional context data
   * @returns {Promise<void>}
   */
  async sendErrorNotification(title, error, contextData = {}) {
    const errorData = {
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      ...contextData
    };
    
    await this.sendMessage(`❌ ERROR: ${title}`, errorData);
  }
}

// Singleton instance
const telegramNotifier = new TelegramNotifier();

module.exports = telegramNotifier;
