const multer = require("multer");
const payment = require("../../models/payment");
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const addSaldoReferral = require("../../helper/saldo-referral");
const transaction = require("../../models/transaction");
const User = require("../../models/user");
const mongoose = require('mongoose');

const listPay = async (req, res) => {
  try {
    const page = Math.max(1, parseInt(req.query.page) || 1);
    const limit = Math.max(1, parseInt(req.query.limit) || 10);
    const skip = (page - 1) * limit;

    const query = {};

    // Add status filter if provided
    if (req.query.status) {
      query.status = req.query.status;
    }

    if (req.query.search && typeof req.query.search === "string") {
      const searchRegex = { $regex: req.query.search, $options: "i" };

      const users = await User.find({
        $or: [
          { name: searchRegex },
          { phone: searchRegex },
          { email: searchRegex },
        ],
      }).select("_id");

      const userIds = users.map((user) => user._id);

      query.$or = [{ trxId: searchRegex }];
      if (userIds.length) {
        query.$or.push({ userId: { $in: userIds } });
      }
    }

    const sortBy = req.query.sort_by || "createdAt"; // default sorted by createdAt
    const sortOrder = req.query.sort === "asc" ? 1 : -1;

    const payments = await payment.find(query)
      .sort({ [sortBy]: sortOrder })
      .skip(skip)
      .limit(limit)
      .populate("userId", "name phone email")
      .lean(); // Gunakan lean() untuk mengembalikan objek plain JavaScript

    const result = payments.map((item) => ({
      ...item,
      name: item.userId?.name || null,
      phone: item.userId?.phone || null,
      email: item.userId?.email || null,
    }));

    return res.status(200).json({ message: "List payment", data: result });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: "Internal server error" });
  }
};



const detailPay = async (req, res) => {
  try {
    const id = req.params.id;
    //get list of transaction pay by trxId:id order by percent desc
    const transactionPays = await payment
      .find({ trxId: id })
      .sort({ percent: 1 });
    return res
      .status(200)
      .json({ message: "Detail transaksi", data: transactionPays });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};

const checkFileType = (file, cb) => {
  const filetypes = /jpeg|jpg|png|gif|pdf/;
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = filetypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb('Error: Images only!');
  }
}

const confirmPayment = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    const id = req.params.id;
    const checkPayment = await payment.findOne({ payId: id }).session(session);
    if (!checkPayment) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ message: "Payment not found" });
    }
    if (checkPayment.status === "PAID") {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ message: "Payment already PAID" });
    }

    // check payment by trxId
    const checkPaymentByTrxId = await payment.find({ trxId: checkPayment.trxId }).session(session);

    // Validasi pembayaran pelunasan
    if (checkPayment.isDp === false) {
      const checkPaymentDp = checkPaymentByTrxId.find(
        (item) => item.isDp === true && item.status.toUpperCase() === "PAID"
      );
      if (!checkPaymentDp) {
        await session.abortTransaction();
        session.endSession();
        return res.status(400).json({ message: "Bayar DP terlebih dahulu" });
      }
    }

    const currentMonthYear = moment().format('MM-YYYY');
    const uploadDir = `./src/public/uploads/${currentMonthYear}/payment`;

    if (!fs.existsSync(uploadDir)){
      fs.mkdirSync(uploadDir, { recursive: true, mode: 0o777 });
    }

    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, uploadDir)
      },
      filename: function (req, file, cb) {
        cb(null, file.fieldname + '-' + Date.now() + path.extname(file.originalname))
      }
    })
  
    const upload = multer({
      storage: storage,
      fileFilter: function (req, file, cb) {
        checkFileType(file, cb);
      }
    }).single('file');

    upload(req, res, async (err) => {
      let uploadedFilePath = null;
      try {
        if (err) {
          await session.abortTransaction();
          session.endSession();
          return res.status(500).json({
            message: 'Internal server error',
            error: err.message
          });
        } else {
          const { filename, path: filePath } = req.file;
          uploadedFilePath = filePath;
          const fullUrl = `https://assets.hellobeauty.id/uploads/${currentMonthYear}/payment/${filename}`;
          
          // Update payment status
          checkPayment.status = "PAID";
          checkPayment.buktiBayar = fullUrl;
          checkPayment.paidAt = new Date();
          await checkPayment.save({ session });

          // Tentukan status transaksi dan tambah saldo referral
          let statusPayment = 'DP_PAID';
          if (checkPayment.isDp === false || checkPayment.percent === 100) {
            statusPayment = 'PAID';
          }

          // Update status transaksi
          await transaction.findOneAndUpdate(
            { trxId: checkPayment.trxId },
            { statusPayment },
            { session }
          );

          // Tambah saldo referral jika perlu
          if (statusPayment === 'PAID') {
            try {
              await addSaldoReferral(checkPayment.trxId, session); // Pastikan fungsi ini support session
            } catch {
              if (uploadedFilePath && fs.existsSync(uploadedFilePath)) {
                fs.unlinkSync(uploadedFilePath);
              }
              await session.abortTransaction();
              session.endSession();
              return res.status(500).json({ message: "Gagal tambah saldo referral" });
            }
          }

          await session.commitTransaction();
          session.endSession();
          return res.status(200).json({
            message: "Payment confirmed",
            data: checkPayment,
          });
        }
      } catch (error) {
        if (uploadedFilePath && fs.existsSync(uploadedFilePath)) {
          fs.unlinkSync(uploadedFilePath);
        }
        await session.abortTransaction();
        session.endSession();
        return res.status(500).json({ message: error.message });
      }
    });

  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    // Hapus file jika sudah terupload
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    return res.status(500).json({ message: error.message });
  }
}

const successPayment = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const query = { status: "PAID" };

    const checkPayment = await payment.find(query)
      .sort({ PAIDAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const totalPayments = await payment.countDocuments(query);

    if (!checkPayment.length) {
      return res.status(400).json({ message: "Payment not found" });
    }

    return res.status(200).json({
      message: "Payment success",
      data: checkPayment,
      totalPages: Math.ceil(totalPayments / limit),
      currentPage: parseInt(page)
    });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
}


module.exports = {
  listPay,
  detailPay,
  confirmPayment,
  successPayment
};
