"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import moment from "@/app/_helper/moment";
import LoadingFull from "@/app/_component/loadingFull";
import { apiMua, apiMuaUpload, getAuth } from "@/app/_helper/api-mua";
import convertRp from "@/app/_helper/convertorp";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import { Icon } from "@iconify/react";
import ErrorSpan from "@/app/_component/errorSpan";

export default function DetailBid() {
  const [booking, setBooking] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [login, setLogin] = useState(false);
  const params = useParams();
  const [notToday, setNotToday] = useState(null)
  const [checkin, setCheckin] = useState(null)
  const [loadingUpload, setLoadingUpload] = useState(false)
  const [file, setFile] = useState(null)
  const [errorUpload, setErrorUpload] = useState('')
  const [dataCheckin, setDataCheckin] = useState({})
  const [checkout, setCheckout] = useState(false)
  const [dataCheckout, setDataCheckout] = useState({})

  const [showBid, setShowBid] = useState(false);

  const getBooking = async () => {
    try {
      setLoading(true);
      const res = await apiMua("GET", `/order/${params.id}`);
      setLoading(false);
      setBooking(res.data);
      checkCheckin()
    } catch (error) {
      console.log(error);
      setLoading(false);
      setError(error);
    }
  };

  const handleCheckIn = () => {
    // check time
    // jika waktu booking bukan hari ini
    const bookingDate = moment(booking.bookingDate).format('YYYY-MM-DD')
    const today = moment().format('YYYY-MM-DD')
    // if(bookingDate!==today) {
    //   console.log("not today")
    //   setNotToday(true)
    //   return
    // }

    setCheckin(true)


  }

  const handleCheckout = () => {
    setCheckout(true)
  }

  useEffect(() => {
    getBooking();
    const token = getAuth();
    if(token) {
      setLogin(true);
    } else {
      setLogin(false);
      setShowBid(true);
    }
    document.title = "Project Detail #" + params.id;
  }, []);

  const handleFile = (e) =>{
    setFile(e.target.files[0])
  }

  const checkCheckin = async() =>{
    try {
      const res = await apiMua("GET", `/checkin?trxId=${params.id}`);
      setDataCheckin(res.data)
      checkCheckout()
    } catch(e) {

    }
  }

  const checkCheckout = async() =>{
    try {
      const res = await apiMua("GET", `/checkout?trxId=${params.id}`);
      setDataCheckout(res.data)
    } catch(e) {

    }
  }
  

  const uploadBukti=async() => {
    try {
      setErrorUpload('')
      const formData = new FormData()
      formData.append('file', file)
      formData.append('trxId', params.id)
      setLoadingUpload(true)
      const res = await apiMuaUpload('/checkin/'+params.id, formData)
      setLoadingUpload(false)
      getBooking()
      setCheckin(false)
    } catch (error) {
      console.log(error);
      
      setLoadingUpload(false)
      setErrorUpload(error.message)
      
    }
  }

  const uploadBuktiCheckout = async() => {
    try {
      setErrorUpload('')
      const formData = new FormData()
      formData.append('file', file)
      formData.append('trxId', params.id)
      setLoadingUpload(true)
      const res = await apiMuaUpload('/checkout/'+params.id, formData)
      setLoadingUpload(false)
      getBooking()
      setCheckout(false)
    } catch (error) {
      console.log(error);
      
      setLoadingUpload(false)
      setErrorUpload(error.message)
      
    }
  }


  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4 justify-between  max-w-[480px]">
          <Link href={`/mua/`}>
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
        </div>
        <div>
          <h3 className="text-2xl font-semibold text-center mb-6">
            Project
            <br /> Detail<span className="text-hb-pink">.</span>
          </h3>
          {loading && <LoadingFull />}
          <div className="mt-4 p-3">
            {!loading && (
              <div className="px-3">
                <div className="mb-3">
                  <span className="text-xs">Booking ID</span>
                  <h4 className="text-sm font-semibold">#{booking.trxId}</h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Budget</span>
                  <h4 className="text-sm font-semibold">{convertRp(booking.muaShare||0)}</h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Paket</span>
                  <div>
                    <div className="flex gap-1 items-center text-hb-pink">
                      <img src="/icons/coin.svg" className="h-3" />
                      <span>
                      {booking.packageItemName}
                      </span>
                    </div>
                  </div>
                </div>
                 <div className="mb-3">
                  <span className="text-xs">Total Pax</span>

                  <div className="font-semibold text-sm">
                      {booking.pax} Orang
                    </div>
                    </div>
               
                <div className="mb-3">
                  <span className="text-xs">Waktu Booking</span>
                  <h4 className="text-sm font-semibold">
                    {moment(booking.bookingDate).format("dddd, DD MMM YYYY")},
                    Jam {booking.bookingTime}
                  </h4>
                </div>

                <div className="mb-3">
                  <span className="text-xs">Alamat</span>
                  <h4 className="text-sm font-semibold">
                    {booking.address}
                    <br />
                    {booking.locationName}
                  </h4>
                </div>
                {booking.note?.length > 0 && (
                  <div className="mb-3">
                    <span className="text-xs">Catatan</span>
                    <h4 className="text-sm font-semibold">{booking.note}</h4>
                  </div>
                )}

                {
              !dataCheckin.muaId && (
                  <div className="mt-4">
                  <button onClick={handleCheckIn} className="btn-primary">
                    Check In
                  </button>
                  <div className="bg-hb-pink-light-2 p-3 pt-6 -mt-4 text-sm rounded-b-xl text-hb-pink">
                   Lakukan Check-in ketika mulai mengerjakan Project ini ya.
                  </div>
                  </div>
              )
              }
               {
                dataCheckin.muaId && (
                   <div className="mb-3">
                    <span className="text-xs">Bukti Checkin</span>
                    <div>
                    <img src={dataCheckin.pictureUrl} className="h-40 w-auto"/>
                    </div>
                    {
                      !dataCheckout.muaId && (
                        <button onClick={handleCheckout} className="btn-primary mt-4">
                          Checkout
                        </button>
                      )
                    }
                  </div>
                )
               }
               {
                dataCheckout.muaId && (
                   <div className="mb-3">
                    <span className="text-xs">Bukti Checkout</span>
                    <div>
                    <img src={dataCheckout.pictureUrl} className="h-40 w-auto"/>
                    </div>
                  </div>
                )
               }
              
              </div>
            )}
          </div>
        </div>
      </div>

      <Modal
        open={notToday}
        onClose={() => {
          setNotToday(false);
        }}
        center
        classNames={{
          overlay: "",
          modal: "rounded-2xl",
        }}
      > 
      <div className="px-4 py-6">
        <Icon icon="lucide:calendar-x-2" className="text-hb-pink mx-auto text-5xl"/>
        <div className="text-center mt-4">
        Maaf, CheckIn hanya bisa dilakukan <br/>di tanggal Booking.
        </div>
      </div>
      </Modal>

      <Modal
        open={checkin}
        onClose={() => {
          setCheckin(false);
        }}
        center
        classNames={{
          overlay: "",
          modal: "rounded-2xl",
        }}
      > 
      <div className="p-3">
        <h1 className="font-semibold text-lg mb-4">
          Checkin Sekarang
        </h1>
        <div className="mb-8">
          <label className="text-sm text-gray-500 block">Bukti Foto</label>
          <input type="file" accept="image/*" id="bukti" onChange={handleFile}/>
        </div>

        {errorUpload && <ErrorSpan msg={errorUpload} />}
        <div className="flex justify-end">
          <button className="btn-primary" onClick={uploadBukti}>
            Simpan
          </button>
        </div>
      </div>
      </Modal>

      <Modal
        open={checkout}
        onClose={() => {
          setCheckout(false);
        }}
        center
        classNames={{
          overlay: "",
          modal: "rounded-2xl",
        }}
      > 
      <div className="p-3">
        <h1 className="font-semibold text-lg mb-4">
          Checkout Sekarang
        </h1>
        <div className="mb-8">
          <label className="text-sm text-gray-500 block">Bukti Foto</label>
          <input type="file" accept="image/*" id="bukti" onChange={handleFile}/>
        </div>

        {errorUpload && <ErrorSpan msg={errorUpload} />}
        <div className="flex justify-end">
          <button className="btn-primary" onClick={uploadBuktiCheckout}>
            Simpan
          </button>
        </div>
      </div>
      </Modal>
    </div>
  );
}
