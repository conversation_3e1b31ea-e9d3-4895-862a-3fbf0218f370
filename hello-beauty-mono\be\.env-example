PORT=8900
SECRET_JWT="mezhuxplcc9ssvtz7bkev2zoyw1zfi34" # secret key for jwt 32 characters
JWT_SECRET_MUA="muasecretkey123456789012345678" # secret key for MUA jwt 32 characters
MONGO_URI="mongodb+srv://yolkmonday:<EMAIL>/"
DB_NAME="hello-beauty"
URL_FRONTEND="http://localhost:3000"
FRONTEND_URL="http://localhost:3000"
BACKEND_URL="http://localhost:8900"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="59880936592-t63kqiaa3jnfh7g8aerrvm961v91nktt.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
SESSION_SECRET="xLLdjLEKfl5HRr"
JWT_SECRET_MUA="muasecretkey123456789012345678"

# Session Configuration (for production)
COOKIE_SECURE="false" # Set to "false" if having session issues in production without HTTPS 

# MIDTRANS
MIDTRANS_SERVER_KEY="SB-Mid-server-xLLdjLEKfl5HRrGi__xQrdHv"

# Mailjet Configuration
MAILJET_API_KEY=********************************
MAILJET_API_SECRET=********************************
MAILJET_SENDER_EMAIL=********************************
