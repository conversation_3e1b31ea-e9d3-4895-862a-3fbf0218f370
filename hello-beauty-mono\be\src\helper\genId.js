const transaction = require("../models/transaction");

const generateTrxId = async () => {
    // get last trxid
    const lastTrx = await transaction.findOne().sort({ _id: -1 }).exec();
    
    // get trxid and handle non-number cases
    let lastTrxIdNumber = parseInt(lastTrx?.trxId || "0");
    if (isNaN(lastTrxIdNumber)) {
        lastTrxIdNumber = 15102016;
    }

    // increment trxid
    return `${lastTrxIdNumber + 1}`;
}

module.exports = generateTrxId;
