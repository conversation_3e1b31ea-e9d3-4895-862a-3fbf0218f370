const mongoose = require("mongoose");

const transactionSchema = new mongoose.Schema(
  {
    trxId: {
      type: String,
      required: true,
    },
    userId: {
      type: String,
      required: true,
    },
    locationId: {
      type: String,
      required: true,
    },
    locationName: {
      type: String,
      required: true,
    },
    packageId: {
      type: String,
      required: true,
    },
    packageName: {
      type: String,
      required: true,
    },
    packageItemName: {
      type: String,
      required: true,
    },
    packagePrice: {
      type: Number,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    phone: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
    address: {
      type: String,
      required: true,
    },
    pax: {
      type: Number,
      required: true,
    },
    totalHarga: {
      type: Number,
      required: true,
    },
    note: {
      type: String,
    },
    status: {
      type: String,
      required: true,
    },
    statusPayment: {
      type: String,
      required: true,
      default:'DP_UNPAID'
    },
    dp: {
      type: Number,
      required: true,
    },
    bookingDate: {
      type: Date,
      required: true,
    },
    bookingTime: {
      type: String,
      required: true,
    },
    isOpen: {
      type: Boolean,
      default: false,
    },
    selectedMuaId: {
      type: String,
    },
    selectedMuaName: {
      type: String,
    },
    selectedMuaPhone: {
      type: String,
    },
    muaShare: {
      type: Number,
    },
    adminId: {
      type: String,
    },
    referralCode: {
      type: String,
    },
    komisiReferral: {
      type: Number
    },
    rating: {
      type: Number,
    },
    review: {
      type: String,
    },
    voucherCode: {
      type: String,
    },
    discountAmount: {
      type: Number,
      default: 0
    }
  },
  { timestamps: true },
);

module.exports = mongoose.model("transaction", transactionSchema);
