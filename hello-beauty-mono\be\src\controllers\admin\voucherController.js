const Voucher = require('../../models/voucher');
// Create a new voucher
const createVoucher = async (req, res) => {
  try {
    const { code, name, discountType, discountValue, minPurchase, maxDiscount, startDate, endDate, usageLimit, isActive } = req.body;

    const existingVoucher = await Voucher.findOne({ code });
    if (existingVoucher) {
      return res.status(400).json({ message: 'Kode voucher sudah digunakan' });
    }

    const voucher = new Voucher({
      code,
      name,
      discountType,
      discountValue,
      minPurchase,
      maxDiscount,
      startDate,
      endDate,
      usageLimit,
      isActive
    });

    await voucher.save();
    res.status(201).json(voucher);
  } catch (error) {
    res.status(500).json({ message: 'Error creating voucher', error: error.message });
  }
};

// Get all vouchers with pagination and filtering
const listVoucher = async (req, res) => {
  try {
    // with pagination, page and limit
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // create query object
    const query = {};

    if (req.query.search) {
      const search = req.query.search;
      query.$or = [
        { code: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } }
      ];
    }

    // filter by status
    if (req.query.status) {
      query.isActive = req.query.status === 'active';
    }

    // filter by date range
    if (req.query.startDate) {
      query.startDate = { $gte: new Date(req.query.startDate) };
    }
    if (req.query.endDate) {
      query.endDate = { $lte: new Date(req.query.endDate) };
    }

    // dynamic sorting
    const sortBy = req.query.sort_by || 'createdAt';
    const sortOrder = req.query.sort_order === 'desc' ? -1 : 1;

    const vouchers = await Voucher.find(query)
      .sort({ [sortBy]: sortOrder })
      .skip(skip)
      .limit(limit);

    return res.status(200).json({ 
      message: "List voucher", 
      data: vouchers 
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ 
      message: "Internal server error" 
    });
  }
};

// Get voucher by ID
const detailVoucher = async (req, res) => {
  try {
    const id = req.params.id;
    const voucher = await Voucher.findById(id);
    if (!voucher) {
      return res.status(404).json({
        message: "Voucher not found"
      });
    }
    return res.status(200).json({
      message: "Detail voucher",
      data: voucher
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

// Update voucher
const updateVoucher = async (req, res) => {
  try {
    const id = req.params.id;
    const body = req.body;
    const voucher = await Voucher.findById(id);
    
    if (!voucher) {
      return res.status(404).json({
        message: "Voucher not found"
      });
    }

    Object.assign(voucher, body);
    await voucher.save();
    
    return res.status(200).json({
      message: "Berhasil mengupdate voucher",
      data: voucher
    });
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({ 
        message: 'Kode voucher sudah digunakan' 
      });
    }
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

// Delete voucher
const deleteVoucher = async (req, res) => {
  try {
    const id = req.params.id;
    const voucher = await Voucher.findById(id);
    
    if (!voucher) {
      return res.status(404).json({
        message: "Voucher tidak ditemukan"
      });
    }

    await Voucher.deleteOne({ _id: id });
    return res.status(200).json({
      message: "Voucher berhasil dihapus"
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Terjadi kesalahan saat menghapus voucher"
    });
  }
};

// Toggle voucher status
const toggleVoucherStatus = async (req, res) => {
  try {
    const id = req.params.id;
    const voucher = await Voucher.findById(id);
    
    if (!voucher) {
      return res.status(404).json({
        message: "Voucher not found"
      });
    }

    voucher.isActive = !voucher.isActive;
    await voucher.save();
    
    return res.status(200).json({
      message: `Voucher berhasil ${voucher.isActive ? 'diaktifkan' : 'dinonaktifkan'}`,
      data: voucher
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

// Get voucher statistics
const getVoucherStats = async (req, res) => {
  try {
    const stats = await Voucher.aggregate([
      {
        $group: {
          _id: null,
          totalVouchers: { $sum: 1 },
          activeVouchers: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$isActive', true] },
                    { $lte: ['$startDate', new Date()] },
                    { $gte: ['$endDate', new Date()] }
                  ]
                },
                1,
                0
              ]
            }
          },
          expiredVouchers: {
            $sum: {
              $cond: [
                { $lt: ['$endDate', new Date()] },
                1,
                0
              ]
            }
          },
          totalUsage: { $sum: '$usedCount' }
        }
      }
    ]);

    return res.status(200).json({
      message: "Statistik voucher",
      data: stats[0] || {
        totalVouchers: 0,
        activeVouchers: 0,
        expiredVouchers: 0,
        totalUsage: 0
      }
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

module.exports = {
  listVoucher,
  detailVoucher,
  createVoucher,
  updateVoucher,
  deleteVoucher,
  toggleVoucherStatus,
  getVoucherStats
}; 
