"use client";
import LoadingFull from "@/app/_component/loadingFull";
import { api } from "@/app/_helper/api";
import convertRp from "@/app/_helper/convertorp";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

export default function Home() {
  const [listPayment, setListPayment] = useState([]);
  const [selectedPayment, setSelectedPayment] = useState("");
  const [loadingPayment, setLoadingPayment] = useState(true);
  const [errorPayment, setErrorPayment] = useState("");
  const [order, setOrder] = useState({});
  const [loadingOrder, setLoadingOrder] = useState(true);
  const [errorOrder, setErrorOrder] = useState("");
  const [pay, setPay] = useState([]);
  const [loadingPay, setLoadingPay] = useState(true);
  const [errorPay, setErrorPay] = useState("");
  const [token, setToken] = useState("");
  const params = useParams();
  const getListPayment = async () => {
    try {
      setLoadingPayment(true);
      setErrorPayment("");
      const res = await api("GET", "/payment");
      setLoadingPayment(false);
      setListPayment(res.data);
    } catch (error) {
      setErrorPayment("Terjadi kesalahan");
      setLoadingPayment(false);
    }
  };

  const getDetailOrder = async () => {
    try {
      setLoadingOrder(true);
      setErrorOrder("");
      const res = await api("GET", `/order/${params.id}`);
      setLoadingOrder(false);
      setOrder(res.data);
    } catch (error) {
      setErrorOrder("Terjadi kesalahan");
      setLoadingOrder(false);
      console.log(error);
    }
  };

  const getDetailPay = async () => {
    try {
      setLoadingPay(true);
      setErrorPay("");
      const res = await api("GET", `/pay/${params.id}`);
      setLoadingPay(false);
      setPay(res.data);
      // check if dp is paid
      if (res.data.length > 0) {
        const dp = res.data.find((item) => item.isDp);
        const pelunasan = res.data.find((item) => !item.isDp);
        if (dp.status === "UNPAID") {
          await createPay(dp.payId);
          return;
        }
        if (dp.status === "PAID" && pelunasan.status === "UNPAID") {
          await createPay(pelunasan.payId);
        }

        if(dp.status==="expired" || pelunasan.status==="expired") {
          await createPay(dp.payId);
          // reload
          await getDetailPay();
        }

        if(dp.status==="PAID" && pelunasan.status==="PAID") {
          // redirect
          window.location.href = `/payment/success`;
        }
      }

    
    } catch (error) {
      setErrorPay("Terjadi kesalahan");
      setLoadingPay(false);
      console.log(error);
    }
  };

  const createPay = async (id) => {
    try {
      if (token) return;
      const res = await api("GET", `/pay/create/${id}`);
      if (res.data) {
        setToken(res.data);
      }
    } catch (error) {}
  };

  const injectMidtrans = () => {
    const script = document.createElement("script");
    script.src = "https://app.sandbox.midtrans.com/snap/snap.js";
    script.setAttribute("data-client-key", "SB-Mid-client-9PZoCH9fwN9kFGxC");
    script.async = true;
    document.body.appendChild(script);
    script.onload = () => {
      console.log("script loaded");
    };
  };

  useEffect(() => {
    return () => {
      injectMidtrans();
      Promise.all([getListPayment(), getDetailOrder(), getDetailPay()]);
    };
  }, []);

  const dp = useMemo(() => {
    const searchDp = pay.find((item) => item.isDp);
    return searchDp;
  });

  const pelunasan = useMemo(() => {
    const searchPelunasan = pay.find((item) => !item.isDp);
    return searchPelunasan
  });

  const isDp = useMemo(() => {
    if(dp && dp.status === "UNPAID") {
      return true
    }

    if(dp && dp.status === "PAID" && pelunasan && pelunasan.status === "UNPAID") {
      return false
    }

    return false
  });

  const totalBayar = useMemo(() => {
    if(dp && pelunasan) {
      if (dp.status === "UNPAID") {
        return dp.totalBayar;
      }

      if(dp.status === "PAID" && pelunasan.status === "UNPAID") {
        return pelunasan.totalBayar;
      }
    }

    return 0
  });

  const handlePay = (e) => {
    try {
      snap.pay(token, {
        onSuccess: function (result) {
          console.log(result);
          console.log("success");
        },
        onPending: function (result) {
          console.log(result);
          console.log("PENDING");
        },
        onError: function (result) {
          console.log(result);
          console.log("error");
        },
        language: "id",
        uiMode: "auto",
      });
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      {(loadingPayment || loadingOrder || loadingPay) && <LoadingFull />}
     
      <div>
        <div className="h-14 flex items-center px-4">
          <Link href="/">
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
          <img src="/logo.png" alt="Logo" className="mx-auto h-8" />
        </div>

        <div className="mt-12">
          <h3 className="text-2xl font-semibold text-center mb-6">
             <span className="block">Pembayaran</span>
            {isDp ? "DP" : "Pelunasan"}
            <span className="text-hb-pink">.</span>
          </h3>
        </div>

        <div className="mt-12 text-center">
          <span className="text-gray-700">Nominal</span>
          <span className="block text-3xl font-semibold">
            {convertRp(totalBayar)}
          </span>
        </div>

         {errorOrder && (
        <div className="text-center text-red-500">{errorOrder}</div>
      )}

        {/* <div className="mt-16 p-3">
          {dp && (
            <div className="p-3 bg-white border border-hb-pink text-hb-pink rounded-xl">
              {dp.status === "PAID" && (
                <div className="font-semibold">
                  DP Sudah dibayar
                  <br />
                  Sisa Pembayaran {convertRp(pelunasan.totalBayar)}
                </div>
              )}
              Pelunasan dibayar sebelum H-1 dari Waktu Booking
            </div>
          )}
        </div> */}

        <div className="mt-16 px-6">
          <button
            onClick={() => {
              handlePay();
            }}
            className="btn-primary flex justify-center"
          >
            PAY NOW
          </button>
        </div>
      </div>
    </div>
  );
}
