const mongoose = require('mongoose');

const paymentMethodSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    code: {
        type: String,
        required: true
    },
    vendor: {
        type: String,
        required: true
    },
    isActive: {
        type: Boolean,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    adminPercentage: {
        type: Number,
        required: true
    },
    adminFlat: {
        type: Number,
        required: true
    },
    logo: {
        type: String,
        required: true
    },
}, { timestamps: true });

module.exports = mongoose.model('paymentMethod', paymentMethodSchema);
