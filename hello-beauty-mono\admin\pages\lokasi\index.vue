<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Lokasi</h1>
        <button
          class="btn-primary flex items-center"
          @click.prevent="showPanel = true"
        >
          <icon name="jam:plus" class="text-xl" />
          Tambah
        </button>
      </div>
      <!-- <div class="grid grid-cols-2 md:grid-cols-4 p-3 gap-2 mt-4">
        <select
v-model="search.status" type="text" class="p-2 border border-gray-200 rounded-lg"
          placeholder="Cari Nama...">
        <option value="" disabled selected>Pilih Status</option>
        <option value="publish">Publish</option>
        <option value="draft">Draft</option>
        </select>
           
        <div class="grid grid-cols-2 gap-2">
          <button
class="btn-primary" @click.prevent="getData()">Cari</button>
          <button
class="btn-secondary" @click.prevent="
  search.status = '',
  getData()">
            Reset
          </button>
        </div>

      </div> -->

      <table class="w-full text-sm mt-4">
        <thead>
          <tr class="bg-gray-100 font-semibold">
            <td class="p-2">
              <p>Nama Lokasi</p>
            </td>
            <td>Status</td>
            <td class="p-2">Waktu dibuat</td>
            <td />
          </tr>
        </thead>
        <tbody>
          <tr v-for="(d, i) in list" :key="i" class="hover:bg-gray-50 border-b">
            <td class="px-2 py-3">
              <p class="text-sm">
                {{ d.name }}
              </p>
            </td>

            <td class="px-2 py-3"><status-lokasi :status="d.isActive" /></td>

            <td class="px-2 py-3">
              <p class="text-sm">
                {{ useMoment(d.createdAt).format("DD MMM, HH:mm") }}
              </p>
            </td>
            <td>
              <div class="flex gap-2">
                <button
                  class="btn-secondary text-xs"
                  @click.prevent="(selectedLokasi = d), (showPanel = true)"
                >
                  <icon name="icon-park-twotone:edit" class="text-xs" />
                  Edit
                </button>
                <button
                  class="btn-danger text-xs"
                  @click.prevent="(hapusId = d), (showHapus = true)"
                >
                  <icon name="icon-park-twotone:delete" class="text-xs" />
                  Hapus
                </button>
              </div>
            </td>
          </tr>
        </tbody>

        <tbody v-if="errorMsg && !loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              <icon name="icon-park-twotone:data" class="text-2xl block" />
              {{ errorMsg }}
            </td>
          </tr>
        </tbody>

        <!-- tbody loding -->
        <tbody v-if="loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              Memuat
              <icon name="svg-spinners:3-dots-bounce" class="text-2xl" />
            </td>
          </tr>
        </tbody>
      </table>

      <div class="p-3">
        <div class="flex justify-end gap-2 items-center mx-auto">
          <button
            v-if="page > 1"
            class="btn-secondary"
            @click.prevent="page--, page < 1 ? (page = 1) : '', getData()"
          >
            Prev
          </button>
          <form @submit="getData()">
            <input
              v-model="page"
              class="text-sm py-2 w-10 text-center border rounded-lg"
              type="number"
            />
          </form>

          <button class="btn-secondary" @click.prevent="page++, getData()">
            Next
          </button>
        </div>
      </div>
      <form-lokasi
        :show="showPanel"
        :data="selectedLokasi"
        @closed="(showPanel = false), (selectedLokasi = {})"
        @refresh="(showPanel = false), (selectedLokasi = {}), getData()"
      />

      <hapus-lokasi
        :show="showHapus"
        :data="hapusId"
        @closed="(showHapus = false), (hapusId = '')"
        @refresh="(showHapus = false), (hapusId = ''), getData()"
      />
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Lokasi",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "Lokasi",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);
const search = ref({
  status: "",
});

const hapusId = ref("");
const showHapus = ref(false);
const selectedLokasi = ref({});
const showPanel = ref(false);

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(`/location?page=${page.value}&limit=10`);
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

onMounted(() => {
  getData();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
