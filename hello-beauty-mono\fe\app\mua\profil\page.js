"use client";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { apiMua, apiMuaUpload, apiUpload } from "@/app/_helper/api-mua";
import BottomMenu from "@/app/_component/mua/bottomMenu";
import { Modal } from "react-responsive-modal";
import "react-responsive-modal/styles.css";

export default function Profile() {
  const [isLogin, setIsLogin] = useState(false);
  const [loadingMe, setLoadingMe] = useState(true);
  const [user, setUser] = useState({});
  const [open, setOpen] = useState(false);
  const [newPhoto, setNewPhoto] = useState("");
  const [loadingPhoto, setLoadingPhoto] = useState(false);
  const [error, setError] = useState("");

  const checkLogin = async () => {
    try {
      setLoadingMe(true);
      const res = await apiMua("GET", "/me");
      setLoadingMe(false);
      setUser(res.data);
    } catch (error) {
      setLoadingMe(false);
      setIsLogin(false);
      window.location.href = "/login";
    }
  };

  const handlePhotoChange = async () => {
    setLoadingPhoto(true);
    setError("");
    try {
      const formData = new FormData();
      formData.append("file", newPhoto);

      await apiMuaUpload("/me/update-photo", formData)

      // Optionally, refresh user data after photo update
      checkLogin();
      setOpen(false); // Close modal only on success
    } catch (error) {
      console.error("Failed to update photo", error);
      setError("Failed to update photo. Please try again.");
    }
    setLoadingPhoto(false);
  };

  useEffect(() => {
    checkLogin();
  }, []);

  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
      
        <div className="pt-4">
          <h3 className="text-2xl font-semibold text-center mb-6">
            MUA Profile<span className="text-hb-pink">.</span>
          </h3>
        </div>

        {loadingMe && (
          <div className="text-center">
            <div className="animate-pulse bg-gray-200 h-20 w-20 rounded-full mx-auto mb-3"></div>
            <div className="animate-pulse bg-gray-200 h-5 w-20 rounded-full mx-auto mb-3"></div>
            <div className="animate-pulse bg-gray-200 h-5 w-20 rounded-full mx-auto mb-3"></div>
          </div>
        )}

        {!loadingMe && (
          <div>
            <div className="mb-8">
              <div className="text-center">
                <div onClick={() => setOpen(true)} className="cursor-pointer">
                  <img
                    src={user.photo || `https://ui-avatars.com/api/?name=${user.name}&background=FF5E5E`}
                    alt="Profile"
                    className="rounded-full mb-1 h-20 mx-auto"
                  />
                  <button className="text-hb-pink -mt-1 text-xs">Ubah Foto</button>
                </div>
                <h3 className="text-lg mt-3">{user.name}</h3>
                <p className="text-xs text-gray-500">{user.email}</p>
              </div>
            </div>

            <div className="p-3">
              <div className="bg-gray-50 rounded-xl text-sm p-3">
                <ul>
                  <li>
                    <Link href="/mua/profil/edit" className="py-4 border-b border-b-gray-200 flex items-center justify-between">
                      <div className="flex gap-2 items-center">
                        <Icon icon="fluent:person-24-regular" className="text-hb-pink text-xl" /> Edit Profile
                      </div>
                      <Icon icon="akar-icons:chevron-right" className="text-gray-800" />
                    </Link>
                  </li>
                  <li>
                    <Link href="/mua/profil/change-password" className="py-4 border-b border-b-gray-200 flex items-center justify-between">
                      <div className="flex gap-2 items-center">
                        <Icon icon="fluent:key-multiple-24-regular" className="text-hb-pink text-xl" /> Change Password
                      </div>
                      <Icon icon="akar-icons:chevron-right" className="text-gray-800" />
                    </Link>
                  </li>
                  <li>
                    <Link href="/mua/terms-and-condition" className="py-4 border-b-gray-200 flex items-center justify-between">
                      <div className="flex gap-2 items-center">
                        <Icon icon="fluent:book-24-regular" className="text-hb-pink text-xl" /> Term & Condition
                      </div>
                      <Icon icon="akar-icons:chevron-right" className="text-gray-800" />
                    </Link>
                  </li>
                </ul>
              </div>
              <div className="mt-14">
                <Link href="/mua/logout" className="btn-secondary w-full flex items-center justify-center">
                  Logout
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
      <BottomMenu/>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        center
        classNames={{
          overlay: "",
          modal: "rounded-2xl p-6",
        }}
      >
        <div className="text-gray-600">
          <div className="mt-6">
            <div>
              <label className="text-xs text-gray-500">Upload Foto Baru</label>
              <input
                type="file"
                onChange={(e) => setNewPhoto(e.target.files[0])}
                className="form-input text-sm"
                disabled={loadingPhoto}
              />
            </div>
            {error && <p className="text-red-500 text-xs mt-2">{error}</p>}
            <div className="flex w-full mt-4">
              <button
                onClick={() => handlePhotoChange()}
                className="btn-primary w-full flex text-center justify-center"
                disabled={loadingPhoto}
              >
                {loadingPhoto ? "Loading..." : "Ubah Foto"}
              </button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}
