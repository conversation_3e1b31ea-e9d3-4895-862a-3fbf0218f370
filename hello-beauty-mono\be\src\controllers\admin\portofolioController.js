const multer = require('multer');
const path = require('path');
const portofolio = require('../../models/portofolio');
const moment = require('moment');
const fs = require('fs');
const package = require('../../models/package');

const listPortofolio = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const portofolios = await portofolio.find().skip(skip).limit(limit).sort({ createdAt: -1 });
    return res.status(200).json({
      message: "List portofolio",
      data: portofolios
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const checkFileType = (file, cb) => {
  const filetypes = /jpeg|jpg|png|gif/;
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = filetypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb('Error: Images only!');
  }
}

const uploadPortofolio = (req,res) => {
  try{
    const currentMonthYear = moment().format('MM-YYYY');
    const uploadDir = `./src/public/uploads/${currentMonthYear}/`;

    if (!fs.existsSync(uploadDir)){
      fs.mkdirSync(uploadDir, { recursive: true,mode: 0o777 });
    }

    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, uploadDir)
      },
      filename: function (req, file, cb) {
        cb(null, file.fieldname + '-' + Date.now() + path.extname(file.originalname))
      }
    })
  
  const upload = multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
      checkFileType(file, cb);
    }
  }).single('file');
  
  upload(req, res, (err) => {
    if (err) {
      res.status(500).json({
        message: 'Internal server error',
        error: err.message
      });
    } else {
      const { packageId, packageName } = req.body;
      const { filename, path } = req.file;
      const fullUrl = `https://assets.hellobeauty.id/uploads/${currentMonthYear}/${filename}`;
      // src/public/uploads/10-2024/file-1729743781008.png
      // ubah jadi /uploads/10-2024/file-1729743781008.png
      const currentPath = path.replace('src/public', '');
      portofolio.create({
        fileName: filename,
        filePath: currentPath,
        fullUrl,
        packageId,
        packageName
      })
      .then((data) => {
        res.status(201).json({
          message: 'Portofolio uploaded successfully',
          data
        });
      })
      .catch((err) => {
        res.status(500).json({
          message: 'Internal server error',
          error: err.message
        });
      });
    }
  });
  } catch(err) {
    console.log(err);
    
    res.status(500).json({
      message: 'Internal server error',
      error: err.message
    });
  }
  
}

const updatePortofolio = async (req, res) => {
  try {
    const { images, packageId } = req.body;

    // Ensure package and portofolio models are correctly imported
    const searchPackage = await package.findById(packageId);
    if (!searchPackage) {
      return res.status(404).json({
        message: 'Paket tidak ditemukan'
      });
    }

    await portofolio.updateMany(
      { _id: { $in: images } },
      { $set: { packageId: packageId, packageName: searchPackage.name } }
    );

    return res.status(200).json({
      message: "Berhasil Update"
    });

  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: 'Internal server error'
    });
  }
}

const deletePortofolio = async (req, res) => {
  try {
    const { id } = req.params;
    const portofolioItem = await portofolio.findById(id);

    if (!portofolioItem) {
      return res.status(404).json({
        message: 'Portofolio not found'
      });
    }

    const filePath = path.join(__dirname, '../../public', portofolioItem.filePath);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    await portofolio.findByIdAndDelete(id);

    return res.status(200).json({
      message: 'Portofolio deleted successfully'
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: 'Internal server error'
    });
  }
}

module.exports = {
  uploadPortofolio,
  listPortofolio,
  updatePortofolio,
  deletePortofolio
};
