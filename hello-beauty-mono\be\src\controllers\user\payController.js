const generatePayId = require("../../helper/genPayId");
const payment = require("../../models/payment");
const paymentMethod = require("../../models/payment-method");
const transaction = require("../../models/transaction");
const { createSnapToken } = require("../../third-party/midtrans");
const { logFunction, logError, logSuccess } = require("../../utils/logger");

const createPay = async (req, res) => {
  const fnName = 'createPay';
  logFunction(fnName, { payId: req.params.id });

  try {
    const id = req.params.id;
    const body= req.body;
    const checkPayment = await payment.findOne({
      payId: id,
    });

    if (!checkPayment) {
      logFunction(fnName, { error: 'Payment not found', payId: id }, 'warn');
      return res.status(400).json({
        message: "Payment tidak ditemukan",
      });
    }
    // jika payment ada
    if (checkPayment.status == "PAID") {
      logFunction(fnName, { error: 'Payment already paid', payId: id }, 'warn');
      return res.status(400).json({
        message: "Payment sudah dibayar",
      });
    }

    // jika payment expired
    if (checkPayment.status == "expired") {
      logFunction(fnName, { info: 'Payment expired, generating new payId', payId: id });
      // update payment id
      checkPayment.status = "UNPAID";
      checkPayment.snapToken = null;
      checkPayment.payId = generatePayId();
      await checkPayment.save();
    }

    // jika snaptoken ada
    if (checkPayment.snapToken) {
      logSuccess(fnName, { payId: id, status: 'existing token' });
      return res.status(200).json({
        message: "Payment Token",
        data: checkPayment.snapToken,
      });
    }

    // jika snapToken kosong
    if (!checkPayment.snapToken) {
      const trx = await transaction.findOne({
        trxId: checkPayment.trxId,
      });
      if(!trx){
        logFunction(fnName, { error: 'Transaction not found', trxId: checkPayment.trxId }, 'warn');
        return res.status(400).json({
          message:"Transaksi tidak ditemukan"
        })
      }
      const checkPaymentMethod = await paymentMethod.findOne({
        code: body.paymentMethod,
      });

      if(!checkPaymentMethod) {
        logFunction(fnName, { error: 'Payment method not found', method: body.paymentMethod }, 'warn');
        return res.status(400).json({
          message: "Metode pembayaran tidak ditemukan",
        });
      }
      let product
      if(checkPayment.isDp && trx.dp !== 100){
        product = "DP "
      }else{
        product = "PL "
      }

      let countAdmin = checkPaymentMethod.adminFlat || 0;
      if(checkPaymentMethod.adminPercentage){
        const countPercentage = checkPayment.totalBayar * (parseInt(checkPaymentMethod.adminPercentage) / 100)
        countAdmin += countPercentage
      }

      product += trx.packageName + " "+trx.packageItemName.slice(0,8)+"...";
      const order = {
          user: {
            first_name: trx.name,
            phone: trx.phone,
            email: trx.email,
          },
          trx_id: trx.trxId,
          pay_id: checkPayment.payId,
          total_bayar: checkPayment.totalBayar+countAdmin,
          total_belanja: checkPayment.totalBayar,
          total_admin: countAdmin,
          is_dp: checkPayment.isDp?true:false,
          product_name: product,
          payment: checkPaymentMethod.code,
        }
      const snapToken = await createSnapToken(order);
      
      if(snapToken.error_messages){
        logFunction(fnName, { error: 'Midtrans error', messages: snapToken.error_messages }, 'warn');
        return res.status(400).json({
          message:snapToken.error_messages[0].message
        })
      }

      // save snapToken
      checkPayment.snapToken = snapToken.token
      checkPayment.paymentMethodId = checkPaymentMethod._id
      await checkPayment.save()
      
      logSuccess(fnName, { 
        payId: checkPayment.payId,
        trxId: trx.trxId,
        paymentMethod: checkPaymentMethod.code,
        amount: checkPayment.totalBayar+countAdmin
      });
      
      return res.status(200).json({
        message: "Payment Token",
        data: snapToken.token,
      });
    }
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: error.message,
    });
  }
};

const detailPay = async (req, res) => {
  const fnName = 'detailPay';
  logFunction(fnName, { payId: req.params.id });

  try {
    const id = req.params.id;
    const payments = await payment.findOne({ payId: id }).populate("paymentMethodId");
    
    if (!payments) {
      logFunction(fnName, { error: 'Payment not found', payId: id }, 'warn');
      return res.status(404).json({ message: "Payment tidak ditemukan" });
    }
    
    logSuccess(fnName, { payId: id, status: payments.status });
    return res.status(200).json({ message: "Detail Payment", data: payments });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({ message: error.message });
  }
}

const detailPayList = async (req, res) => {
  const fnName = 'detailPayList';
  logFunction(fnName, { trxId: req.params.id });

  try {
    const id = req.params.id;
    // get list of transaction pay by trxId:id order by percent desc
    const payments = await payment.find({ trxId: id })
    
    logSuccess(fnName, { 
      trxId: id,
      totalPayments: payments.length
    });
    
    return res.status(200).json({ message: "List payment", data: payments });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({ message: error.message });
  }
}

module.exports = {
  detailPay,
  createPay,
  detailPayList
}
