const { decodeJwtClient } = require("../../helper/jwt");
const transaction = require("../../models/transaction");
const user = require("../../models/user");
const { logFunction, logError, logSuccess } = require("../../utils/logger");

const addReview = async (req, res) => {
  const fnName = 'addReview';
  logFunction(fnName, { 
    userId: req.headers.authorization ? 'authenticated' : 'anonymous',
    bookingId: req.body.bookingId
  });

  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const body = req.body;

    if(!u){
      logFunction(fnName, { error: 'Invalid token' }, 'warn');
      return res.status(400).json({
        message: "Token tidak valid",
      });
    }

    // check body satu satu
    if(!body.bookingId){
      logFunction(fnName, { error: 'Booking ID required' }, 'warn');
      return res.status(400).json({
        message: "Order id tidak boleh kosong",
      });
    }

    if(!body.review){
      logFunction(fnName, { error: 'Review text required' }, 'warn');
      return res.status(400).json({
        message: "Review tidak boleh kosong",
      });
    }

    if(!body.rating){
      logFunction(fnName, { error: 'Rating required' }, 'warn');
      return res.status(400).json({
        message: "Rating tidak boleh kosong",
      });
    }

    // check user
    const checkUser = await user.findOne({
      _id: u.id,
    });

    if(!checkUser){
      logFunction(fnName, { error: 'User not found', userId: u.id }, 'warn');
      return res.status(400).json({
        message: "User tidak ditemukan",
      });
    }

    // check order
    const checkOrder = await transaction.findOne({
      trxId: body.bookingId,
    });

    if(!checkOrder){
      logFunction(fnName, { error: 'Order not found', bookingId: body.bookingId }, 'warn');
      return res.status(400).json({
        message: "Order tidak ditemukan",
      });
    }

    // pastikan order sudah selesai
    if(checkOrder.status !== "DONE"){
      logFunction(fnName, { 
        error: 'Order not completed', 
        bookingId: body.bookingId,
        currentStatus: checkOrder.status 
      }, 'warn');
      return res.status(400).json({
        message: "Order belum selesai",
      });
    }

    // simpan review
    await transaction.updateOne(
      { trxId: body.bookingId },
      {
        review: body.review,
        rating: body.rating
      }
    );

    logSuccess(fnName, { 
      userId: u.id,
      bookingId: body.bookingId,
      rating: body.rating
    });

    return res.status(200).json({
      message: "Review berhasil ditambahkan",
    });

  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Terjadi Kesalahan",
    });
  }
}

module.exports = {
  addReview
}
