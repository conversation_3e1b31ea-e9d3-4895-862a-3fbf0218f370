@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .text-label {
    @apply text-xs text-gray-600;
  }

  .btn-primary {
    @apply px-4 py-2 bg-primary text-sm rounded-lg text-white focus:outline-none;
  }

  .btn-danger {
    @apply px-4 py-2 bg-red-600 text-sm rounded-lg text-white focus:outline-none;
  }

   .btn-success {
   @apply px-4 py-2 bg-green-600 text-sm rounded-lg text-white focus:outline-none;
  }

  .btn-success-secondary {
    @apply px-4 py-2 bg-green-100 text-sm rounded-lg text-green-600 focus:outline-none;
  }

  .btn-secondary {
    @apply px-4 py-2 border bg-gray-50 rounded-md text-primary text-sm focus:outline-none;
  }

  .form-input {
    @apply p-2 text-sm border w-full rounded-md  block;
  }

  .tr {
    @apply border-b text-xs hover:bg-gray-100;
  }

  .td {
  }

  .grid-4 {
    @apply grid grid-cols-1 md:grid-cols-4 gap-2;
  }

  .flex-pagin {
    @apply mt-4 flex justify-center items-center gap-2;
  }

  .text-back {
    @apply text-lg font-bold mb-6 w-max hover:text-primary cursor-pointer;
  }

  .text-label {
    @apply text-xs text-gray-600;
  }

  .text-primary .gradient-primary {
    @apply bg-gradient-to-r from-violet-500 to-fuchsia-500;
  }
}

.mx-input {
  height: 40px !important;
}

/* hiden range input number */
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0 !important;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield !important;
}

.vs--searchable .vs__dropdown-toggle {
  border: solid 1px #E5E7EB !important;
  height: 38px !important;
}
