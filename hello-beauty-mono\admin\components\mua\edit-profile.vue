<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <form class="p-4" @submit.prevent="submit">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">Edit Profile</h2>
          </div>

          <div class="mt-4">
            <label class="text-label"><PERSON><PERSON></label>
            <input
              required
              type="text"
              v-model="form.name"
              class="form-input"
            />
          </div>

          <div class="mt-4">
            <label class="text-label">Nama Profile</label>
            <input
              required
              type="text"
              v-model="form.mua_name"
              class="form-input"
            />
          </div>

          <div class="mt-4">
            <label class="text-label">Email</label>
            <input
              required
              type="email"
              v-model="form.email"
              class="form-input"
            />
          </div>

          <div class="mt-4">
            <label class="text-label">Phone</label>
            <input
              required
              type="text"
              v-model="form.phone"
              class="form-input"
            />
          </div>

          <div class="mt-4">
            <label class="text-label">Paket</label>

            <select v-model="form.packageId" class="form-input">
              <option v-for="p in listPaket" :key="p._id" :value="p._id">
                {{ p.name }}
              </option>
            </select>
          </div>

          <div class="mt-4">
            <label class="text-label">Instagram</label>
            <input type="text" v-model="form.instagram" class="form-input" />
          </div>

          <div class="mt-4">
            <label class="text-label">Alamat</label>
            <textarea v-model="form.address" class="form-input"></textarea>
          </div>

          <div class="mt-4">
            <label class="text-label">Kota</label>

            <select v-model="form.locationId" class="form-input">
              <option
                v-for="location in listLocation"
                :key="location._id"
                :value="location._id"
              >
                {{ location.name }}
              </option>
            </select>
          </div>

          <div class="mt-4">
            <label class="text-label">Layanan</label>
            <textarea v-model="form.serviceType" class="form-input"></textarea>
            <small class="text-gray-500"
              >Pisahkan layanan dengan koma (,)</small
            >
          </div>
          <div class="mt-4">
            <label class="text-label">Lama Menjadi MUA</label>
            <input v-model="form.lamaMua" class="form-input" />
            <small>* Dalam tahun</small>
          </div>
          <div class="mt-4">
            <label class="text-label">Link Hello Beauty</label>
            <input v-model="form.linkHb" class="form-input" />
          </div>

          <div class="h-4" />
          <div v-if="errorMessage" class="text-red-500 text-sm mb-4">
            {{ errorMessage }}
          </div>
          <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>{{ loading ? "Saving" : "Save" }}</span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Cancel
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";

const emit = defineEmits(["closed", "refresh"]);
const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);
const form = ref({
  name: "",
  email: "",
  phone: "",
  instagram: "",
  address: "",
  locationName: "",
  serviceType: "",
  mua_name: "",
  locationId: "",
  packageId: "",
});
const loading = ref(false);

const listLocation = ref([]);
const loadingLocation = ref(false);

const errorMessage = ref("");

const getLocation = async () => {
  try {
    loadingLocation.value = true;
    const { data } = await adminGet(`/location?page=1&limit=999`);
    loadingLocation.value = false;
    listLocation.value = data.data;
    // selectedLocation.value = data.data[0].id;
  } catch (error) {
    loadingLocation.value = false;
  }
};

const listPaket = ref([]);

const getPaket = async () => {
  try {
    listPaket.value = [];
    const { data } = await adminGet(`/package?page=1&limit=999`);
    listPaket.value = data.data;
  } catch (error) {
    console.log(error);
  }
};

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    getLocation();
    getPaket();
    if (props.data) {
      console.log("props.data", props.data);

      form.value = {
        ...props.data,
        serviceType: props.data.serviceType
          ? props.data.serviceType.join(", ")
          : [],
        mua_name: props.data.profileName,
        locationId: props.data.locationid || props.data.locationId,
        packageId: props.data.packageid || props.data.packageId,
      };
    }
  },
  { immediate: true }
);

const route = useRoute();

const submit = async () => {
  try {
    loading.value = true;
    errorMessage.value = ""; // Clear previous error message
    const payload = {
      ...form.value,
      serviceType: form.value.serviceType.split(",").map((s) => s.trim()),
    };
    await adminPut(`/mua/${route.params.id}`, payload);
    loading.value = false;
    emit("refresh"); // Trigger parent to refresh data
    emit("closed");
  } catch (error) {
    loading.value = false;
    errorMessage.value = error.response?.data?.message || "An error occurred";
    console.error(error.response?.data?.message);
  }
};
</script>
