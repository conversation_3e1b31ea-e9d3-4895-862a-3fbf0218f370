const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const User = require('../models/user');
const Mua = require('../models/mua');

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, { id: user._id, type: user.userType });
});

// Deserialize user from session
passport.deserializeUser(async (obj, done) => {
  try {
    let user;
    if (obj.type === 'user') {
      user = await User.findById(obj.id);
    } else if (obj.type === 'mua') {
      user = await Mua.findById(obj.id);
    }
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Google Strategy for regular users
passport.use('google-user', new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: `${process.env.BACKEND_URL || 'http://localhost:8900'}/auth/google/callback`
}, async (accessToken, refreshToken, profile, done) => {
  try {
    // Check if user already exists with this Google ID
    let user = await User.findOne({ googleId: profile.id });
    
    if (user) {
      user.userType = 'user';
      return done(null, user);
    }

    // Check if user exists with same email
    user = await User.findOne({ email: profile.emails[0].value });
    
    if (user) {
      // Link Google account to existing user
      user.googleId = profile.id;
      user.profilePicture = profile.photos[0].value;
      await user.save();
      user.userType = 'user';
      return done(null, user);
    }

    // Create new user
    const newUser = new User({
      name: profile.displayName,
      email: profile.emails[0].value,
      googleId: profile.id,
      profilePicture: profile.photos[0].value,
      phone: '', // Will be required to fill later
      password: 'google_oauth' // Placeholder password for Google users
    });

    await newUser.save();
    newUser.userType = 'user';
    return done(null, newUser);
  } catch (error) {
    return done(error, null);
  }
}));

// Google Strategy for MUA users
passport.use('google-mua', new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: `${process.env.BACKEND_URL || 'http://localhost:8900'}/mua/auth/google/callback`
}, async (accessToken, refreshToken, profile, done) => {
  try {
    // Check if MUA already exists with this Google ID
    let mua = await Mua.findOne({ googleId: profile.id });
    
    if (mua) {
      mua.userType = 'mua';
      return done(null, mua);
    }

    // Check if MUA exists with same email
    mua = await Mua.findOne({ email: profile.emails[0].value });
    
    if (mua) {
      // Link Google account to existing MUA
      mua.googleId = profile.id;
      mua.profilePicture = profile.photos[0].value;
      await mua.save();
      mua.userType = 'mua';
      return done(null, mua);
    }

    // Create new MUA
    const newMua = new Mua({
      name: profile.displayName,
      email: profile.emails[0].value,
      googleId: profile.id,
      profilePicture: profile.photos[0].value,
      phone: '', // Will be required to fill later
      password: 'google_oauth' // Placeholder password for Google users
    });

    await newMua.save();
    newMua.userType = 'mua';
    return done(null, newMua);
  } catch (error) {
    return done(error, null);
  }
}));

module.exports = passport;