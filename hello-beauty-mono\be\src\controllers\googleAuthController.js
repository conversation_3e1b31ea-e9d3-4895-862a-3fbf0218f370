const passport = require('../config/passport');
const jwt = require('jsonwebtoken');
const User = require('../models/user');
const Mua = require('../models/mua');

// Google OAuth for regular users
const googleAuth = passport.authenticate('google-user', {
  scope: ['profile', 'email']
});

const googleCallback = (req, res, next) => {
  passport.authenticate('google-user', { session: false }, async (err, user) => {
    if (err) {
      console.error('Google OAuth Error:', err);
      console.error('Error details:', {
        message: err.message,
        stack: err.stack,
        name: err.name
      });
      return res.redirect(`${process.env.FRONTEND_URL}/login?error=oauth_error`);
    }
    
    if (!user) {
      console.error('Google OAuth: No user returned from authentication');
      return res.redirect(`${process.env.FRONTEND_URL}/login?error=oauth_failed`);
    }

    try {
      // Generate JWT token
      const token = jwt.sign(
        { 
          id: user._id, 
          email: user.email,
          userType: 'user'
        },
        process.env.SECRET_JWT,
        { expiresIn: '7d' }
      );

      // Check if phone number is missing
      if (!user.phone || user.phone === '') {
        return res.redirect(`${process.env.FRONTEND_URL}/profile/complete?token=${token}&missing=phone`);
      }

      // Redirect to frontend with token
      res.redirect(`${process.env.FRONTEND_URL}/login/success?token=${token}`);
    } catch (error) {
      console.error('Google callback error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/login?error=token_error`);
    }
  })(req, res, next);
};

// Google OAuth for MUA users
const googleAuthMua = passport.authenticate('google-mua', {
  scope: ['profile', 'email']
});

const googleCallbackMua = (req, res, next) => {
  passport.authenticate('google-mua', { session: false }, async (err, mua) => {
    if (err) {
      console.error('Google MUA OAuth Error:', err);
      console.error('MUA Error details:', {
        message: err.message,
        stack: err.stack,
        name: err.name
      });
      return res.redirect(`${process.env.FRONTEND_URL}/mua/login?error=oauth_error`);
    }
    
    if (!mua) {
      console.error('Google MUA OAuth: No MUA returned from authentication');
      return res.redirect(`${process.env.FRONTEND_URL}/mua/login?error=oauth_failed`);
    }

    try {
      // Generate JWT token
      const token = jwt.sign(
        { 
          id: mua._id, 
          email: mua.email,
          userType: 'mua'
        },
        process.env.JWT_SECRET_MUA,
        { expiresIn: '7d' }
      );

      // Check if phone number is missing
      if (!mua.phone || mua.phone === '') {
        return res.redirect(`${process.env.FRONTEND_URL}/mua/profile/complete?token=${token}&missing=phone`);
      }

      // Redirect to frontend with token
      res.redirect(`${process.env.FRONTEND_URL}/mua/login/success?token=${token}`);
    } catch (error) {
      console.error('Google MUA callback error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/mua/login?error=token_error`);
    }
  })(req, res, next);
};

// Update phone number for Google users
const updatePhoneNumber = async (req, res) => {
  try {
    const { phone } = req.body;
    
    // Check if req.user exists
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const { userType } = req.user;

    if (!phone) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required'
      });
    }

    let user;
    if (userType === 'user') {
      user = await User.findById(req.user.id);
      // Check if phone already exists for other users
      const existingUser = await User.findOne({ phone, _id: { $ne: req.user.id } });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'Phone number already exists'
        });
      }
    } else if (userType === 'mua') {
      user = await Mua.findById(req.user.id);
      // Check if phone already exists for other MUAs
      const existingMua = await Mua.findOne({ phone, _id: { $ne: req.user.id } });
      if (existingMua) {
        return res.status(400).json({
          success: false,
          message: 'Phone number already exists'
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: 'Invalid user type'
      });
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    user.phone = phone;
    await user.save();

    res.json({
      success: true,
      message: 'Phone number updated successfully',
      data: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone
      }
    });
  } catch (error) {
    console.error('Update phone error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  googleAuth,
  googleCallback,
  googleAuthMua,
  googleCallbackMua,
  updatePhoneNumber
};