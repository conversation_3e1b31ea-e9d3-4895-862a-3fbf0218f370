import { Modal } from "react-responsive-modal";
import { Icon } from "@iconify/react";

const ConfirmationModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  isLoading,
  data,
  error 
}) => {
  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      center
      classNames={{
        overlay: "",
        modal: "rounded-2xl p-6 w-full md:max-w-2xl lg:max-w-3xl",
      }}
    >
      <div className="text-gray-600">
        <h3 className="text-xl font-semibold mb-4">Konfirmasi Booking</h3>
      
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs text-gray-500">Nama</div>
              <div className="font-medium">{data?.name}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs text-gray-500">WhatsApp</div>
              <div className="font-medium">{data?.phone}</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs text-gray-500">Email</div>
              <div className="font-medium">{data?.email}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs text-gray-500">Jumlah Pax</div>
              <div className="font-medium">{data?.pax} orang</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs text-gray-500">Tanggal</div>
              <div className="font-medium">{data?.date}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs text-gray-500">Waktu</div>
              <div className="font-medium">{data?.time}</div>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-xs text-gray-500">Alamat</div>
            <div className="font-medium">{data?.address}</div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-xs text-gray-500">Lokasi</div>
            <div className="font-medium">{data?.location}</div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-xs text-gray-500">Paket</div>
            <div className="font-medium">{data?.package}</div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-xs text-gray-500">Harga Paket</div>
            <div className="font-medium">Rp {data?.price}</div>
          </div>

          {data?.voucherDiscount !== "0" && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs text-gray-500">Potongan Voucher</div>
              <div className="font-medium text-green-600">- Rp {data?.voucherDiscount}</div>
            </div>
          )}

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-xs text-gray-500">Total Harga</div>
            <div className="font-medium">Rp {data?.finalPrice}</div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-xs text-gray-500">Down Payment ({data?.dp}%)</div>
            <div className="font-medium text-hb-pink">Rp {data?.dpAmount}</div>
          </div>
        </div>

          {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-lg">
            {error}
          </div>
        )}

        <div className="flex gap-4 mt-6">
          <button
            onClick={onClose}
            className="flex-1 border border-gray-300 text-gray-600 py-2 px-4 rounded-lg hover:bg-gray-50"
          >
            Kembali
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className="flex-1 bg-hb-pink text-white py-2 px-4 rounded-lg hover:bg-hb-pink-dark disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin" />
                Memproses...
              </div>
            ) : (
              "Konfirmasi Booking"
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmationModal; 
