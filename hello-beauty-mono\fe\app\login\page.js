"use client"
import {Icon} from '@iconify/react';
import Link from 'next/link'
import {useState} from "react";
import ErrorSpan from '../_component/errorSpan';
import {apiPublic} from '../_helper/api';
import {setCookie} from '@/app/_helper/cookie';

export default function Home() {

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [passwordVisible, setPasswordVisible] = useState(false);

   const handleSubmit = async (e) => {
    try {
      e.preventDefault()
      setError('')
      setLoading(true)
      const res = await apiPublic('POST', '/auth/login', {email, password})
      setCookie('hb_token', res.data.token);
      setCookie('user', JSON.stringify(res.data.user));
      setLoading(false);
      window.location.href = '/';
      setLoading(false)
    } catch (error) {
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div className="h-14 flex items-center px-4">
        <Link href="/">
        <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link>
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="mt-20">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Login<span className="text-hb-pink">.</span></h3>
      </div>
      <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 px-4 mt-20">
        <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/sms.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} 
          required value={email} onChange={(e) => setEmail(e.target.value)}
          type="email" placeholder="Email" className="w-full  focus:outline-none"  />
        </div>

        <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/fingerprint.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} 
          required value={password} onChange={(e) => setPassword(e.target.value)}
          type={passwordVisible ? "text" : "password"} placeholder="Password" className="w-full  focus:outline-none"  />
          <button
            type="button"
            onClick={() => setPasswordVisible(!passwordVisible)}
            className="ml-2"
          >
            <Icon icon={passwordVisible ? "mdi:eye-off" : "mdi:eye"} />
          </button>
        </div>
        <div className="w-full text-right mb-4">
          <Link href="/forgot-password" className="text-hb-pink underline">Lupa Password ?</Link>
        </div>
        
      </div>
       <div className="px-4">
        {
          error && <ErrorSpan msg={error} />
        }

      </div>

      <div className="mt-4 px-6">
        <button type="submit" disabled={loading} className="btn-primary items-center flex justify-center">
         {
            loading && <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin mr-2" />
          }
          Login
        </button>
         <div className="py-6 w-full text-center text-gray-400 text-xs">
          Belum Punya Akun ? <Link href="/register" className="text-hb-pink underline">Register</Link>
        </div>

        <div className="py-6 w-full text-center text-gray-400 text-xs">
          <Link href="/mua/login" className="text-hb-pink underline">Login MUA</Link>
        </div>

        {/* <div className="py-6 w-full text-center text-gray-400 text-xs">
          ATAU
        </div>
        
        <a 
          href={`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8900'}/auth/google`}
          className="rounded-full w-full text-center py-2 border border-gray-500 text-gray-500 mt-2 flex justify-center items-center hover:bg-gray-50 transition-colors"
        >
          <img src="/google.jpeg" alt="Google" className="h-6 mr-4" />
          Login dengan Google
        </a> */}
        </div>
      </form>


      </div>
  );
}
