<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Verifikasi MUA</h1>
        <!-- <button
          class="btn-primary flex items-center"
          @click.prevent="showPanel = true"
        >
          <icon name="jam:plus" class="text-xl" />
          Tambah
        </button> -->
      </div>
      <div class="grid grid-cols-2 md:grid-cols-4 p-3 gap-2 mt-4">
        <select
          v-model="search.status"
          type="text"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Cari Nama..."
        >
          <option value="" disabled selected>Pilih Status</option>
          <option value="PENDING">Pending</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
          <option value="">Semua</option>
        </select>
        <div class="grid grid-cols-2 gap-2">
          <button class="btn-primary" @click.prevent="getData()">Cari</button>
          <button
            class="btn-secondary"
            @click.prevent="(search.status = ''), getData()"
          >
            Reset
          </button>
        </div>
      </div>

      <table class="w-full text-sm mt-4">
        <thead>
          <tr class="bg-gray-100 font-semibold">
            <td class="p-2">
              <p>Nama</p>
              <p>MUA</p>
            </td>
            <td class="p-2">
              <p>Email</p>
              <p>Whatsapp</p>
            </td>
            <td class="p-2">Alamat</td>
            <td class="p-2">Status</td>
            <td class="p-2">Note</td>
            <td class="p-2">Waktu Daftar</td>
            <td />
          </tr>
        </thead>
        <tbody>
          <tr v-for="(d, i) in list" :key="i" class="hover:bg-gray-50 border-b">
            <td class="p-2">
              <p class="text-sm">
                {{ d.name }}
              </p>
              <p class="text-sm">
                {{ d.profileName }}
              </p>
            </td>
            <td class="p-2">
              <p class="text-sm">
                {{ d.email }}
              </p>
              <p class="text-sm">
                {{ d.phone }}
              </p>
            </td>

            <td class="p-2">
              <p class="text-sm">
                {{ d.address }}
              </p>
              <p class="text-sm">
                {{ d.locationName }}
              </p>
            </td>
            <td class="p-2">
              <status-approval :status="d.status" />
            </td>
            <td class="p-2">
              <p class="text-xs text-gray-500">
                {{ d.note || "-" }}
              </p>
            </td>
            <td class="p-2">
              <p class="text-sm">
                {{ useMoment(d.createdAt).format("DD MMM, HH:mm") }}
              </p>
            </td>
            <td class="p-2">
              <button
                v-if="d.status === 'pending'"
                class="btn-secondary"
                @click.prevent="(selectedMua = d), (showPanel = true)"
              >
                Review MUA
                <icon name="jam:arrow-right" class="text-xl" />
              </button>
            </td>
          </tr>
        </tbody>

        <tbody v-if="errorMsg && !loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              <icon name="icon-park-twotone:data" class="text-2xl block" />
              {{ errorMsg }}
            </td>
          </tr>
        </tbody>

        <!-- tbody loding -->
        <tbody v-if="loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              Memuat
              <icon name="svg-spinners:3-dots-bounce" class="text-2xl" />
            </td>
          </tr>
        </tbody>
      </table>

      <div class="p-3">
        <div class="flex justify-end gap-2 items-center mx-auto">
          <button
            v-if="page > 1"
            class="btn-secondary"
            @click.prevent="page--, page < 1 ? (page = 1) : '', getData()"
          >
            Prev
          </button>
          <form @submit="getData()">
            <input
              v-model="page"
              class="text-sm py-2 w-10 text-center border rounded-lg"
              type="number"
            />
          </form>
          <button class="btn-secondary" @click.prevent="page++, getData()">
            Next
          </button>
        </div>
      </div>
      <form-approval
        :show="showPanel"
        :data="selectedMua"
        @closed="(showPanel = false), (selectedMua = {})"
        @refresh="(showPanel = false), (selectedMua = {}), getData()"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import { debounce } from "lodash"; // Import debounce function

definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Verifikasi MUA",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "Lokasi",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);
const search = ref({
  status: "",
});

const selectedMua = ref({});
const showPanel = ref(false);

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(
      `/approval?page=${page.value}&limit=10&status=${search.value.status}`
    );
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

watch(
  () => search.value,
  (val) => {
    getData();
  },
  { deep: true }
);

onMounted(() => {
  getData();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
