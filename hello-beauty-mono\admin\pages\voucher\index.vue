<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Voucher</h1>
        <button
          class="btn-primary flex items-center"
          @click.prevent="showPanel = true"
        >
          <icon name="jam:plus" class="text-xl" />
          Tambah
        </button>
      </div>

      <form
        class="grid grid-cols-2 md:grid-cols-4 p-3 gap-2 mt-4"
        @submit.prevent="(page = 1), getData()"
      >
        <input
          v-model="search.search"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Cari..."
          type="text"
        />
        <input
          v-model="search.startDate"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Tanggal Mulai"
          type="date"
        />
        <input
          v-model="search.endDate"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Tanggal Berakhir"
          type="date"
        />
        <select
          v-model="search.status"
          class="p-2 border border-gray-200 rounded-lg"
        >
          <option value="">Status Voucher</option>
          <option value="active">Aktif</option>
          <option value="inactive">Tidak Aktif</option>
        </select>
        <div class="grid grid-cols-2 gap-2">
          <button class="btn-primary" type="submit">Cari</button>
          <button
            type="button"
            class="btn-secondary"
            @click.prevent="
              (search = {
                search: '',
                startDate: '',
                endDate: '',
                status: '',
              }),
                (page = 1),
                getData()
            "
          >
            Reset
          </button>
        </div>
      </form>

      <table class="w-full text-sm mt-4">
        <thead>
          <tr class="bg-gray-100 font-semibold">
            <td class="p-2 w-32">Kode</td>
            <td class="p-2 w-40">Nama</td>
            <td class="p-2 w-24">Tipe</td>
            <td class="p-2 w-32">Nilai</td>
            <td class="p-2 w-32">Maksimal</td>
            <td class="p-2 w-32">Minimal</td>
            <td class="p-2 w-48">Masa Berlaku</td>
            <td class="p-2 w-24">Batas</td>
            <td class="p-2 w-24">Status</td>
            <td class="p-2 w-32">Dibuat</td>
            <td class="p-2 w-24">Aksi</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(d, i) in list" :key="i" class="hover:bg-gray-50 border-b">
            <td class="px-2 py-3">
              <p class="text-sm font-medium">{{ d.code }}</p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm">{{ d.name }}</p>
            </td>
            <td class="px-2 py-3">
              <span
                :class="
                  d.discountType === 'percentage'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-purple-100 text-purple-800'
                "
                class="px-2 py-1 rounded-full text-xs"
              >
                {{ d.discountType === "percentage" ? "Persentase" : "Nominal" }}
              </span>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm font-medium">
                {{
                  d.discountType === "percentage"
                    ? d.discountValue + "%"
                    : useRupiah(d.discountValue)
                }}
              </p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm font-medium">{{ useRupiah(d.maxDiscount) }}</p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm font-medium">{{ useRupiah(d.minPurchase) }}</p>
            </td>
            <td class="px-2 py-3">
              <div class="flex flex-col">
                <p class="text-sm">
                  {{ useMoment(d.startDate).format("DD MMM YYYY") }}
                </p>
                <p class="text-xs text-gray-500">
                  {{ useMoment(d.endDate).format("DD MMM YYYY") }}
                </p>
              </div>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm">{{ d.usageLimit }}</p>
            </td>
            <td class="px-2 py-3">
              <span
                :class="
                  d.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                "
                class="px-2 py-1 rounded-full text-xs"
              >
                {{ d.isActive ? "Aktif" : "Tidak Aktif" }}
              </span>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm">
                {{ useMoment(d.createdAt).format("DD MMM, HH:mm") }}
              </p>
            </td>
            <td class="px-2 py-3">
              <div class="flex gap-1">
                <button
                  class="btn-secondary text-xs p-1"
                  @click.prevent="(selectedVoucher = d), (showPanel = true)"
                >
                  <icon name="icon-park-twotone:edit" class="text-xs" />
                </button>
                <button
                  class="btn-danger text-xs p-1"
                  @click.prevent="(hapusId = d), (showHapus = true)"
                >
                  <icon name="icon-park-twotone:delete" class="text-xs" />
                </button>
              </div>
            </td>
          </tr>
        </tbody>

        <tbody v-if="errorMsg && !loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="11">
              <icon name="icon-park-twotone:data" class="text-2xl block" />
              {{ errorMsg }}
            </td>
          </tr>
        </tbody>

        <tbody v-if="loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="11">
              Memuat
              <icon name="svg-spinners:3-dots-bounce" class="text-2xl" />
            </td>
          </tr>
        </tbody>
      </table>

      <div class="p-3">
        <div class="flex justify-end gap-2 items-center mx-auto">
          <button
            v-if="page > 1"
            class="btn-secondary"
            @click.prevent="page--, page < 1 ? (page = 1) : '', getData()"
          >
            Prev
          </button>
          <form @submit="getData()">
            <input
              v-model="page"
              class="text-sm py-2 w-10 text-center border rounded-lg"
              type="number"
            />
          </form>

          <button class="btn-secondary" @click.prevent="page++, getData()">
            Next
          </button>
        </div>
      </div>

      <form-voucher
        :show="showPanel"
        :data="selectedVoucher"
        @closed="(showPanel = false), (selectedVoucher = {})"
        @refresh="(showPanel = false), (selectedVoucher = {}), getData()"
      />

      <hapus-voucher
        :show="showHapus"
        :data="hapusId"
        @closed="(showHapus = false), (hapusId = '')"
        @refresh="(showHapus = false), (hapusId = ''), getData()"
      />
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Voucher",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "Voucher",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);
const search = ref({
  search: "",
  startDate: "",
  endDate: "",
  status: "",
});

const hapusId = ref("");
const showHapus = ref(false);
const selectedVoucher = ref({});
const showPanel = ref(false);

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(
      `/voucher?page=${page.value}&limit=10&search=${search.value.search}&startDate=${search.value.startDate}&endDate=${search.value.endDate}&status=${search.value.status}`
    );
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response?.data?.message || "Terjadi kesalahan";
  }
};

onMounted(() => {
  getData();
});
</script> 
