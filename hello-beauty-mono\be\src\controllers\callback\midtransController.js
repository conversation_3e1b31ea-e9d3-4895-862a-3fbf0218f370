const paymentCallback = require('../../models/payment-callback');
const payment = require('../../models/payment');
const transaction = require("../../models/transaction");
const { addSaldoReferral } = require('../admin/referralController');
const { logFunction, logError, logSuccess } = require('../../utils/logger');
const telegramNotifier = require('../../utils/telegramNotifier');

// Constants for transaction statuses
const TRANSACTION_STATUS = {
    SETTLEMENT: 'settlement',
    CAPTURE: 'capture',
    DENY: 'deny',
    CANCEL: 'cancel',
    FAILURE: 'failure',
    EXPIRE: 'expire'
};

const PAYMENT_STATUS = {
    PAID: 'PAID',
    FAILED: 'FAILED',
    EXPIRED: 'EXPIRED'
};

const TRANSACTION_PAYMENT_STATUS = {
    PAID: 'PAID',
    DP_PAID: 'DP_PAID'
};

/**
 * Saves the payment callback data
 * @param {Object} body - Request body
 * @param {Object} header - Request headers
 * @returns {Promise<Object>} - Saved payment callback
 */
const savePaymentCallback = async (body, header) => {
    logFunction('savePaymentCallback', { orderId: body.order_id, status: body.transaction_status });
    
    try {
        const newPaymentCallback = await new paymentCallback({
            order_id: body.order_id,
            status: body.transaction_status,
            rawData: body,
            header: header
        });
        const savedCallback = await newPaymentCallback.save();
        
        logSuccess('savePaymentCallback', { success: true, id: savedCallback._id });
        return savedCallback;
    } catch (error) {
        logError('savePaymentCallback', error);
        await telegramNotifier.sendErrorNotification('Failed to save payment callback', error, { orderId: body.order_id });
        throw error;
    }
};

/**
 * Updates payment status based on transaction status
 * @param {string} orderId - Payment order ID
 * @param {string} status - New payment status
 * @returns {Promise<void>}
 */
const updatePaymentStatus = async (orderId, status) => {
    logFunction('updatePaymentStatus', { orderId, newStatus: status });
    
    try {
        const paymentBefore = await payment.findOne({ payId: orderId });
        if (!paymentBefore) {
            const errorMsg = `Payment not found for orderId: ${orderId}`;
            logError('updatePaymentStatus', new Error(errorMsg));
            await telegramNotifier.sendErrorNotification('Payment Not Found', errorMsg);
            throw new Error(errorMsg);
        }
        
        const updateResult = await payment.findOneAndUpdate(
            { payId: orderId },
            { 
                status,
                ...(status === PAYMENT_STATUS.PAID && { paidAt: new Date() })
            },
            { new: true }
        );
        
        logSuccess('updatePaymentStatus', { 
            oldStatus: paymentBefore.status, 
            newStatus: status,
            paymentData: {
                orderId,
                isDp: updateResult.isDp,
                percent: updateResult.percent,
                paidAt: updateResult.paidAt
            }
        });
        
        await telegramNotifier.sendPaymentNotification('Payment Status Updated', {
            orderId,
            oldStatus: paymentBefore.status,
            newStatus: status,
            isDp: updateResult.isDp,
            percent: updateResult.percent,
            trxId: updateResult.trxId
        });
        
        return updateResult;
    } catch (error) {
        logError('updatePaymentStatus', error);
        await telegramNotifier.sendErrorNotification('Failed to update payment status', error, { orderId, status });
        throw error;
    }
};

/**
 * Processes full payment and updates transaction status
 * @param {string} trxId - Transaction ID
 * @returns {Promise<void>}
 */
const processFullPayment = async (trxId) => {
    logFunction('processFullPayment', { trxId });
    
    try {
        const trxBefore = await transaction.findOne({ trxId });
        if (!trxBefore) {
            const errorMsg = `Transaction not found for trxId: ${trxId}`;
            logError('processFullPayment', new Error(errorMsg));
            await telegramNotifier.sendErrorNotification('Transaction Not Found', errorMsg);
            throw new Error(errorMsg);
        }
        
        const updateResult = await transaction.findOneAndUpdate(
            { trxId },
            { statusPayment: TRANSACTION_PAYMENT_STATUS.PAID },
            { new: true }
        );
        
        logSuccess('processFullPayment', { 
            oldStatus: trxBefore.statusPayment, 
            newStatus: TRANSACTION_PAYMENT_STATUS.PAID,
            trxData: {
                trxId,
                statusPayment: updateResult.statusPayment
            }
        });
        
        await telegramNotifier.sendPaymentNotification('Full Payment Processed', {
            trxId,
            oldStatus: trxBefore.statusPayment,
            newStatus: TRANSACTION_PAYMENT_STATUS.PAID,
            totalHarga: trxBefore.totalHarga
        });
        
        // Check if transaction has referral code before adding saldo referral
        if (updateResult && updateResult.referralCode) {
            await addSaldoReferral(trxId);
        }
        
        return updateResult;
    } catch (error) {
        logError('processFullPayment', error);
        await telegramNotifier.sendErrorNotification('Failed to process full payment', error, { trxId });
        throw error;
    }
};

/**
 * Processes down payment and updates transaction status
 * @param {string} trxId - Transaction ID
 * @returns {Promise<void>}
 */
const processDownPayment = async (trxId) => {
    logFunction('processDownPayment', { trxId });
    
    try {
        const trxBefore = await transaction.findOne({ trxId });
        if (!trxBefore) {
            const errorMsg = `Transaction not found for trxId: ${trxId}`;
            logError('processDownPayment', new Error(errorMsg));
            await telegramNotifier.sendErrorNotification('Transaction Not Found', errorMsg);
            throw new Error(errorMsg);
        }
        
        const updateResult = await transaction.findOneAndUpdate(
            { trxId },
            { statusPayment: TRANSACTION_PAYMENT_STATUS.DP_PAID },
            { new: true }
        );
        
        logSuccess('processDownPayment', { 
            oldStatus: trxBefore.statusPayment, 
            newStatus: TRANSACTION_PAYMENT_STATUS.DP_PAID,
            trxData: {
                trxId,
                statusPayment: updateResult.statusPayment
            }
        });
        
        await telegramNotifier.sendPaymentNotification('Down Payment Processed', {
            trxId,
            oldStatus: trxBefore.statusPayment,
            newStatus: TRANSACTION_PAYMENT_STATUS.DP_PAID,
            totalHarga: trxBefore.totalHarga
        });
        
        return updateResult;
    } catch (error) {
        logError('processDownPayment', error);
        await telegramNotifier.sendErrorNotification('Failed to process down payment', error, { trxId });
        throw error;
    }
};

/**
 * Handles successful payment (settlement or capture)
 * @param {Object} paymentData - Payment data
 * @returns {Promise<void>}
 */
const handleSuccessfulPayment = async (paymentData) => {
    const { order_id: orderId, transaction_status: status } = paymentData;
    logFunction('handleSuccessfulPayment', { orderId, status });
    
    try {
        // Update payment status to PAID
        await updatePaymentStatus(orderId, PAYMENT_STATUS.PAID);
        
        // Find the payment record
        const searchPayment = await payment.findOne({ payId: orderId });
        if (!searchPayment) {
            const errorMsg = `Payment record not found for orderId: ${orderId}`;
            logError('handleSuccessfulPayment', new Error(errorMsg));
            await telegramNotifier.sendErrorNotification('Payment Record Not Found', errorMsg);
            throw new Error(errorMsg);
        }
        
        const { trxId, isDp, percent } = searchPayment;
        
        // Log and notify about the payment details for debugging
        const paymentDetails = { trxId, isDp, percent, orderId, status };
        logFunction('handleSuccessfulPayment', { paymentDetails }, 'debug');
        await telegramNotifier.sendPaymentNotification('Payment Details', paymentDetails);
        
        // Special logging for DP 100% case to track the issue
        if (isDp && percent === 100) {
            logFunction('handleSuccessfulPayment', { 
                message: 'Processing 100% DP payment',
                paymentDetails 
            }, 'debug');
            await telegramNotifier.sendPaymentNotification('100% DP Payment Detected', paymentDetails);
        }

        if (!isDp) {
            // Regular full payment
            logFunction('handleSuccessfulPayment', { message: 'Processing as regular full payment', isDp, percent });
            await processFullPayment(trxId);
        } else if (isDp && percent === 100) {
            // 100% DP case - this is where the issue might be
            logFunction('handleSuccessfulPayment', { message: 'Processing as 100% DP payment', isDp, percent });
            await processFullPayment(trxId);
        } else {
            // Regular DP case
            logFunction('handleSuccessfulPayment', { message: 'Processing as partial DP payment', isDp, percent });
            await processDownPayment(trxId);
        }
        
        // Success logging after all operations
        const resultStatus = (!isDp || (isDp && percent === 100)) ? 
            TRANSACTION_PAYMENT_STATUS.PAID : 
            TRANSACTION_PAYMENT_STATUS.DP_PAID;
        
        const actionType = (!isDp || (isDp && percent === 100)) ? 
            'full payment processed' : 
            'down payment processed';
        
        logSuccess('handleSuccessfulPayment', { 
            orderId,
            trxId,
            status: resultStatus,
            action: actionType,
            isDp,
            percent
        });
        
        await telegramNotifier.sendPaymentNotification('Payment Successfully Processed', {
            orderId,
            trxId,
            status: resultStatus,
            action: actionType,
            isDp,
            percent
        });
        
    } catch (error) {
        logError('handleSuccessfulPayment', error);
        await telegramNotifier.sendErrorNotification('Failed to handle successful payment', error, { orderId, status });
        throw error;
    }
};

/**
 * Handles failed payment (deny, cancel, or failure)
 * @param {string} orderId - Payment order ID
 * @returns {Promise<void>}
 */
const handleFailedPayment = async (orderId) => {
    logFunction('handleFailedPayment', { orderId });
    
    try {
        await updatePaymentStatus(orderId, PAYMENT_STATUS.FAILED);
        
        logSuccess('handleFailedPayment', { 
            orderId,
            status: PAYMENT_STATUS.FAILED,
            action: 'payment marked as failed'
        });
        
        await telegramNotifier.sendPaymentNotification('Payment Failed', {
            orderId,
            status: PAYMENT_STATUS.FAILED
        });
    } catch (error) {
        logError('handleFailedPayment', error);
        await telegramNotifier.sendErrorNotification('Failed to handle failed payment', error, { orderId });
        throw error;
    }
};

/**
 * Handles expired payment
 * @param {string} orderId - Payment order ID
 * @returns {Promise<void>}
 */
const handleExpiredPayment = async (orderId) => {
    logFunction('handleExpiredPayment', { orderId });
    
    try {
        await updatePaymentStatus(orderId, PAYMENT_STATUS.EXPIRED);
        
        logSuccess('handleExpiredPayment', { 
            orderId,
            status: PAYMENT_STATUS.EXPIRED,
            action: 'payment marked as expired'
        });
        
        await telegramNotifier.sendPaymentNotification('Payment Expired', {
            orderId,
            status: PAYMENT_STATUS.EXPIRED
        });
    } catch (error) {
        logError('handleExpiredPayment', error);
        await telegramNotifier.sendErrorNotification('Failed to handle expired payment', error, { orderId });
        throw error;
    }
};

const addData = async (req, res) => {
    const fnName = 'addData';
    const { body, headers } = req;
    const { order_id: orderId, transaction_status: status } = body;

    logFunction(fnName, { orderId, status });
    await telegramNotifier.sendPaymentNotification('Midtrans Callback Received', { orderId, status });

    try {
        // Save payment callback
        await savePaymentCallback(body, headers);
        logSuccess(fnName, { orderId, status, action: 'callback saved' });

        // Handle different transaction statuses
        if ([TRANSACTION_STATUS.SETTLEMENT, TRANSACTION_STATUS.CAPTURE].includes(status)) {
            await handleSuccessfulPayment(body);
        } else if ([TRANSACTION_STATUS.DENY, TRANSACTION_STATUS.CANCEL, TRANSACTION_STATUS.FAILURE].includes(status)) {
            await handleFailedPayment(orderId);
        } else if (status === TRANSACTION_STATUS.EXPIRE) {
            await handleExpiredPayment(orderId);
        } else {
            // Unknown status
            logFunction(fnName, { message: 'Unknown transaction status', orderId, status }, 'warn');
            await telegramNotifier.sendPaymentNotification('Unknown Transaction Status', { orderId, status });
        }

        logSuccess(fnName, { orderId, status, action: 'callback processing completed' });
        
        // Final verification check for DP 100% case
        if ([TRANSACTION_STATUS.SETTLEMENT, TRANSACTION_STATUS.CAPTURE].includes(status)) {
            const paymentRecord = await payment.findOne({ payId: orderId });
            const transactionRecord = await transaction.findOne({ trxId: paymentRecord?.trxId });
            
            if (paymentRecord && paymentRecord.isDp && paymentRecord.percent === 100) {
                await telegramNotifier.sendPaymentNotification('DP 100% Verification', {
                    orderId,
                    trxId: paymentRecord.trxId,
                    paymentStatus: paymentRecord.status,
                    transactionPaymentStatus: transactionRecord?.statusPayment,
                    isDp: paymentRecord.isDp,
                    percent: paymentRecord.percent,
                    message: 'Final verification for DP 100% payment case'
                });
            }
        }
        
        return res.status(201).json({ message: 'OK' });

    } catch (error) {
        logError(fnName, error);
        await telegramNotifier.sendErrorNotification('Midtrans Callback Processing Error', error, { orderId, status });
        return res.status(500).json({ message: 'Internal server error' });
    }
};

module.exports = {
    addData
};
