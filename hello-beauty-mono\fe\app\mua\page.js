"use client";
import BottomMenu from "@/app/_component/mua/bottomMenu";
import { apiMua } from "@/app/_helper/api-mua";
import { useEffect, useState } from "react";
import Link from "next/link";
import { Icon } from "@iconify/react";
import LoadingFull from "../_component/loadingFull";
import MyPortofolio from "../_component/mua/portofolio/myPortofolio";
import MyProject from "../_component/mua/project/myProject";

export default function Mua() {
  const [loading, setLoading] = useState(true);
  const [me, setMe] = useState({});
  const [approved, setApproved] = useState(false);
  const [rejected, setRejected] = useState(false);
  const [pending, setPending] = useState(false);
  const [note, setNote] = useState("");
  
  // get me
  const getMe = async () => {
    try {
      const response = await apiMua("GET", "/me");
      setLoading(false);
      setMe(response.data);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };
  // get approval
  const checkApproval = async () => {
    try {
      const response = await apiMua("GET", "/approval/check");
      if (response.data.status === "approved") {
        setApproved(true);
      }
      if(response.data.status === "rejected") {
        setRejected(true);
        // setNote
      }
      if(response.data.status === "PENDING") {
        setPending(true);
      }

      if(response.data.note) {
        setNote(response.data.note);
      }

    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
      getMe();
      checkApproval();
  }, []);
  return (
    <div className="max-w-[480px] mx-auto ">
      {
        loading && (
          <LoadingFull/>
        )
      }

      <div className="min-h-[300px] grid-bg"/>

      <div className="-mt-[300px] p-4">

        <img src="/logo.png" alt="Logo" className="h-8 mb-6" />
      {
        (!me.isApproved && !approved && !pending && !rejected && !loading) && (
          <div className="min-h-screen flex items-center justify-center">
            <div>
              <Icon icon="hugeicons:id-not-verified" className="text-5xl mx-auto text-hb-pink"/>
              <div className="mb-12 mt-4 text-hb-pink">
              Akun belum di verifikasi
              </div>
              <Link href="/mua/submit" className="btn-primary px-6">
                Verifikasi Sekarang
              </Link>
              </div>
          </div>
        )
      }

      {
        (pending && !loading) && (
          <div className="min-h-screen flex items-center justify-center">
            <div>
              <Icon icon="hugeicons:user-id-verification" className="text-5xl mx-auto text-hb-pink"/>
              <div className="mb-12 mt-4 text-center text-hb-pink">
              Verifikasi Akun <br/>sedang di proses
              </div>
            </div>
          </div>
        )
      }

      {
        (rejected && !loading) && (
          <div className="min-h-screen flex items-center justify-center">
            <div>
              <Icon icon="hugeicons:id-not-verified" className="text-5xl mx-auto text-hb-pink"/>
              <div className=" mt-4 text-hb-pink text-center">
              Akun ditolak
              </div>
              <div className="my-4 mb-12 text-red-600 text-center">
                Alasan:  {note}
              </div>
              <Link href="/mua/submit" className="btn-primary px-6">
                Verifikasi Ulang
              </Link>
            </div>
          </div>
        )
      }
       
        {
          (me.isApproved && !loading) && (
            <div>
              <div>
                <div class="w-full max-w-2xl bg-white roundex-xl mb-4">
        <div class="aspect-w-16 aspect-h-9">
            <iframe
                class="w-full h-60 rounded-lg"
                src="https://www.youtube.com/embed/GJ564mXQjBU"
                title="YouTube video player"
                frameborder="0"
                autoPlay="1"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen>
            </iframe>
        </div>
    </div>
              </div>
              <MyProject/>
              <MyPortofolio/>
            </div>
          )
        }
      </div>


<div className="h-40">

</div>
      <BottomMenu />
    </div>
  );
}
