<template>
  <div>
    <table class="w-full text-sm mt-4">
      <thead>
        <tr class="bg-gray-100 font-semibold">
          <td class="p-2">Tanggal</td>
          <td class="p-2">Tipe</td>
          <td class="p-2">Nominal</td>
          <td class="p-2">Deskripsi</td>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in list" :key="index">
          <td class="p-2">
            {{ useMoment(item.createdAt).format("DD MMM YYYY, HH:mm") }}
          </td>
          <td
            class="p-2"
            :class="{
              'text-green-500': item.type === 'credit',
              'text-red-500': item.type === 'debit',
            }"
          >
            {{ item.type }}
          </td>
          <td class="p-2">{{ useRupiah(item.amount) }}</td>
          <td class="p-2">{{ item.description }}</td>
        </tr>
        <tr v-if="!list.length">
          <td class="p-2 text-center" :colspan="4">Data tidak ditemukan</td>
        </tr>
      </tbody>
    </table>
    <loader-full v-if="loading" />
  </div>
</template>

<script setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});

const list = computed(() => props.list);
</script>
