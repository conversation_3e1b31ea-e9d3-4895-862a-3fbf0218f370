<template>
  <div>
    <span
      :class="colorStatus"
      class="text-[10px] font-semibold px-2 py-1 rounded-full"
    >
      {{ props.status ? 'Verified' : 'Unverified' }}
    </span>
  </div>
</template>

<script setup>
const props = defineProps({
  status: {
    type: String,
    required: true
  }
});

const colorStatus = computed(() => {
  if (props.status) {
    return 'text-green-600 bg-green-100';
  }
  return 'text-red-600 bg-red-100';
});
</script>
