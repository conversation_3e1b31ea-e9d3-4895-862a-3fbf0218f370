"use client";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { apiMua } from "@/app/_helper/api-mua";

export default function EditProfile() {
  const [form, setForm] = useState({
    name: "",
    muaName: "",
    phone: "",
    email: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await apiMua("GET", "/me");
        setForm({
          name: response.data.name,
          muaName: response.data.muaName,
          phone: response.data.phone,
          email: response.data.email,
        });
      } catch (error) {
        setError(error.message);
      }
    };
    fetchData();
  }, []);

  const handleSubmit = async (e) => { 
    e.preventDefault();
    setError("");
    setLoading(true);
    try {
      await api<PERSON>ua("PUT", "/me", form);
      setForm({
        name: "",
        muaName: "",
        phone: "",
      });
      window.location.href = "/mua/profil";
    } catch (error) {
      setError(error.message);
    }
    setLoading(false);
  }

  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4 justify-between max-w-[480px]">
          <Link href={`/mua/profil`}>
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
        </div>
        <div className="pt-4">
          <h3 className="text-2xl font-semibold text-center mb-6">
            Edit<br/> Profil<span className="text-hb-pink">.</span>
          </h3>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-3">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Nama
              </label>
              <input
                type="text"
                name="name"
                disabled={loading}
                required
                onChange={(e) => setForm({ ...form, name: e.target.value })}
                id="name"
                autoComplete="name"
                className="form-input"
                value={form.name}
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Nama MUA
              </label>
              <input
                type="text"
                name="muaName"
                id="muaName"
                disabled={loading}
                required
                onChange={(e) => setForm({ ...form, muaName: e.target.value })}
                autoComplete="muaName"
                className="form-input"
                value={form.muaName}
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Nomor Whatsapp
              </label>
              <input
                type="text"
                name="phone"
                id="phone"
                disabled={loading}
                required
                onChange={(e) => setForm({ ...form, phone: e.target.value })}
                autoComplete="phone"
                className="form-input"
                value={form.phone}
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                type="email"
                name="email"
                id="email"
                disabled
                className="form-input"
                value={form.email}
              />
            </div>
            {
              error && (
                <div className="text-red-500 text-sm mb-4">
                  {error}
                </div>
              )
            }
            <div className="mb-4">
              <button
                type="submit"
                className="btn-primary flex items-center justify-center"
              >
                {loading && (
                  <Icon
                    icon="svg-spinners:180-ring-with-bg"
                    className="text-white animate-spin mr-2"
                  />
                )}
                Simpan
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
