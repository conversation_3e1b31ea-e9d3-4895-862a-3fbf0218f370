const cron = require('node-cron');
const payment = require('../../models/payment');
const transaction = require('../../models/transaction');
const { sendWhatsapp } = require('../../helper/kirimi');
const moment = require('moment');
const { toRp } = require('../../helper/rupiah');

// Helper function to create delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to get random delay between 30-60 seconds
const getRandomDelay = () => Math.floor(Math.random() * (60000 - 30000 + 1)) + 30000;

const bayarDpReminder = async (req, res) => {
  try {
    // Get all unpaid DP payments that were created 24 hours ago
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const unpaidDpPayments = await payment.find({
      isDp: true,
      status: 'unpaid',
      createdAt: { $lte: twentyFourHoursAgo }
    }).populate('userId');

    for (const payment of unpaidDpPayments) {
      const trx = await transaction.findOne({ trxId: payment.trxId });
      if (trx) {
        const msg = `Halo, ${payment.userId.name}!\n\nTerima kasih telah memesan paket ${trx.packageName} ${trx.packageItemName} di ${trx.locationName}.\n\nDetail transaksi:\nID\n*${payment.trxId}*\n\nPaket\n*${trx.packageName} - ${trx.packageItemName}*\n\nHarga\n*${toRp(trx.packagePrice)}*\n\nJumlah\n*${trx.pax} pax*\n\nTotal\n*${toRp(trx.totalHarga)}*\n\nDP (${payment.percent}%) \n*${toRp(payment.totalBayar)}*.\n\nSilakan lakukan pembayaran segera untuk menghindari pembatalan otomatis.\n\nDetail dan Pembayaran\n\n*https://hellobeauty.id/booking/${payment.trxId}*`;
        await sendWhatsapp(payment.userId.phone, msg);
        
        // Add random delay between 30-60 seconds before sending next message
        await delay(getRandomDelay());
      }
    }

    if (req && res) {
      return res.status(200).json({ message: 'DP reminder sent successfully' });
    }
  } catch (error) {
    console.error('Error in bayarDpReminder:', error);
    if (req && res) {
      return res.status(500).json({ message: 'Internal server error' });
    }
  }
};

const pelunasanReminder = async (req, res) => {
  try {
    // Get all unpaid full payments where booking date is within 3 days
    const threeDaysFromNow = moment().add(3, 'days').startOf('day').toDate();
    const unpaidFullPayments = await payment.find({
      isDp: false,
      status: 'unpaid'
    }).populate('userId');

    for (const payment of unpaidFullPayments) {
      const trx = await transaction.findOne({ trxId: payment.trxId });
      if (trx && moment(trx.bookingDate).isSameOrBefore(threeDaysFromNow)) {
        const msg = `Halo, ${payment.userId.name}!\n\nTerima kasih telah memesan paket ${trx.packageName} ${trx.packageItemName} di ${trx.locationName}.\n\nDetail transaksi:\nID\n*${payment.trxId}*\n\nPaket\n*${trx.packageName} - ${trx.packageItemName}*\n\nHarga\n*${toRp(trx.packagePrice)}*\n\nJumlah\n*${trx.pax} pax*\n\nTotal\n*${toRp(trx.totalHarga)}*\n\nPelunasan (${payment.percent}%) \n*${toRp(payment.totalBayar)}*.\n\nBooking Date\n*${moment(trx.bookingDate).format('DD MMMM YYYY')}*\n\nSilakan lakukan pelunasan segera.\n\nDetail dan Pembayaran\n\n*https://hellobeauty.id/booking/${payment.trxId}*`;
        await sendWhatsapp(payment.userId.phone, msg);
        
        // Add random delay between 30-60 seconds before sending next message
        await delay(getRandomDelay());
      }
    }

    if (req && res) {
      return res.status(200).json({ message: 'Pelunasan reminder sent successfully' });
    }
  } catch (error) {
    console.error('Error in pelunasanReminder:', error);
    if (req && res) {
      return res.status(500).json({ message: 'Internal server error' });
    }
  }
};

const expiredDp = async (req, res) => {
  try {
    // Get all unpaid DP payments that were created 24 hours ago
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const unpaidDpPayments = await payment.find({
      isDp: true,
      status: 'unpaid',
      createdAt: { $lte: twentyFourHoursAgo }
    });

    for (const paymentDoc of unpaidDpPayments) {
      try {
        // Update payment status to expired
        paymentDoc.status = 'expired';
        await paymentDoc.save();

        // Find the corresponding full payment
        const fullPayment = await payment.findOne({
          trxId: paymentDoc.trxId,
          isDp: false,
          userId: paymentDoc.userId
        });

        if (fullPayment) {
          fullPayment.status = 'expired';
          await fullPayment.save();
        }
      } catch (error) {
        console.error(`Error processing payment ${paymentDoc.trxId}:`, error);
        // Continue with next payment even if one fails
        continue;
      }
    }

    if (req && res) {
      return res.status(200).json({ message: 'DP expiration processed successfully' });
    }
  } catch (error) {
    console.error('Error in expiredDp:', error);
    if (req && res) {
      return res.status(500).json({ message: 'Internal server error' });
    }
  }
};

// Schedule cron jobs
cron.schedule('0 9 * * *', () => {
  bayarDpReminder();
  pelunasanReminder();
  expiredDp();
});

module.exports = {
  bayarDpReminder,
  pelunasanReminder,
  expiredDp
};
