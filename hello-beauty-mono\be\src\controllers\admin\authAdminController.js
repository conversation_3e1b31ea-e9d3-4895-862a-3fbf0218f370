const admin = require('../../models/admin');
const bcrypt = require('bcrypt');
const {encode} = require('../../helper/jwt');
const { logFunction, logError, logSuccess } = require('../../utils/logger');

const loginAdmin = async (req, res) => {
    const fnName = 'loginAdmin';
    logFunction(fnName, { email: req.body.email });

    try {
        const { email, password } = req.body;
        const searchAdmin = await admin.findOne({ email: email });
        if(!searchAdmin) {
            logFunction(fnName, { error: 'Admin not found', email }, 'warn');
            return res.status(400).json({ message: 'Email atau Password salah !' });
        }

        const isMatch = await bcrypt.compare(password, searchAdmin.password);
        if(isMatch) {
            const dataAdmin = {
                id: searchAdmin._id,
                name: searchAdmin.name,
                email: searchAdmin.email,
                phone: searchAdmin.phone,
                level: searchAdmin.level,
                exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 365)
            }
            const token = await encode(dataAdmin);

            logSuccess(fnName, { 
                adminId: searchAdmin._id,
                email: searchAdmin.email,
                level: searchAdmin.level
            });

            return res.status(200).json({ 
                message: 'Login berhasil', 
                data:{ 
                    data:dataAdmin,
                    token: token 
                } 
            });
        } else {
            logFunction(fnName, { error: 'Invalid password', email }, 'warn');
            return res.status(400).json({ message: 'Email atau Password salah !' });
        }
    } catch (error) {
        logError(fnName, error);
        res.status(500).json({ message: error.message });
    }
}

module.exports = {
    loginAdmin
}
