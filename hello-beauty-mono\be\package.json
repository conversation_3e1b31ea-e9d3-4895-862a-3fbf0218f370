{"name": "be", "version": "1.0.0", "description": "Backend for Hello Beauty", "main": "index.js", "author": "yolkmonday", "scripts": {"start": "node index.js", "dev": "nodemon src/server.js", "lint": "eslint .", "test": "jest"}, "license": "MIT", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cluster": "^0.7.7", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.2", "helmet": "^8.1.0", "http": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "mongoose": "^8.5.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-mailjet": "^6.0.8", "nodemailer": "^6.9.16", "os": "^0.1.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "winston": "^3.17.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "eslint": "^9.9.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "globals": "^15.9.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.5.3"}, "jest": {"testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/"]}}