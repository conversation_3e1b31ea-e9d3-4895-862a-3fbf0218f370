<template>
  <input
    :value="formattedValue"
    type="text"
    inputmode="numeric"
    @input="updateValue($event.target.value)"
  />
</template>

<script setup>
// import { ref, computed, watch } from 'vue';
// import { mask } from 'vue-the-mask';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

const formattedValue = computed(() => {
  return new Intl.NumberFormat('id-ID', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(props.modelValue);
});

function updateValue(value) {
  const number = parseInt(value.replace(/\D/g, '')) || 0;
  emit('update:modelValue', number);
}
</script>
