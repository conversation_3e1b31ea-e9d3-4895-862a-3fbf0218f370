import axios from 'axios';
import { useAuth } from '~/store/auth';
import { useRuntimeConfig } from '#imports';

const createAxiosInstance = () => {
  const auth = useAuth();
  const config = useRuntimeConfig();
  const defaultUrl = config.public.API_URL;

  const instance = axios.create({
    baseURL: defaultUrl,
    headers: {
      common: {
        Authorization: `${auth.tokenV0y}` || '',
      },
    },
    timeout: 120000,
  });

  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      // if (error.response.status === 401) {
      //   window.location.href = '/logout';
      // }
      throw error;
    },
  );

  return instance;
};

const adminGet = (url) => {
  const instance = createAxiosInstance();
  return instance.get(url);
};

const adminPost = (url, payload) => {
  const instance = createAxiosInstance();
  return instance.post(url, payload);
};

const adminPut = (url, payload) => {
  const instance = createAxiosInstance();
  return instance.put(url, payload);
};

const adminDelete = (url) => {
  const instance = createAxiosInstance();
  return instance.delete(url);
};

const adminFile = (url, form) => {
  const instance = createAxiosInstance();
  instance.defaults.headers.common['Content-Type'] = 'multipart/form-data';
  return instance.post(url, form);
};

export {
  adminGet, adminPost, adminDelete, adminPut, adminFile
};
