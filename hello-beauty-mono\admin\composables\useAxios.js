import { useAuth } from '~/store/auth';
import { useRuntimeConfig } from '#imports';

const createFetchOptions = () => {
  const auth = useAuth();
  const config = useRuntimeConfig();
  const defaultUrl = config.public.API_URL;

  return {
    baseURL: defaultUrl,
    headers: {
      Authorization: `${auth.tokenV0y}` || '',
    },
    timeout: 120000,
    onResponseError({ response }) {
      // if (response.status === 401) {
      //   window.location.href = '/logout';
      // }
      throw createError({
        statusCode: response.status,
        statusMessage: response.statusText,
        data: response._data
      });
    },
  };
};

const adminGet = (url) => {
  const options = createFetchOptions();
  return $fetch(url, {
    ...options,
    method: 'GET'
  });
};

const adminPost = (url, payload) => {
  const options = createFetchOptions();
  return $fetch(url, {
    ...options,
    method: 'POST',
    body: payload
  });
};

const adminPut = (url, payload) => {
  const options = createFetchOptions();
  return $fetch(url, {
    ...options,
    method: 'PUT',
    body: payload
  });
};

const adminDelete = (url) => {
  const options = createFetchOptions();
  return $fetch(url, {
    ...options,
    method: 'DELETE'
  });
};

const adminFile = (url, form) => {
  const options = createFetchOptions();
  return $fetch(url, {
    ...options,
    method: 'POST',
    body: form
  });
};

export {
  adminGet, adminPost, adminDelete, adminPut, adminFile
};
