import { useAuth } from '~/store/auth';
import { useRuntimeConfig } from '#imports';

const createFetchOptions = () => {
  const auth = useAuth();
  const config = useRuntimeConfig();

  return {
    headers: {
      'Authorization': `Bearer ${auth.tokenV0y}`,
      'Content-Type': 'application/json'
    },
    onResponseError({ response }) {
      console.error('API Error:', response.status, response.statusText);
      // if (response.status === 401) {
      //   window.location.href = '/logout';
      // }
      throw createError({
        statusCode: response.status,
        statusMessage: response.statusText,
        data: response._data
      });
    },
  };
};

const getFullUrl = (url) => {
  const config = useRuntimeConfig();
  const baseURL = config.public.API_URL;
  return `${baseURL}${url}`;
};

const adminGet = (url) => {
  const options = createFetchOptions();
  const fullUrl = getFullUrl(url);
  console.log('GET Request to:', fullUrl);
  return $fetch(fullUrl, {
    ...options,
    method: 'GET'
  });
};

const adminPost = (url, payload) => {
  const options = createFetchOptions();
  const fullUrl = getFullUrl(url);
  console.log('POST Request to:', fullUrl);
  return $fetch(fullUrl, {
    ...options,
    method: 'POST',
    body: payload
  });
};

const adminPut = (url, payload) => {
  const options = createFetchOptions();
  const fullUrl = getFullUrl(url);
  console.log('PUT Request to:', fullUrl);
  return $fetch(fullUrl, {
    ...options,
    method: 'PUT',
    body: payload
  });
};

const adminDelete = (url) => {
  const options = createFetchOptions();
  const fullUrl = getFullUrl(url);
  console.log('DELETE Request to:', fullUrl);
  return $fetch(fullUrl, {
    ...options,
    method: 'DELETE'
  });
};

const adminFile = (url, form) => {
  const auth = useAuth();
  const config = useRuntimeConfig();
  const fullUrl = getFullUrl(url);
  console.log('FILE Request to:', fullUrl);

  return $fetch(fullUrl, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${auth.tokenV0y}`,
      // Don't set Content-Type for FormData, let browser set it
    },
    body: form
  });
};

export {
  adminGet, adminPost, adminDelete, adminPut, adminFile
};
