const mongoose = require("mongoose");

const bidSchema = new mongoose.Schema(
  {
    muaId: {
      type: String,
      required: true,
    },
    muaName: {
      type: String,
      required: true,
    },
    trxId: {
      type: String,
      required: true,
    },
    note: {
      type: String,
      required: true,
    },
    selected: {
      type: Boolean,
      default: false,
    },
    idTrx: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "transaction",
    },
  },
  { timestamps: true },
);

module.exports = mongoose.model("bid", bidSchema);
