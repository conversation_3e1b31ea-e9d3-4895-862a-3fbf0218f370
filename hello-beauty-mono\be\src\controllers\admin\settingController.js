const setting = require("../../models/setting");

const getSetting = async(req,res) => {
  try {
    const checkSetting = await setting.findOne()
    console.log(checkSetting);
    
    return res.json({
      data: checkSetting
    })
  } catch (error) {
    return res.status(500).json({ message: error.message });
    
  }
}

const saveSetting = async (req, res) => {
  try {
    // get first collection
    const checkSetting = await setting.findOne();
    // if collection is empty, create new collection
    if (!checkSetting) {
      const newSetting = new setting({
        settingDP: req.body.settingDP,
        komisiReferral: req.body.komisiReferral,
      });
      await newSetting.save();
      return res.json({ message: "Setting saved successfully" });
    } else {
      // if collection is not empty, update collection
      checkSetting.settingDP = req.body.settingDP;
      checkSetting.komisiReferral = req.body.komisiReferral;
      await checkSetting.save();
      return res.json({ message: "Setting updated successfully" });
    }
  } catch (error) {
    return res.status(500).json({ message: error.message });
    
  }
}

module.exports = {
  saveSetting,
  getSetting
}
