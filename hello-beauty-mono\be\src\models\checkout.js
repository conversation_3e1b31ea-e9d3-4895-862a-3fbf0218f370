const mongoose = require('mongoose');

const checkoutSchema = new mongoose.Schema({
  muaId: {
    type: String,
    required: true,
  },
  muaName: {
    type: String,
    required: true,
  },
  trxId: {
    type: String,
    required: true,
  },
  pictureUrl: {
    type: String,
  },
  isManual: {
    type: Boolean,
    default: false,
  },
}, { timestamps: true })

module.exports = mongoose.model('checkout', checkoutSchema);
