const dotenv = require("dotenv")
const { encode } = require("../../helper/jwt")
const sendEmail = require("../../helper/send-email")
const user = require("../../models/user")
const bcrypt = require("bcrypt")
const md5 = require("md5")
const { validateEmail, validatePhone } = require("../../helper/validation")
const { logFunction, logError, logSuccess } = require("../../utils/logger")

dotenv.config()

const registerUser = async (req, res) => {
  const fnName = 'registerUser';
  logFunction(fnName, { email: req.body.email, phone: req.body.phone });

  try {
    const body = req.body

    // validate name
    if (!body.name || body.name.trim().length === 0) {
      logFunction(fnName, { error: 'Empty name' }, 'warn');
      return res.status(400).json({
        message: "<PERSON>a tidak boleh kosong"
      })
    }

    // validate email
    const emailValidation = validateEmail(body.email)
    if (!emailValidation.isValid) {
      logFunction(fnName, { error: 'Invalid email', message: emailValidation.message }, 'warn');
      return res.status(400).json({
        message: emailValidation.message
      })
    }

    // validate phone
    const phoneValidation = validatePhone(body.phone)
    if (!phoneValidation.isValid) {
      logFunction(fnName, { error: 'Invalid phone', message: phoneValidation.message }, 'warn');
      return res.status(400).json({
        message: phoneValidation.message
      })
    }

    // check Email
    let referralCode = body.name.split(" ").join("").toLowerCase().slice(0,5) + Math.floor(Math.random() * 99)
    const randomString = Math.random().toString(36).substring(7)
    referralCode = referralCode+randomString.slice(0,2)
    referralCode = referralCode.toUpperCase()
    
    const checkUser = await user.findOne({
      email: body.email
    })
    
    if (checkUser) {
      logFunction(fnName, { error: 'Email already registered' }, 'warn');
      return res.status(400).json({
        message: "Email sudah terdaftar"
      })
    }

    // check phone
    const checkPhone = await user.findOne({
      phone: phoneValidation.formattedPhone
    })

    if (checkPhone) {
      logFunction(fnName, { error: 'Phone already registered' }, 'warn');
      return res.status(400).json({
        message: "Whatsapp sudah terdaftar"
      })
    }

    // insert data
    const encPassword = await bcrypt.hashSync(body.password, 10)
    
    const newUser = new user({
      name: body.name,
      email: body.email,
      phone: phoneValidation.formattedPhone,
      password: encPassword,
      referralCode: referralCode
    })
    await newUser.save()
    
    const dataToken = {
      id: newUser._id,
      name: newUser.name,
      email: newUser.email,
      phone: newUser.phone,
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 365)
    }
    const token = await encode(dataToken)
    
    if(!newUser){
      logError(fnName, new Error('Failed to register user'));
      return res.status(400).json({
        message:"Gagal mendaftarkan user"
      })
    }

    logSuccess(fnName, { userId: newUser._id });
    return res.status(201).json({
      message: "Berhasil mendaftarkan user",
      data:{
        user: {
          name:newUser.name,
          email:newUser.email,
          phone:newUser.phone
        },
        token
      }
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: error.message||"Internal Server Error"
    })
  }
}

const loginUser = async (req, res) => {
  const fnName = 'loginUser';
  logFunction(fnName, { email: req.body.email });

  try {
    const body = req.body
    
    const checkUser = await user.findOne({
      email: body.email
    })
    if (!checkUser) {
      logFunction(fnName, { error: 'User not found' }, 'warn');
      return res.status(400).json({
        message: "Email atau Password salah"
      })
    }

    const checkPassword = await bcrypt.compare(body.password, checkUser.password)
    if (!checkPassword) {
      logFunction(fnName, { error: 'Invalid password' }, 'warn');
      return res.status(400).json({
        message: "Email atau Password salah"
      })
    }

    const dataToken = {
      id: checkUser._id,
      name: checkUser.name,
      email: checkUser.email,
      phone: checkUser.phone,
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 365)
    }
    const token = await encode(dataToken)

    logSuccess(fnName, { userId: checkUser._id });
    return res.status(200).json({
      message: "Berhasil login",
      data: {
        user: {
          name: checkUser.name,
          email: checkUser.email,
          phone: checkUser.phone
        },
        token
      }
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: error.message||"Internal Server Error"
    })
  }
}

const sendEmailForgotPassword = async (req, res) => {
  const fnName = 'sendEmailForgotPassword';
  logFunction(fnName, { email: req.body.email });

  try {
    const body = req.body
    const checkUser = await user.findOne({
      email: body.email
    })

    if (!checkUser) {
      logFunction(fnName, { error: 'User not found' }, 'warn');
      return res.status(400).json({
        message: "Email telah dikirim, Silahkan cek email anda"
      })
    }

    const randomToken = Math.random().toString(36).substring(7)
    const timestamp = new Date().getTime()
    const token = md5(randomToken + timestamp)
    checkUser.resetPasswordToken = token
    await checkUser.save()

    await sendEmail(checkUser.email, "Reset Password", `Klik link berikut untuk reset password anda: ${process.env.URL_FRONTEND}/reset-password?token=${token}`)

    logSuccess(fnName, { userId: checkUser._id });
    return res.status(200).json({
      message: "Email telah dikirim, Silahkan cek email anda"
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal Server Error"
    })
  }
}

const validateTokenResetPassword = async (req, res) => {
  const fnName = 'validateTokenResetPassword';
  logFunction(fnName, { token: req.body.token });

  try {
    const token = req.body.token
    const checkToken = await user.findOne({
      resetPasswordToken: token
    })

    if (!checkToken) {
      logFunction(fnName, { error: 'Invalid token' }, 'warn');
      return res.status(400).json({
        message: "Token tidak valid"
      })
    }

    checkToken.password = await bcrypt.hashSync(req.body.password, 10)
    checkToken.resetPasswordToken = ""
    await checkToken.save()

    logSuccess(fnName, { userId: checkToken._id });
    return res.status(200).json({
      message: "Berhasil reset password"
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal Server Error"
    })
  }
}

module.exports = {
  registerUser,
  loginUser,
  sendEmailForgotPassword,
  validateTokenResetPassword
}
