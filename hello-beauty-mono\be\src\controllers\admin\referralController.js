const { decode } = require("../../helper/jwt");
const mutasiReferral = require("../../models/mutasi-referral");
const user = require("../../models/user");
const transaction = require("../../models/transaction");

const addSaldoReferral = async (req, res) => {
  try {
    // Handle both API calls and direct calls
    const trxId = req?.body?.trxId || req;
    let adminId = "system";
    
    // If this is an API call (has res parameter)
    if (res) {
      const auth = req.headers.authorization;
      const u = await decode(auth);
      adminId = u.id;
    }

    // check trx
    const checkTrx = await transaction.findOne({
      trxId: trxId
    });

    if (!checkTrx) {
      if (res) return res.status(400).json({ message: "Transaksi tidak ditemukan" });
      throw new Error("Transaksi tidak ditemukan");
    }

    if(checkTrx.statusPayment !== "PAID") {
      if (res) return res.status(400).json({ message: "Transaksi belum lunas" });
      throw new Error("Transaksi belum lunas");
    }

    // check user
    const checkUser = await user.findOne({
      referralCode: checkTrx.referralCode
    });

    if (!checkUser) {
      if (res) return res.status(400).json({ message: "User tidak ditemukan" });
      throw new Error("User tidak ditemukan");
    }

    // check di mutasi referral
    const checkHistory = await mutasiReferral.findOne({
      userId: checkUser._id,
      description: "Komisi referral dari transaksi " + checkTrx.trxId
    });

    if (checkHistory) {
      if (res) return res.status(400).json({ message: "Komisi referral sudah ditambahkan" });
      throw new Error("Komisi referral sudah ditambahkan");
    }

    // check saldo referral
    checkUser.saldoReferral += checkTrx.komisiReferral;
    await checkUser.save();

    // create history
    const createHistory = await mutasiReferral.create({
      userId: checkUser._id,
      amount: checkTrx.komisiReferral,
      description: "Komisi referral dari transaksi " + checkTrx.trxId,
      type: "credit",
      adminId: adminId
    });

    if(!createHistory) {
      if (res) return res.status(400).json({
        message: "Gagal menambahkan saldo referral"
      });
      throw new Error("Gagal menambahkan saldo referral");
    }

    if (res) {
      return res.status(201).json({
        message: "Berhasil menambahkan saldo referral"
      });
    }
    return true;

  } catch (error) {
    if (res) {
      return res.status(500).json({ message: error.message });
    }
    throw error;
  }
}

const tarikReferral = async (req, res) => {
  try {
    const body = req.body;
    // body.userId: string
    // body.amount: number
    // body.description: string
    const auth = req.headers.authorization;
    const u = await decode(auth);
    const adminId = u.id

    // check user
    const checkUser = await user.findOne({
      _id: body.userId
    });

    if (!checkUser) {
      return res.status(400).json({ message: "User tidak ditemukan" });
    }

    // check saldo referral
    if (checkUser.saldoReferral < body.amount) {
      return res.status(400).json({ message: "Saldo referral tidak mencukupi" });
    }

    // update saldo referral
    checkUser.saldoReferral -= body.amount;
    await checkUser.save();

    // create history
    const updateHistory = await mutasiReferral.create({
      userId: body.userId,
      amount: body.amount,
      description: body.description,
      type: "debit",
      adminId: adminId
    });

    if(!updateHistory) {
      return res.status(400).json({ message: "Gagal menarik saldo referral" });
    }

    return res.status(201).json({ message: "Berhasil menarik saldo referral" });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
}

const listMutasiReferral = async (req, res) => {
  try {
    const q = req.query;

    const listPenarikan = await mutasiReferral.find({
      userId: q.userId
    }).sort({ _id: -1 })

    return res.status(200).json({ data: listPenarikan });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
}

module.exports = {
  tarikReferral,
  addSaldoReferral,
  listMutasiReferral
}


