<template>
  <div class="mt-4">
    <div class="grid grid-cols-1 border-t">
      <div class="w-full text-sm mt-4 grid grid-cols-1 gap-3 md:grid-cols-3">
        <div
          v-for="(l, i) in list"
          :key="i"
          class="p-4 rounded-lg border text-center"
        >
          <div class="">
            <img
              :src="`https://ui-avatars.com/api/?name=${l.muaName}&size=64&background=random&color=fff`"
              class="w-16 h-16 rounded-full mx-auto"
            />
            <p class="text-sm mt-2 font-semibold">
              {{ l.muaName }}
            </p>
          </div>
          <div class="mt-2">
            <p class="text-xs italic">"{{ l.note }}"</p>
          </div>
          <div class="mt-4 w-full">
            <div
              v-if="!checkSelected"
              class="grid grid-cols-2 w-full gap-2 mx-auto"
            >
              <button
                class="btn-primary text-xs h-8"
                @click.prevent="(showPilih = true), (selectedMua = l)"
              >
                <icon name="tabler:check" class="text-sm" />
                Pilih
              </button>
              <button
                class="btn-secondary text-xs h-8"
                @click.prevent="getDetailMua(l.muaId), (showMua = true)"
              >
                <icon name="tabler:eye" class="text-sm" />
                Lihat Profil
              </button>
            </div>
            <div>
              <span
                v-if="l.selected"
                class="text-green-500 bg-green-100 px-2 py-1 rounded-lg"
              >
                <icon name="lets-icons:check-fill" class="text-sm" />
                Terpilih
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!list.length" class="text-gray-400 text-center py-4">
      Belum ada penawaran
    </div>
    <!--  -->
    <client-only>
      <VueSidePanel
        v-model="showPilih"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="getDetail()"
      >
        <div class="p-4">
          <h3>Apakah anda yakin ingin memilih MUA ini?</h3>
          <div class="mb-3">
            <label class="text-gray-600 text-sm">Nama / MUA</label>
            <div>
              {{ selectedMua.muaName || "-" }}
            </div>
          </div>
          <div class="mb-3">
            <label class="text-gray-600 text-sm">Catatan</label>
            <div>
              {{ selectedMua.note || "-" }}
            </div>
          </div>

          <div>
            <label class="text-gray-600 text-sm">Rating</label>
            <div>
              {{ selectedMua.rating || "-" }}
            </div>
          </div>

          <div class="flex gap-2">
            <button
              :disabled="loadingPilih"
              class="btn-primary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="setPilihMua"
            >
              <icon v-if="loadingPilih" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loadingPilih ? "Menyimpan" : "Pilih MUA" }}
              </span>
            </button>
            <button
              :disabled="loadingPilih"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPilih = false"
            >
              Batal
            </button>
          </div>
        </div>
      </VueSidePanel>
      <VueSidePanel
        v-model="showMua"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
      >
        <div v-if="muaDetail.approval" class="p-4">
          <div
            class="flex gap-2 items-center justify-between mb-4"
            @click.prevent="showMua = false"
          >
            <div class="flex gap-2 items-center">
              <icon name="line-md:arrow-left" />
              <h2 class="font-bold">Detail MUA</h2>
            </div>

            <button class="btn-primary" @click.prevent="showPilih = true">
              Pilih MUA
            </button>
          </div>
          <div class="grid grid-cols-2 gap-3 !text-sm">
            <div class="mb-2">
              <label class="block text-xs font-semibold">Nama Lengkap</label>
              <span>{{ muaDetail.approval.name }}</span>
            </div>

            <div class="mb-2">
              <label class="block text-xs font-semibold">MUA</label>
              <span>{{ muaDetail.approval.profileName }}</span>
            </div>

            <div class="mb-2">
              <label class="block text-xs font-semibold">Rating</label>
              <div class="font-semibold text-primary flex items-center gap-1">
                <icon name="iconoir:star" class="" />
                {{ muaDetail.rating }}
              </div>
            </div>

            <div class="mb-2">
              <label class="block text-xs font-semibold"
                >Email / Whatsapp</label
              >
              <span>
                {{ muaDetail.approval.email }} / {{ muaDetail.approval.phone }}
              </span>
            </div>

            <div class="mb-2">
              <label class="block text-xs font-semibold">Instagram</label>
              <a
                :href="`https://instgram.com/${muaDetail.approval.instagram}`"
                target="_blank"
                class="text-primary"
              >
                {{ muaDetail.approval.instagram }}
                <icon name="iconamoon:arrow-top-right-1-bold" class="text-sm" />
              </a>
            </div>

            <div class="mb-2 col-span-2">
              <label class="block text-sm font-semibold">Layanan</label>
              <div class="mt-2">
                <div class="flex flex-wrap gap-2">
                  <div
                    v-for="(d, i) in muaDetail.approval.serviceType"
                    :key="i"
                    class="bg-primary-light rounded-full px-2 text-primary"
                  >
                    {{ d }}
                  </div>
                </div>
              </div>
            </div>

            <div class="mb-2">
              <label class="block text-sm font-semibold">Alamat</label>
              <span>
                {{ muaDetail.approval.address }}
              </span>
              <span class="block">
                {{ muaDetail.approval.locationName }}
              </span>
            </div>

            <div class="mb-2">
              <label class="block text-sm font-semibold">Akun Sebelumnya</label>
              <a
                :href="muaDetail.approval.linkHb"
                target="_blank"
                class="text-primary"
              >
                {{ muaDetail.approval.linkHb }}
                <icon name="iconamoon:arrow-top-right-1-bold" class="text-sm" />
              </a>
            </div>

            <div class="mb-2">
              <label class="block text-sm font-semibold"
                >Lama Menjadi MUA</label
              >
              <span>
                {{ muaDetail.approval.lamaMua || "-" }}
              </span>
            </div>

            <div class="mb-2">
              <label class="block text-sm font-semibold">Ikut Acara HB</label>
              <span>
                {{
                  muaDetail.approval.hasParticipated ? "Pernah" : "Tidak Pernah"
                }}
              </span>
            </div>
            <div class="mb-2">
              <label class="block text-sm font-semibold">Job HB </label>
              <span>
                {{ muaDetail.approval.hasJob ? "Pernah" : "Tidak Pernah" }}
                {{
                  muaDetail.approval.hasJob
                    ? `( ${muaDetail.approval.jumlah_job} x )`
                    : ""
                }}
              </span>
            </div>

            <div class="mb-2">
              <label class="block text-sm font-semibold"
                >Menjadi Partner / Kolaborasi</label
              >
              <span>
                {{ muaDetail.approval.hasCollaboration ? "" : "Tidak Pernah" }}
              </span>
              <span
                v-if="muaDetail.approval.hasCollaboration"
                class="block text-primary"
              >
                {{ muaDetail.approval.collaboration }}
              </span>
            </div>
            <div class="mb-2 col-span-2">
              <label class="block text-sm font-semibold"
                >Sertifikat Training</label
              >
              <div class="w-full">
                <div
                  v-for="(d, i) in muaDetail.approval.trainings"
                  :key="i"
                  class="mb-2"
                >
                  <a
                    :href="d"
                    target="_blank"
                    class="text-primary flex items-center border-b py-1 gap-2 justify-between"
                  >
                    <div>Training {{ i + 1 }}</div>
                    <icon
                      name="iconamoon:arrow-top-right-1-bold"
                      class="text-sm"
                    />
                  </a>
                </div>
                <div v-if="!muaDetail.approval.trainings.length">
                  <span class="text-gray-400">Belum ada training</span>
                </div>
              </div>
            </div>
            <div class="mb-2 col-span-2">
              <label class="block text-sm font-semibold"
                >Sertifikat Nasional</label
              >
              <div class="w-full">
                <div
                  v-for="(d, i) in muaDetail.approval.certificates"
                  :key="i"
                  class="mb-2"
                >
                  <a
                    :href="d"
                    target="_blank"
                    class="text-primary flex items-center border-b py-1 gap-2 justify-between"
                  >
                    <div>Sertifikat {{ i + 1 }}</div>
                    <icon
                      name="iconamoon:arrow-top-right-1-bold"
                      class="text-sm"
                    />
                  </a>
                </div>
                <div v-if="!muaDetail.approval.certificates.length">
                  <span class="text-gray-400">Belum ada sertifikat</span>
                </div>
              </div>
            </div>

            <div class="col-span-2">
              <div class="font-semibold">Portofolio</div>
              <div class="grid grid-cols-2">
                <div v-for="(p, i) in muaPortofolio" :key="i">
                  <img :src="p.fullUrl" class="w-full h-96 object-cover" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const emit = defineEmits({});

const showPilih = ref(false);
const showMua = ref(false);

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const selectedMua = ref({});

const checkSelected = computed(() => {
  return list.value.find((l) => l.selected);
});

const getDetail = async () => {
  try {
    loading.value = true;
    list.value = [];
    const { data } = await adminGet(`/bid/${props.id}`);
    loading.value = false;
    list.value = data.data;

    const selectedMua = list.value.find((v) => {
      return v.selected;
    });

    emit("setSelectedMua", selectedMua); // Corrected emit function call
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

const muaDetail = ref({});
const getDetailMua = async (muaId) => {
  try {
    loading.value = true;
    const { data } = await adminGet(`/mua/${muaId}`);
    loading.value = false;
    muaDetail.value = data.data;
    getPortofolio(muaId);
    // showMua.value = true
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};
const loadingPilih = ref(false);
const loadingPortofolio = ref(false);
const muaPortofolio = ref([]);

const getPortofolio = async (muaId) => {
  try {
    loadingPortofolio.value = true;
    muaPortofolio.value = [];
    const { data } = await adminGet(`/mua/portofolio/${muaId}`);
    loadingPortofolio.value = false;
    muaPortofolio.value = data.data;
  } catch (error) {
    loadingPortofolio.value = false;
    errorMsg.value = error.response.data.message;
  }
};

const setPilihMua = async () => {
  try {
    loading.value = true;
    const { data } = await adminPost(`/bid/select`, {
      muaId: selectedMua.value.muaId,
      trxId: props.id,
    });
    loading.value = false;
    showPilih.value = false;
    $toast.success(data.message);
    getDetail();
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

onMounted(() => {
  getDetail();
});
</script>
