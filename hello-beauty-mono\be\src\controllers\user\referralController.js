const user = require('../../models/user')
const {decodeJwtClient} = require('../../helper/jwt')
const transaction = require('../../models/transaction')
const { logFunction, logError, logSuccess } = require('../../utils/logger')
const mutasiReferral = require('../../models/mutasi-referral')

const historyReferral = async (req, res) => {
  const fnName = 'historyReferral';
  logFunction(fnName, { 
    userId: req.headers.authorization ? 'authenticated' : 'anonymous'
  });

  try {
    const auth =  req.headers.authorization
    const u = await decodeJwtClient(auth)

    const searchUser = await user.findById(u.id)
    if(!searchUser){
      logFunction(fnName, { error: 'User not found', userId: u.id }, 'warn');
      return res.status(404).json({
        message:"User tidak ditemukan"
      })
    }

    if(searchUser.referralCode === "" || !searchUser.referralCode){
      let referralCode = searchUser.name.split(" ").join("").toLowerCase().slice(0,5) + Math.floor(Math.random() * 99)
      const randomString = Math.random().toString(36).substring(7)
      referralCode = referralCode+randomString.slice(0,2)
      referralCode = referralCode.toUpperCase()
      // update referral code
      await user.findByIdAndUpdate(searchUser._id, { referralCode })
      logFunction(fnName, { error: 'No referral code', userId: u.id }, 'warn');
      return res.status(400).json({
        message:"Kamu tidak memiliki Referral Code"
      })
    }

    const listReferral = await mutasiReferral.find({
      userId: searchUser._id
    }).sort({ createdAt: -1 })

    if(!listReferral){
      logFunction(fnName, { error: 'No referral history', userId: u.id }, 'warn');
      return res.status(404).json({
        message:"Belum ada riwayat Referral"
      })
    }
    
    logSuccess(fnName, { 
      userId: u.id,
      referralCode: searchUser.referralCode,
      totalReferrals: listReferral.length
    });
    
    return res.status(200).json({
      message:"Success",
      data: listReferral
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

module.exports = {
  historyReferral
}
