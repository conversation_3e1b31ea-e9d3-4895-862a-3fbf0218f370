<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Team</h1>
        <button
          class="btn-primary flex items-center"
          @click.prevent="showPanel = true"
        >
          <icon name="jam:plus" class="text-xl" />
          Tambah
        </button>
      </div>
     

      <table class="w-full text-sm mt-4">
        <thead>
          <tr class="bg-gray-100 font-semibold">
            <td class="p-2">
              <p>Nama</p>
            </td>
            <td>
              <p>
                Email
              </p>
            </td>
      
            <td class="p-2">Waktu dibuat</td>
            <td />
          </tr>
        </thead>
        <tbody>
          <tr v-for="(d, i) in list" :key="i" class="hover:bg-gray-50 border-b">
            <td class="px-2 py-3">
              <p class="text-sm">
                {{ d.name }}
              </p>
            </td>
            <td>
              <p class="text-sm">
                {{ d.email }}
              </p>
              </td> 
            <td class="px-2 py-3">
              <p class="text-sm">
           
                {{ useMoment(d.createdAt).format("DD MMM, HH:mm") }}
              </p>
            </td>
            <td>
              <div class="flex gap-2">
                <button
                  class="btn-secondary text-xs"
                  @click.prevent="(selectedTeam = d), (showPanel = true)"
                >
                  <icon name="icon-park-twotone:edit" class="text-xs" />
                  Edit
                </button>
                <button
                  class="btn-danger text-xs"
                  @click.prevent="(hapusId = d), (showHapus = true)"
                >
                  <icon name="icon-park-twotone:delete" class="text-xs" />
                  Hapus
                </button>
              </div>
            </td>
          </tr>
        </tbody>

        <tbody v-if="errorMsg && !loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              <icon name="icon-park-twotone:data" class="text-2xl block" />
              {{ errorMsg }}
            </td>
          </tr>
        </tbody>

        <!-- tbody loding -->
        <tbody v-if="loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              Memuat
              <icon name="svg-spinners:3-dots-bounce" class="text-2xl" />
            </td>
          </tr>
        </tbody>
      </table>

      <div class="p-3">
        <div class="flex justify-end gap-2 items-center mx-auto">
          <button
            v-if="page > 1"
            class="btn-secondary"
            @click.prevent="page--, page < 1 ? (page = 1) : '', getData()"
          >
            Prev
          </button>
          <form @submit="getData()">
            <input
              v-model="page"
              class="text-sm py-2 w-10 text-center border rounded-lg"
              type="number"
            >
          </form>

          <button class="btn-secondary" @click.prevent="page++, getData()">
            Next
          </button>
        </div>
      </div>
      <form-team
        :show="showPanel"
        :data="selectedTeam"
        @closed="closePanel"
        @refresh="closePanel, getData()"
      />

      <hapus-team
        :show="showHapus"
        :data="hapusId"
        @closed="closePanel"
        @refresh="closePanel, getData()"
      />
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Team",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "team",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);


const hapusId = ref("");
const showHapus = ref(false);
const selectedTeam = ref({});
const showPanel = ref(false);

const closePanel = () => {
  showPanel.value = false;
  selectedTeam.value = {};
  hapusId.value = "";
  showHapus.value = false;
};

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(`/admin?page=${page.value}&limit=10`);
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

onMounted(() => {
  getData();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
