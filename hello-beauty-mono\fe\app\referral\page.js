"use client";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { api } from "../_helper/api";
import { useEffect, useState } from "react";
import LoadingFull from "../_component/loadingFull";
import convertRp from "../_helper/convertorp";
import moment from "moment";

export default function Profile() {
  const [isLogin, setIsLogin] = useState(false);
  const [loadingMe, setLoadingMe] = useState(true);
  const [user, setUser] = useState({});
  const [loadingReferral, setLoadingReferral] = useState(true);
  const [listReferral, setListReferral] = useState([]);
  const [errorReferral, setErrorReferral] = useState("Tidak ada data referral");
  const checkLogin = async () => {
    try {
      setLoadingMe(true);
      const res = await api("GET", "/me");
      setLoadingMe(false);
      setUser(res.data);
    } catch (error) {
      setLoadingMe(false);
      setIsLogin(false);
      window.location.href = "/login";
    }
  };

  const getHistoryReferral = async () => {
    try {
      setLoadingReferral(true);
      const res = await api("GET", "/referral/history");
      setLoadingReferral(false);
      setListReferral(res.data);
    } catch (error) {
      setLoadingReferral(false);
      console.log(error);
      setErrorReferral(error.message);
    }
  }
  useEffect(() => {
    checkLogin();
    getHistoryReferral();
  }, []);

  const copyText = (text) => {
    navigator.clipboard.writeText(text);
  }

  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4 justify-between max-w-[480px] sticky top-0 bg-white/80 backdrop-blur-sm">
          <Link href="/" className="hover:opacity-80 transition-opacity">
            <img src="/icons/arrow-left.svg" alt="Back" className="w-6 h-6" />
          </Link>
          <Link href="/profile" className="bg-hb-pink-light-2 p-2 rounded-full hover:opacity-80 transition-opacity">
            <Icon
              icon="icon-park-twotone:people"
              className="text-xl text-hb-pink"
            />
          </Link>
        </div>

        <div className="px-4 py-6">
          <h3 className="text-2xl font-semibold text-center mb-8">
            Referral<span className="text-hb-pink">.</span>
          </h3>

          {loadingMe ? (
            <LoadingFull />
          ) : (
            <div className="space-y-8">
              {/* Referral Balance Section */}
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <p className="text-center text-sm text-gray-600 mb-2">
                  Saldo Referral
                </p>
                <div className="text-hb-pink text-3xl font-bold mb-2 text-center">
                  {convertRp(user.saldoReferral || 0)}
                </div>
                <div className="text-center text-sm text-gray-500">
                  Untuk menarik saldo referral, silahkan hubungi admin.
                </div>
              </div>

              {/* Referral Code Section */}
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <p className="text-center text-sm text-gray-600 mb-4">
                  Kode Referral Saya
                </p>
                <div 
                  onClick={() => copyText(user.referralCode)} 
                  className="group cursor-pointer uppercase flex items-center justify-center gap-2 text-center text-hb-pink font-semibold p-4 bg-hb-pink-light-2 rounded-xl border-2 border-hb-pink hover:bg-hb-pink-light-3 transition-colors"
                >
                  {user.referralCode}
                  <Icon icon="akar-icons:copy" className="text-hb-pink text-lg group-hover:scale-110 transition-transform" />
                </div>
                <div className="text-center text-sm text-gray-500 mt-4">
                  Gunakan kode referral ini untuk mendapatkan komisi dari setiap order yang menggunakan kode referral anda.
                </div>
              </div>

              {/* Referral History Section */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2">
                    Riwayat Referral
                  </h3>
                  <div className="text-sm text-gray-500">
                    Hanya order yang selesai yang akan mendapatkan komisi referral.
                  </div>
                </div>

                {loadingReferral ? (
                  <div className="space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="animate-pulse bg-gray-100 h-20 rounded-xl"></div>
                    ))}
                  </div>
                ) : listReferral.length > 0 ? (
                  <div className="space-y-3">
                    {listReferral.map((item, index) => (
                      <div 
                        key={index} 
                        className="bg-white py-4  transition-shadow border-b"
                      >
                        <div className="flex items-center justify-between">
                          <div className="pr-3">
                            <p className="text-sm text-gray-800">
                              {moment(item.createdAt).format("DD MMMM YYYY, HH:mm")}
                            </p>
                            <p className="text-sm text-gray-500">{item.description}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500">Komisi</p>
                            <p className={`text-sm font-semibold ${item.type === "credit" ? "text-green-600" : "text-red-600"}`}>
                              {item.type === "credit" ? "+" : "-"}
                              {convertRp(item.amount)}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-white rounded-xl p-8 text-center">
                    <Icon 
                      icon="fluent:cloud-error-24-regular" 
                      className="text-5xl text-gray-400 mx-auto mb-4" 
                    />
                    <p className="text-gray-500">{errorReferral}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
