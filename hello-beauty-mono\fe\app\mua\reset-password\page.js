"use client"
import {Icon} from '@iconify/react';
import Link from 'next/link'
import {useState} from "react";
import ErrorSpan from '../../_component/errorSpan';
import {apiMuaPublic} from '../../_helper/api-mua';
import {setCookie} from '@/app/_helper/cookie';
import { useSearchParams } from 'next/navigation';

export default function Home() {

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [success, setSuccess] = useState(false);
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
   const handleSubmit = async (e) => {
    try {
      e.preventDefault()
      setError('')
      setLoading(true)
      if (newPassword.length < 8) {
        setError("Password harus lebih dari 8 karakter");
        setLoading(false);
        return;
      }
      if (newPassword !== confirmPassword) {
        setError("Passwords do not match");
        setLoading(false);
        return;
      }
      const res = await apiMuaPublic('POST', '/auth/validate-reset-password', {token: token, password:newPassword})
      
      setLoading(false)
      setSuccess(true);
      
    } catch (error) {
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div className="h-14 flex items-center px-4">
        <Link href="/login">
        <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link>
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="mt-20">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Reset Password<br/>MUA<span className="text-hb-pink">.</span></h3>
      </div>
      <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 px-4 mt-20">
        
        <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/fingerprint.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} 
          required value={newPassword} onChange={(e) => setNewPassword(e.target.value)}
          type={newPasswordVisible ? "text" : "password"} placeholder="New Password" className="w-full  focus:outline-none"  />
          <button
            type="button"
            onClick={() => setNewPasswordVisible(!newPasswordVisible)}
            className="ml-2"
          >
            <Icon icon={newPasswordVisible ? "mdi:eye-off" : "mdi:eye"} />
          </button>
        </div>
        <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/fingerprint.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} 
          required value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)}
          type={confirmPasswordVisible ? "text" : "password"} placeholder="Confirm New Password" className="w-full  focus:outline-none"  />
          <button
            type="button"
            onClick={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
            className="ml-2"
          >
            <Icon icon={confirmPasswordVisible ? "mdi:eye-off" : "mdi:eye"} />
          </button>
        </div>
      </div>
       <div className="px-4">
        {
          error && <ErrorSpan msg={error} />
        }
        {
          success && <div className="text-green-500">Password reset successfully!</div>
        }
      </div>

      <div className="mt-4 px-6">
        <button type="submit" disabled={loading} className="btn-primary items-center flex justify-center">
         {
            loading && <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin mr-2" />
          }
          Kirim
        </button>
        </div>
      </form>


      </div>
  );
}
