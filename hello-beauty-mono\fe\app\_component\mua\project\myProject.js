"use client"
import { apiMua } from "@/app/_helper/api-mua";
import { Icon } from "@iconify/react";
import Link from "next/link";
import moment from "@/app/_helper/moment";
import { useEffect, useState } from "react";

export default function MyProject() {
    const [myBid, setMyBid] = useState([]);
    const [loading, setLoading] = useState(true);
    const getMyBid = async () => {
    try {
      const response = await apiMua("GET", "/bid/selected");
      setLoading(false);
      setMyBid(response.data);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect (() => {
    getMyBid();
  }, []);

  return (
    <div className="p-3 border rounded-xl">
      <h3 className="font-semibold flex gap-1 items-center mb-2">
        <Icon icon="uil:moneybag" className="text-lg text-hb-pink" />
        <div className="text-sm mt-1">
        Project Saya
        </div>
      </h3>
      <div>
         <ul>
          {myBid.map((item, index) => {
            const formattedBookingDate = moment(item.idTrx.bookingDate).format("YYYY-MM-DD") + "T" + item.idTrx.bookingTime + ":00.000Z";
            return (
            <li key={index} className="bg-white py-4 border-y">
             
              <Link href={`/mua/project/${item.trxId}`} className="w-full flex items-center justify-between">
              <div>
              <div className="text-sm text-gray-600">
                {moment(formattedBookingDate).fromNow()}
              </div>
                <div>
                  {item.idTrx.address},{" "}
                  <span className="font-semibold">{item.idTrx.locationName}</span>
                </div>
                <div className="text-xs flex gap-1 items-center text-gray-600">
                  <Icon icon="akar-icons:calendar" className="h-3 " />
                  {moment(item.idTrx.bookingDate).format("DD MMM YYYY")}
                  {" Jam "}
                  {item.idTrx.bookingTime}
                </div>
              </div>

              <div className="flex gap-1 items-center text-hb-pink"> 
                <div className="text-sm">
                Detail
                </div>
                <Icon icon="mage:arrow-up-right-circle-fill" className=" text-lg"/>
              </div>
              </Link>
            </li>
          )})}

          {!loading && myBid.length === 0 && (
            <div className="  py-4 text-gray-500 text-center">
              <Icon icon="line-md:coffee-half-empty-twotone-loop" className="text-4xl mx-auto text-gray-300"/>
              <span className="text-xs">
              Belum ada project
              </span>
            </div>
          )}
        </ul>
      </div>
    </div>
  )
  }
