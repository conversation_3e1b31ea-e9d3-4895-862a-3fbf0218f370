const Voucher = require('../../models/voucher');
const { toRp } = require('../../helper/rupiah');
const { logFunction, logError, logSuccess } = require('../../utils/logger');

const checkVoucher = async (req, res) => {
  const fnName = 'checkVoucher';
  logFunction(fnName, { 
    code: req.query.code,
    amount: req.query.amount
  });

  try {
    const { code, amount } = req.query;

    if (!code) {
      logFunction(fnName, { error: 'Voucher code required' }, 'warn');
      return res.status(400).json({
        message: "Kode voucher harus diisi"
      });
    }

    if (!amount) {
      logFunction(fnName, { error: 'Purchase amount required' }, 'warn');
      return res.status(400).json({
        message: "Jumlah pembelian harus diisi"
      });
    }

    const voucher = await Voucher.findOne({ code });

    if (!voucher) {
      logFunction(fnName, { error: 'Invalid voucher code', code }, 'warn');
      return res.status(400).json({
        message: "Kode voucher tidak valid"
      });
    }

    // Check if voucher is active
    if (!voucher.isActive) {
      logFunction(fnName, { error: 'Inactive voucher', code }, 'warn');
      return res.status(400).json({
        message: "Voucher tidak aktif"
      });
    }

    // Check voucher dates
    const now = new Date();
    if (now < new Date(voucher.startDate) || now > new Date(voucher.endDate)) {
      logFunction(fnName, { 
        error: 'Voucher expired', 
        code,
        startDate: voucher.startDate,
        endDate: voucher.endDate
      }, 'warn');
      return res.status(400).json({
        message: "Voucher tidak berlaku pada periode ini"
      });
    }

    // Check usage limit
    if (voucher.usageLimit <= 0) {
      logFunction(fnName, { error: 'Voucher usage limit reached', code }, 'warn');
      return res.status(400).json({
        message: "Voucher telah mencapai batas penggunaan"
      });
    }

    // Check minimum purchase
    if (amount < voucher.minPurchase) {
      logFunction(fnName, { 
        error: 'Minimum purchase not met', 
        code,
        amount,
        minPurchase: voucher.minPurchase
      }, 'warn');
      return res.status(400).json({
        message: `Minimal pembelian untuk menggunakan voucher ini adalah ${toRp(voucher.minPurchase)}`
      });
    }

    // Calculate discount amount
    let discountAmount;
    if (voucher.discountType === 'percentage') {
      discountAmount = (amount * voucher.discountValue) / 100;
      if (discountAmount > voucher.maxDiscount) {
        discountAmount = voucher.maxDiscount;
      }
    } else {
      discountAmount = voucher.discountValue;
      if (discountAmount > voucher.maxDiscount) {
        discountAmount = voucher.maxDiscount;
      }
    }

    logSuccess(fnName, { 
      code,
      discountAmount,
      discountType: voucher.discountType,
      discountValue: voucher.discountValue
    });

    return res.status(200).json({
      message: "Voucher valid",
      data: {
        code: voucher.code,
        name: voucher.name,
        discountType: voucher.discountType,
        discountValue: voucher.discountValue,
        maxDiscount: voucher.maxDiscount,
        minPurchase: voucher.minPurchase,
        discountAmount: discountAmount
      }
    });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Terjadi kesalahan saat memvalidasi voucher"
    });
  }
};

module.exports = {
  checkVoucher
}; 
