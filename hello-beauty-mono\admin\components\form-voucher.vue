<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <form class="p-4" @submit.prevent="submitForm">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">
              {{ data.id ? "Edit" : "Tambah" }} Voucher
            </h2>
          </div>

          <div class="grid grid-cols-2 gap-4 !text-sm">
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Kode Voucher</label>
              <input
                v-model="form.code"
                type="text"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div class="col-span-2">
              <label class="text-sm font-semibold block">Nama Voucher</label>
              <input
                v-model="form.name"
                type="text"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div>
              <label class="text-sm font-semibold block">Tipe Diskon</label>
              <select
                v-model="form.discount_type"
                required
                :disabled="loading"
                class="form-input text-sm"
              >
                <option value="" disabled selected>Pilih Tipe Diskon</option>
                <option value="percentage">Persentase</option>
                <option value="fixed">Nominal</option>
              </select>
            </div>

            <div>
              <label class="text-sm font-semibold block">Nilai Diskon</label>
              <rupiah-input
                v-model="form.discount_value"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div>
              <label class="text-sm font-semibold block"
                >Diskon Maksimal (Rp)</label
              >
              <rupiah-input
                v-model="form.max_discount"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div>
              <label class="text-sm font-semibold block"
                >Minimal Pembelian (Rp)</label
              >
              <rupiah-input
                v-model="form.min_purchase"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div>
              <label class="text-sm font-semibold block">Tanggal Mulai</label>
              <input
                v-model="form.start_date"
                type="date"
                required
                :disabled="loading"
                :min="minStartDate"
                class="form-input text-sm"
                placeholder=""
                @change="validateDates"
              />
            </div>

            <div>
              <label class="text-sm font-semibold block"
                >Tanggal Berakhir</label
              >
              <input
                v-model="form.end_date"
                type="date"
                required
                :disabled="loading"
                :min="form.start_date || minStartDate"
                class="form-input text-sm"
                :class="{ 'border-red-500': dateError }"
                placeholder=""
                @change="validateDates"
              />
              <p v-if="dateError" class="text-red-500 text-xs mt-1">
                Tanggal berakhir harus setelah tanggal mulai
              </p>
            </div>

            <div>
              <label class="text-sm font-semibold block"
                >Batas Penggunaan</label
              >
              <input
                v-model="form.usage_limit"
                type="number"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div>
              <label class="text-sm font-semibold block">Status</label>
              <select
                v-model="form.is_active"
                required
                :disabled="loading"
                class="form-input text-sm"
              >
                <option value="" disabled selected>Pilih Status</option>
                <option :value="true">Aktif</option>
                <option :value="false">Non Aktif</option>
              </select>
            </div>
          </div>

          <div class="h-4" />
          <div class="flex gap-2">
            <button
              class="btn-primary text-sm flex items-center gap-1"
              :disabled="loading"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              class="btn-secondary text-sm flex items-center gap-1"
              :disabled="loading"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["closed", "refresh"]);

const showPanel = ref(false);
const loading = ref(false);
const dateError = ref(false);

// Get today's date in YYYY-MM-DD format
const today = new Date();
const minStartDate = ref(today.toISOString().split("T")[0]);

const form = ref({
  code: "",
  name: "",
  discount_type: "",
  discount_value: "",
  max_discount: "",
  min_purchase: "",
  start_date: "",
  end_date: "",
  usage_limit: "",
  is_active: true,
});

const validateDates = () => {
  if (form.value.start_date && form.value.end_date) {
    const start = new Date(form.value.start_date);
    const end = new Date(form.value.end_date);
    dateError.value = end <= start;
  } else {
    dateError.value = false;
  }
};

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;

    if (props.data._id) {
      // Format dates for input
      const startDate = props.data.startDate
        ? new Date(props.data.startDate).toISOString().split("T")[0]
        : "";
      const endDate = props.data.endDate
        ? new Date(props.data.endDate).toISOString().split("T")[0]
        : "";

      form.value = {
        code: props.data.code,
        name: props.data.name,
        discount_type: props.data.discountType,
        discount_value: props.data.discountValue,
        max_discount: props.data.maxDiscount,
        min_purchase: props.data.minPurchase,
        start_date: startDate,
        end_date: endDate,
        usage_limit: props.data.usageLimit,
        is_active: props.data.isActive,
      };
      validateDates();
    } else {
      form.value = {
        code: "",
        name: "",
        discount_type: "",
        discount_value: "",
        max_discount: "",
        min_purchase: "",
        start_date: minStartDate.value,
        end_date: "",
        usage_limit: "",
        is_active: true,
      };
      dateError.value = false;
    }
  },
  { deep: true }
);

const validateForm = () => {
  // Check if end date is after start date
  if (form.value.end_date && form.value.start_date >= form.value.end_date) {
    $toast.error("Tanggal berakhir harus setelah tanggal mulai");
    return false;
  }

  // Check if start date is not in the past
  if (form.value.start_date < minStartDate.value) {
    $toast.error("Tanggal mulai tidak boleh di masa lalu");
    return false;
  }

  // Check if discount value is valid
  if (
    form.value.discount_type === "percentage" &&
    form.value.discount_value > 100
  ) {
    $toast.error("Diskon persentase tidak boleh lebih dari 100%");
    return false;
  }

  // Check if max discount is valid
  if (
    form.value.max_discount &&
    form.value.min_purchase &&
    Number(form.value.max_discount) > Number(form.value.min_purchase)
  ) {
    $toast.error("Diskon maksimal tidak boleh lebih dari minimal pembelian");
    return false;
  }

  return true;
};

const submitForm = async () => {
  try {
    if (!validateForm()) return;

    loading.value = true;
    const payload = {
      code: form.value.code,
      name: form.value.name,
      discountType: form.value.discount_type,
      discountValue: form.value.discount_value,
      maxDiscount: form.value.max_discount,
      minPurchase: form.value.min_purchase,
      startDate: form.value.start_date,
      endDate: form.value.end_date,
      usageLimit: form.value.usage_limit,
      isActive: form.value.is_active,
    };

    if (props.data._id) {
      await adminPut(`/voucher/${props.data._id}`, payload);
    } else {
      await adminPost("/voucher", payload);
    }
    $toast.success("Voucher berhasil disimpan");
    emit("refresh");
  } catch (error) {
    $toast.error(
      `Terjadi kesalahan ${error.response?.data?.message || error.message}`
    );
  } finally {
    loading.value = false;
  }
};
</script> 
