<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="400px"
        @closed="$emit('closed')"
      >
        <div class="p-6">
          <div class="flex justify-between items-center">
            <h1 class="text-lg font-bold">Hapus MUA</h1>
          </div>
          <div class="mt-4">
            <p class="text-sm mb-3">
              Apakah anda yakin ingin menghapus MUA ini?
            </p>

            <div class="mb-3">
              <p class="text-xs">Nama MUA</p>
              <p class="text-sm font-semibold">{{ data?.name || "-" }}</p>
            </div>

            <div class="flex justify-start mt-4 gap-2">
              <button class="btn-danger" @click="save" :disabled="!data">
                <icon name="icon-park-twotone:delete" class="text-xs" />
                Hapus
              </button>
              <button
                class="p-2 bg-gray-300 text-gray-700 rounded-lg px-4 text-xs"
                @click="showPanel = false"
              >
                Batal
              </button>
            </div>
          </div>
        </div>

        <loader-full v-if="loading" />
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
import { computed } from "vue";
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);

const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = computed({
  get: () => props.show,
  set: (val) => {
    if (!val) {
      emit("closed");
    }
  },
});

const loading = ref(false);

const save = async () => {
  if (!props.data?._id) return;

  try {
    loading.value = true;
    const { data } = await adminDelete(`/mua/${props.data._id}`);
    loading.value = false;
    $toast.success(data.message);
    emit("refresh");
    showPanel.value = false;
  } catch (error) {
    loading.value = false;
    $toast.error(
      `Terjadi kesalahan ${error.response?.data?.message || error.message}`
    );
  }
};
</script> 
