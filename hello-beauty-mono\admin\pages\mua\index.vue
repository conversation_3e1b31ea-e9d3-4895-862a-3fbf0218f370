<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">MUA</h1>
      </div>
      <form
        class="grid grid-cols-2 md:grid-cols-4 p-3 gap-2 mt-4"
        @submit.prevent="getData()"
      >
        <input
          v-model="search.search"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Cari..."
          type="text"
        />

        <select
          v-model="search.isApproved"
          class="p-2 border border-gray-200 rounded-lg"
        >
          <option value="">Semua Status</option>
          <option value="true">Verified</option>
          <option value="false">Unverified</option>
        </select>

        <div class="grid grid-cols-2 gap-2">
          <button class="btn-primary" type="submit">Cari</button>
          <button
            type="button"
            class="btn-secondary"
            @click.prevent="
              (search = {
                search: '',
                isApproved: '',
              }),
                getData()
            "
          >
            Reset
          </button>
        </div>
      </form>
      <table class="w-full text-sm mt-4">
        <thead>
          <tr class="bg-gray-100 font-semibold">
            <td class="p-2 cursor-pointer" @click="sort('name')">
              <div class="flex items-center">
                <p>Nama</p>
                <span v-if="sortBy === 'name'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
              <p>MUA</p>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('email')">
              <div class="flex items-center">
                <p>Email</p>
                <span v-if="sortBy === 'email'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
              <p>Phone</p>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('rating')">
              <div class="flex items-center">
                <p>Rating</p>
                <span v-if="sortBy === 'rating'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('isApproved')">
              <div class="flex items-center">
                <p>Status</p>
                <span v-if="sortBy === 'isApproved'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td>Layanan</td>
            <td class="p-2" @click="sort('packageId')">
              Paket
              <span v-if="sortBy === 'packageId'">
                <icon
                  :name="
                    sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                  "
                />
              </span>
            </td>
            <td class="p-2 cursor-pointer" @click="sort('createdAt')">
              <div class="flex items-center">
                <p>Waktu Pendaftaran</p>
                <span v-if="sortBy === 'createdAt'">
                  <icon
                    :name="
                      sortOrder === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'
                    "
                  />
                </span>
              </div>
            </td>
            <td />
          </tr>
        </thead>
        <tbody>
          <tr v-for="(d, i) in list" :key="i" class="hover:bg-gray-50 border-b">
            <td class="px-2 py-3">
              <p class="text-sm">
                {{ d.name }}
              </p>
              <p class="text-sm">
                {{ d.muaName }}
              </p>
            </td>
            <td>
              <p class="text-sm">
                {{ d.email }}
              </p>
              <p class="text-sm">
                {{ d.phone }}
              </p>
            </td>
            <td class="px-4">
              <p class="text-sm text-primary">
                <icon name="iconoir:star" class="" />
                {{ d.rating }}
              </p>
            </td>
            <td>
              <status-akun :status="d.isApproved" />
            </td>
            <td>
              <div v-if="d.approval" class="max-w-[300px]">
                <div v-if="d.approval.serviceType">
                  <div class="flex flex-wrap gap-2">
                    <div
                      v-for="(d, i) in d.approval.serviceType"
                      :key="i"
                      class="bg-primary-light rounded-full px-2 text-primary"
                    >
                      {{ d }}
                    </div>
                  </div>
                </div>
                <div v-else>-</div>
              </div>
              <div v-else>-</div>
            </td>
            <td>
              <p class="text-sm">
                {{ d.packageName }}
              </p>
            </td>
            <td class="px-2 py-3">
              <p class="text-sm">
                {{ useMoment(d.createdAt).format("DD MMM, HH:mm") }}
              </p>
            </td>
            <td>
              <div class="flex gap-2">
                <Nuxt-Link class="btn-secondary" :to="`/mua/${d._id}`">
                  Detail
                </Nuxt-Link>
                <button class="btn-danger" @click="showDeletePanel(d)">
                  <icon name="icon-park-twotone:delete" class="text-xs" /> Hapus
                </button>
              </div>
            </td>
          </tr>
        </tbody>

        <tbody v-if="errorMsg && !loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              <icon name="icon-park-twotone:data" class="text-2xl block" />
              {{ errorMsg }}
            </td>
          </tr>
        </tbody>

        <!-- tbody loding -->
        <tbody v-if="loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              Memuat
              <icon name="svg-spinners:3-dots-bounce" class="text-2xl" />
            </td>
          </tr>
        </tbody>
      </table>

      <div class="p-3">
        <div class="flex justify-end gap-2 items-center mx-auto">
          <button
            v-if="page > 1"
            class="btn-secondary"
            @click.prevent="page--, page < 1 ? (page = 1) : '', getData()"
          >
            Prev
          </button>
          <form @submit="getData()">
            <input
              v-model="page"
              class="text-sm py-2 w-10 text-center border rounded-lg"
              type="number"
            />
          </form>

          <button class="btn-secondary" @click.prevent="page++, getData()">
            Next
          </button>
        </div>
      </div>
    </div>
    <hapus-mua
      :show="showDelete"
      :data="selectedMua"
      @closed="showDelete = false"
      @refresh="getData()"
    />
  </div>
</template>

<script setup>
import HapusMua from "~/components/mua/hapus-mua.vue";

definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "MUA",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "MUA",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);
const search = ref({
  search: "",
  isApproved: "",
});
const sortBy = ref("");
const sortOrder = ref("asc");
const showDelete = ref(false);
const selectedMua = ref(null);

const sort = (column) => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === "asc" ? "desc" : "asc";
  } else {
    sortBy.value = column;
    sortOrder.value = "asc";
  }
  getData();
};

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(
      `/mua?page=${page.value}&limit=10&search=${search.value.search}&isApproved=${search.value.isApproved}&sort_by=${sortBy.value}&sort_order=${sortOrder.value}`
    );
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

const showDeletePanel = (mua) => {
  selectedMua.value = mua;
  showDelete.value = true;
};

onMounted(() => {
  getData();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
