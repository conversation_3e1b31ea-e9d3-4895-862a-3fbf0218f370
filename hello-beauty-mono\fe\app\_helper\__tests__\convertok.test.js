import { convertToK } from '../convertok';

describe('convertToK', () => {
  it('should convert numbers to K format', () => {
    expect(convertToK(1000)).toBe('1K');
    expect(convertToK(1500)).toBe('1.5K');
    expect(convertToK(2000)).toBe('2K');
    expect(convertToK(2500)).toBe('2.5K');
  });

  it('should handle numbers less than 1000', () => {
    expect(convertToK(500)).toBe('0.5K');
    expect(convertToK(100)).toBe('0.1K');
    expect(convertToK(50)).toBe('0.05K');
  });

  it('should handle zero', () => {
    expect(convertToK(0)).toBe('0K');
  });

  it('should handle negative numbers', () => {
    expect(convertToK(-1000)).toBe('-1K');
    expect(convertToK(-1500)).toBe('-1.5K');
  });
}); 
