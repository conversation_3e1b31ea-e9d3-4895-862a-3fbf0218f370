{"version": 3, "sources": ["../../../../../../node_modules/vue3-side-panel/node_modules/body-scroll-lock/lib/bodyScrollLock.esm.js", "../../../../../../node_modules/vue3-side-panel/src/components/SidePanelCloseButton.vue", "../../../../../../node_modules/vue3-side-panel/src/components/SidePanel.vue", "../../../../../../node_modules/vue3-side-panel/src/VueSidePanel.ts"], "sourcesContent": ["function _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\n// Older browsers don't support event options, feature detect it.\n\n// Adopted and modified solution from <PERSON><PERSON><PERSON> (2017)\n// https://stackoverflow.com/questions/41594997/ios-10-safari-prevent-scrolling-behind-a-fixed-overlay-and-maintain-scroll-posi\n\nvar hasPassiveEvents = false;\nif (typeof window !== 'undefined') {\n  var passiveTestOptions = {\n    get passive() {\n      hasPassiveEvents = true;\n      return undefined;\n    }\n  };\n  window.addEventListener('testPassive', null, passiveTestOptions);\n  window.removeEventListener('testPassive', null, passiveTestOptions);\n}\n\nvar isIosDevice = typeof window !== 'undefined' && window.navigator && window.navigator.platform && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === 'MacIntel' && window.navigator.maxTouchPoints > 1);\n\n\nvar locks = [];\nvar documentListenerAdded = false;\nvar initialClientY = -1;\nvar previousBodyOverflowSetting = void 0;\nvar previousBodyPosition = void 0;\nvar previousBodyPaddingRight = void 0;\n\n// returns true if `el` should be allowed to receive touchmove events.\nvar allowTouchMove = function allowTouchMove(el) {\n  return locks.some(function (lock) {\n    if (lock.options.allowTouchMove && lock.options.allowTouchMove(el)) {\n      return true;\n    }\n\n    return false;\n  });\n};\n\nvar preventDefault = function preventDefault(rawEvent) {\n  var e = rawEvent || window.event;\n\n  // For the case whereby consumers adds a touchmove event listener to document.\n  // Recall that we do document.addEventListener('touchmove', preventDefault, { passive: false })\n  // in disableBodyScroll - so if we provide this opportunity to allowTouchMove, then\n  // the touchmove event on document will break.\n  if (allowTouchMove(e.target)) {\n    return true;\n  }\n\n  // Do not prevent if the event has more than one touch (usually meaning this is a multi touch gesture like pinch to zoom).\n  if (e.touches.length > 1) return true;\n\n  if (e.preventDefault) e.preventDefault();\n\n  return false;\n};\n\nvar setOverflowHidden = function setOverflowHidden(options) {\n  // If previousBodyPaddingRight is already set, don't set it again.\n  if (previousBodyPaddingRight === undefined) {\n    var _reserveScrollBarGap = !!options && options.reserveScrollBarGap === true;\n    var scrollBarGap = window.innerWidth - document.documentElement.clientWidth;\n\n    if (_reserveScrollBarGap && scrollBarGap > 0) {\n      var computedBodyPaddingRight = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'), 10);\n      previousBodyPaddingRight = document.body.style.paddingRight;\n      document.body.style.paddingRight = computedBodyPaddingRight + scrollBarGap + 'px';\n    }\n  }\n\n  // If previousBodyOverflowSetting is already set, don't set it again.\n  if (previousBodyOverflowSetting === undefined) {\n    previousBodyOverflowSetting = document.body.style.overflow;\n    document.body.style.overflow = 'hidden';\n  }\n};\n\nvar restoreOverflowSetting = function restoreOverflowSetting() {\n  if (previousBodyPaddingRight !== undefined) {\n    document.body.style.paddingRight = previousBodyPaddingRight;\n\n    // Restore previousBodyPaddingRight to undefined so setOverflowHidden knows it\n    // can be set again.\n    previousBodyPaddingRight = undefined;\n  }\n\n  if (previousBodyOverflowSetting !== undefined) {\n    document.body.style.overflow = previousBodyOverflowSetting;\n\n    // Restore previousBodyOverflowSetting to undefined\n    // so setOverflowHidden knows it can be set again.\n    previousBodyOverflowSetting = undefined;\n  }\n};\n\nvar setPositionFixed = function setPositionFixed() {\n  return window.requestAnimationFrame(function () {\n    // If previousBodyPosition is already set, don't set it again.\n    if (previousBodyPosition === undefined) {\n      previousBodyPosition = {\n        position: document.body.style.position,\n        top: document.body.style.top,\n        left: document.body.style.left\n      };\n\n      // Update the dom inside an animation frame \n      var _window = window,\n          scrollY = _window.scrollY,\n          scrollX = _window.scrollX,\n          innerHeight = _window.innerHeight;\n\n      document.body.style.position = 'fixed';\n      document.body.style.top = -scrollY;\n      document.body.style.left = -scrollX;\n\n      setTimeout(function () {\n        return window.requestAnimationFrame(function () {\n          // Attempt to check if the bottom bar appeared due to the position change\n          var bottomBarHeight = innerHeight - window.innerHeight;\n          if (bottomBarHeight && scrollY >= innerHeight) {\n            // Move the content further up so that the bottom bar doesn't hide it\n            document.body.style.top = -(scrollY + bottomBarHeight);\n          }\n        });\n      }, 300);\n    }\n  });\n};\n\nvar restorePositionSetting = function restorePositionSetting() {\n  if (previousBodyPosition !== undefined) {\n    // Convert the position from \"px\" to Int\n    var y = -parseInt(document.body.style.top, 10);\n    var x = -parseInt(document.body.style.left, 10);\n\n    // Restore styles\n    document.body.style.position = previousBodyPosition.position;\n    document.body.style.top = previousBodyPosition.top;\n    document.body.style.left = previousBodyPosition.left;\n\n    // Restore scroll\n    window.scrollTo(x, y);\n\n    previousBodyPosition = undefined;\n  }\n};\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollHeight#Problems_and_solutions\nvar isTargetElementTotallyScrolled = function isTargetElementTotallyScrolled(targetElement) {\n  return targetElement ? targetElement.scrollHeight - targetElement.scrollTop <= targetElement.clientHeight : false;\n};\n\nvar handleScroll = function handleScroll(event, targetElement) {\n  var clientY = event.targetTouches[0].clientY - initialClientY;\n\n  if (allowTouchMove(event.target)) {\n    return false;\n  }\n\n  if (targetElement && targetElement.scrollTop === 0 && clientY > 0) {\n    // element is at the top of its scroll.\n    return preventDefault(event);\n  }\n\n  if (isTargetElementTotallyScrolled(targetElement) && clientY < 0) {\n    // element is at the bottom of its scroll.\n    return preventDefault(event);\n  }\n\n  event.stopPropagation();\n  return true;\n};\n\nexport var disableBodyScroll = function disableBodyScroll(targetElement, options) {\n  // targetElement must be provided\n  if (!targetElement) {\n    // eslint-disable-next-line no-console\n    console.error('disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.');\n    return;\n  }\n\n  // disableBodyScroll must not have been called on this targetElement before\n  if (locks.some(function (lock) {\n    return lock.targetElement === targetElement;\n  })) {\n    return;\n  }\n\n  var lock = {\n    targetElement: targetElement,\n    options: options || {}\n  };\n\n  locks = [].concat(_toConsumableArray(locks), [lock]);\n\n  if (isIosDevice) {\n    setPositionFixed();\n  } else {\n    setOverflowHidden(options);\n  }\n\n  if (isIosDevice) {\n    targetElement.ontouchstart = function (event) {\n      if (event.targetTouches.length === 1) {\n        // detect single touch.\n        initialClientY = event.targetTouches[0].clientY;\n      }\n    };\n    targetElement.ontouchmove = function (event) {\n      if (event.targetTouches.length === 1) {\n        // detect single touch.\n        handleScroll(event, targetElement);\n      }\n    };\n\n    if (!documentListenerAdded) {\n      document.addEventListener('touchmove', preventDefault, hasPassiveEvents ? { passive: false } : undefined);\n      documentListenerAdded = true;\n    }\n  }\n};\n\nexport var clearAllBodyScrollLocks = function clearAllBodyScrollLocks() {\n  if (isIosDevice) {\n    // Clear all locks ontouchstart/ontouchmove handlers, and the references.\n    locks.forEach(function (lock) {\n      lock.targetElement.ontouchstart = null;\n      lock.targetElement.ontouchmove = null;\n    });\n\n    if (documentListenerAdded) {\n      document.removeEventListener('touchmove', preventDefault, hasPassiveEvents ? { passive: false } : undefined);\n      documentListenerAdded = false;\n    }\n\n    // Reset initial clientY.\n    initialClientY = -1;\n  }\n\n  if (isIosDevice) {\n    restorePositionSetting();\n  } else {\n    restoreOverflowSetting();\n  }\n\n  locks = [];\n};\n\nexport var enableBodyScroll = function enableBodyScroll(targetElement) {\n  if (!targetElement) {\n    // eslint-disable-next-line no-console\n    console.error('enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.');\n    return;\n  }\n\n  locks = locks.filter(function (lock) {\n    return lock.targetElement !== targetElement;\n  });\n\n  if (isIosDevice) {\n    targetElement.ontouchstart = null;\n    targetElement.ontouchmove = null;\n\n    if (documentListenerAdded && locks.length === 0) {\n      document.removeEventListener('touchmove', preventDefault, hasPassiveEvents ? { passive: false } : undefined);\n      documentListenerAdded = false;\n    }\n  }\n\n  if (isIosDevice) {\n    restorePositionSetting();\n  } else {\n    restoreOverflowSetting();\n  }\n};\n\n", "<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  name: 'SidePanelCloseButton',\n  emits: ['close'],\n});\n</script>\n\n<template>\n  <div class=\"vsp-close\" @click=\"$emit('close')\">\n    <span class=\"vsp-close__x\" />\n  </div>\n</template>\n\n<style lang=\"scss\">\n.vsp-close {\n  $this: &;\n\n  position: absolute;\n  top: 24px;\n  right: 24px;\n  display: inline-block;\n  width: 36px;\n  height: 36px;\n  text-align: center;\n  cursor: pointer;\n  background: white;\n  border-radius: 50%;\n  transition: background-color 0.1s;\n\n  &:hover {\n    background: #f7f7f7;\n  }\n\n  &__x {\n    position: relative;\n    top: 8px;\n    left: -1px;\n    display: inline-block;\n    width: 20px;\n    height: 20px;\n\n    &::before,\n    &::after {\n      position: absolute;\n      width: 2px;\n      height: 20px;\n      content: ' ';\n      background-color: #777;\n    }\n\n    &::before {\n      transform: rotate(-45deg);\n    }\n\n    &::after {\n      transform: rotate(45deg);\n    }\n\n    #{$this}:active & {\n      top: 9px;\n      left: -1px;\n    }\n  }\n\n  &:active {\n    top: 23px;\n    right: 23px;\n    width: 38px;\n    height: 38px;\n    background: #f1f1f1;\n  }\n}\n</style>\n", "<script lang=\"ts\">\nimport {\n  defineComponent,\n  onBeforeMount,\n  onBeforeUnmount,\n  PropType,\n  watch,\n  ref,\n  computed,\n  onMounted,\n  nextTick,\n} from 'vue';\nimport { disableBodyScroll, enableBodyScroll } from 'body-scroll-lock';\nimport SidePanelCloseButton from './SidePanelCloseButton.vue';\n\nexport default defineComponent({\n  name: 'VueSidePanel',\n  components: {\n    SidePanelCloseButton,\n  },\n  props: {\n    idName: {\n      type: String as PropType<string>,\n      default: 'vsp-container',\n    },\n    hideCloseBtn: {\n      type: Boolean as PropType<boolean>,\n      default: false,\n    },\n    noClose: {\n      type: Boolean as PropType<boolean>,\n      default: false,\n    },\n    side: {\n      type: String as PropType<string>,\n      validator: (value: string) => ['top', 'right', 'bottom', 'left'].includes(value),\n      default: 'right',\n    },\n    rerender: {\n      type: Boolean as PropType<boolean>,\n      default: false,\n    },\n    zIndex: {\n      type: [Number, String] as PropType<number | 'auto' | undefined>,\n      default: 'auto',\n    },\n    width: {\n      type: String as PropType<string>,\n      default: 'auto',\n    },\n    height: {\n      type: String as PropType<string>,\n      default: 'auto',\n    },\n    lockScroll: {\n      type: Boolean as PropType<boolean>,\n      default: false,\n    },\n    lockScrollHtml: {\n      type: Boolean as PropType<boolean>,\n      default: true,\n    },\n    modelValue: {\n      type: Boolean as PropType<boolean>,\n      default: false,\n      required: true,\n    },\n    overlayColor: {\n      type: String as PropType<string>,\n      default: 'black',\n    },\n    overlayOpacity: {\n      type: Number as PropType<number>,\n      default: 0.5,\n    },\n    overlayDuration: {\n      type: Number as PropType<number>,\n      default: 500,\n    },\n    panelColor: {\n      type: String as PropType<string>,\n      default: 'white',\n    },\n    panelDuration: {\n      type: Number as PropType<number>,\n      default: 300,\n    },\n    transitionName: {\n      type: String as PropType<string | undefined>,\n      default: undefined,\n    },\n    headerClass: {\n      type: String as PropType<string>,\n      default: '',\n    },\n    bodyClass: {\n      type: String as PropType<string>,\n      default: '',\n    },\n    footerClass: {\n      type: String as PropType<string>,\n      default: '',\n    },\n  },\n  emits: ['update:modelValue', 'closed', 'opened'],\n  setup(props, { emit, attrs }) {\n    let teleportContainer = undefined as HTMLDivElement | undefined;\n    const panel = ref<HTMLElement | null>(null);\n    const overlay = ref<HTMLElement | null>(null);\n    const footer = ref<HTMLElement | null>(null);\n    const header = ref<HTMLElement | null>(null);\n    const body = ref<HTMLElement | null>(null);\n    const footerHeight = ref(0);\n    const bodyScrollHeight = ref(0);\n    const headerHeight = ref(0);\n    const panelHeight = ref(0);\n    const windowHeight = ref(0);\n    const zIndex = ref<number>();\n    const isBodyAlreadyLocked = ref(false);\n\n    const calculateRightSize = async () => {\n      if (window?.innerHeight > 0) windowHeight.value = window.innerHeight;\n\n      footerHeight.value = footer.value ? footer.value.clientHeight : 0;\n      headerHeight.value = header.value ? header.value.clientHeight : 0;\n      bodyScrollHeight.value = body.value ? body.value.scrollHeight : 0;\n      panelHeight.value = panel.value ? panel.value.clientHeight : 0;\n    };\n\n    const closePanel = () => emit('update:modelValue', false);\n\n    const lockUnlockBodyScroll = (elem: HTMLElement, lock: boolean) => {\n      if (lock) {\n        setTimeout(() => {\n          disableBodyScroll(elem, { reserveScrollBarGap: true });\n          if (props.lockScrollHtml) document.documentElement.style.overflow = 'hidden';\n        }, 0);\n        return;\n      }\n\n      enableBodyScroll(elem);\n      if (props.lockScrollHtml) document.documentElement.style.removeProperty('overflow');\n    };\n\n    const getMaxZIndex = () =>\n      Math.max(\n        ...Array.from(document.querySelectorAll('body *'), (el) =>\n          parseFloat(window.getComputedStyle(el).zIndex),\n        ).filter((zIndex) => !Number.isNaN(zIndex)),\n        0,\n      );\n\n    onMounted(() => {\n      zIndex.value = props.zIndex === 'auto' ? getMaxZIndex() : props.zIndex;\n    });\n\n    onBeforeMount(() => {\n      const alreadyCreatedTarget = document.getElementById(props.idName);\n      if (!!alreadyCreatedTarget) return;\n      teleportContainer = document.createElement('div');\n      teleportContainer.setAttribute('id', props.idName);\n      document.body.appendChild(teleportContainer);\n    });\n\n    onBeforeUnmount(() => {\n      const { modelValue, lockScroll } = props;\n      if (lockScroll && panel.value && modelValue) lockUnlockBodyScroll(panel.value, false);\n      if (teleportContainer) document.body.removeChild(teleportContainer);\n      window.removeEventListener('resize', calculateRightSize);\n    });\n\n    watch(\n      () => [header.value, footer.value, props.height, props.width, props.side, props.modelValue],\n      () => {\n        nextTick(() => calculateRightSize());\n      },\n    );\n\n    watch(\n      () => [props.modelValue, panel.value],\n      (newP, oldP) => {\n        const wasShown = oldP ? (oldP[0] as boolean) : false;\n        const [isShown, panelEl] = newP as [boolean, HTMLElement | null];\n        const isOpening = isShown;\n        const isClosing = wasShown && !isShown;\n        if (!panelEl) return;\n        if (isOpening) isBodyAlreadyLocked.value = !!document.body.style.overflow;\n\n        if (isShown) {\n          if (props.lockScroll) lockUnlockBodyScroll(panelEl, true);\n          calculateRightSize();\n          window.addEventListener('resize', calculateRightSize);\n          return;\n        }\n\n        if (!props.lockScroll || !isClosing || isBodyAlreadyLocked.value) return;\n\n        setTimeout(() => {\n          if (panelEl) lockUnlockBodyScroll(panelEl, false);\n        }, props.panelDuration);\n        window.removeEventListener('resize', calculateRightSize);\n      },\n      { immediate: true },\n    );\n\n    const bodyHeight = computed<number | undefined>(() => {\n      if (!panelHeight.value) return;\n\n      const panelMaxHeight = bodyScrollHeight.value + headerHeight.value + footerHeight.value;\n      let height = panelHeight.value - headerHeight.value - footerHeight.value;\n\n      if (['top', 'bottom'].includes(props.side) && props.height === 'auto') {\n        height =\n          windowHeight.value >= panelMaxHeight\n            ? bodyScrollHeight.value\n            : windowHeight.value - headerHeight.value - footerHeight.value;\n      }\n\n      return height;\n    });\n\n    const overlayStyles = computed(() => ({\n      zIndex: zIndex.value,\n      animationDuration: `${props.overlayDuration}ms`,\n      '--overlay-opacity': props.overlayOpacity,\n      opacity: props.modelValue ? props.overlayOpacity : 0,\n      backgroundColor: props.overlayColor,\n      pointerEvents: !props.modelValue ? 'none' : 'all',\n    }));\n\n    const panelStyles = computed(() => ({\n      width: ['left', 'right'].includes(props.side) ? props.width : undefined,\n      maxWidth: '100%',\n\n      ...(['top', 'bottom'].includes(props.side)\n        ? {\n            // minHeight: props.height,\n            height: props.height,\n            maxHeight: '100%',\n          }\n        : {}),\n\n      zIndex: zIndex.value,\n      backgroundColor: props.panelColor,\n      animationDuration: `${props.panelDuration}ms`,\n\n      ...Object.assign({}, attrs.style),\n    }));\n\n    return {\n      body,\n      panel,\n      overlay,\n      overlayStyles,\n      header,\n      footer,\n      closePanel,\n      panelStyles,\n      bodyHeight,\n    };\n  },\n});\n</script>\n\n<template>\n  <teleport :to=\"`#${idName}`\">\n    <div class=\"vsp-wrapper\" :class=\"[modelValue && 'vsp-wrapper--active']\">\n      <Transition name=\"overlay\">\n        <div\n          v-show=\"modelValue\"\n          ref=\"overlay\"\n          class=\"vsp-overlay\"\n          :style=\"overlayStyles\"\n          @click=\"() => (noClose ? undefined : closePanel())\"\n        />\n      </Transition>\n      <Transition :name=\"transitionName || `slide-${side}`\" @after-enter=\"$emit('opened')\" @after-leave=\"$emit('closed')\">\n        <div\n          v-if=\"rerender ? modelValue : true\"\n          v-show=\"rerender ? true : modelValue\"\n          ref=\"panel\"\n          class=\"vsp\"\n          :class=\"[`vsp--${side}-side`, $attrs.class]\"\n          :style=\"panelStyles\"\n        >\n          <div v-if=\"!!$slots.header\" ref=\"header\" :class=\"[headerClass, 'vsp__header']\">\n            <slot name=\"header\" :close=\"closePanel\" />\n          </div>\n          <div ref=\"body\" :class=\"[bodyClass, 'vsp__body']\" :style=\"{ height: `${bodyHeight}px` }\">\n            <slot name=\"default\" :close=\"closePanel\" />\n            <SidePanelCloseButton v-if=\"!hideCloseBtn\" @close=\"closePanel\" />\n          </div>\n          <div v-if=\"!!$slots.footer\" ref=\"footer\" :class=\"[footerClass, 'vsp__footer']\">\n            <slot name=\"footer\" />\n          </div>\n        </div>\n      </Transition>\n    </div>\n  </teleport>\n</template>\n\n<style lang=\"scss\">\n.vsp-wrapper {\n  .vsp-overlay {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n\n  .vsp {\n    position: fixed;\n\n    &--right-side,\n    &--left-side {\n      top: 0;\n      height: 100%;\n    }\n\n    &--right-side {\n      right: 0;\n      left: unset;\n    }\n\n    &--left-side {\n      right: unset;\n      left: 0;\n    }\n\n    &--bottom-side,\n    &--top-side {\n      left: 0;\n      width: 100%;\n    }\n\n    &--bottom-side {\n      bottom: 0;\n    }\n\n    &--top-side {\n      top: 0;\n    }\n\n    &__header,\n    &__body,\n    &__footer {\n      overflow: auto;\n    }\n\n    &__body {\n      position: relative;\n    }\n  }\n\n  .overlay-enter-active,\n  .overlay-leave-active {\n    animation: overlay-transition;\n  }\n\n  .overlay-leave-active {\n    animation-direction: reverse;\n  }\n\n  .slide-right-enter-active,\n  .slide-right-leave-active {\n    animation: slide-right;\n  }\n\n  .slide-right-leave-active {\n    animation-direction: reverse;\n  }\n\n  .slide-left-enter-active,\n  .slide-left-leave-active {\n    animation: slide-left;\n  }\n\n  .slide-left-leave-active {\n    animation-direction: reverse;\n  }\n\n  .slide-top-enter-active,\n  .slide-top-leave-active {\n    animation: slide-left;\n  }\n\n  .slide-top-leave-active {\n    animation-direction: reverse;\n  }\n\n  .slide-top-enter-active,\n  .slide-top-leave-active {\n    animation: slide-top;\n  }\n\n  .slide-top-leave-active {\n    animation-direction: reverse;\n  }\n\n  .slide-bottom-enter-active,\n  .slide-bottom-leave-active {\n    animation: slide-bottom;\n  }\n\n  .slide-bottom-leave-active {\n    animation-direction: reverse;\n  }\n\n  @keyframes slide-right {\n    0% {\n      transform: translateX(100%);\n      opacity: 0;\n    }\n    100% {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes slide-left {\n    0% {\n      transform: translateX(-100%);\n      opacity: 0;\n    }\n    100% {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes slide-bottom {\n    0% {\n      transform: translateY(100%);\n      opacity: 0;\n    }\n    100% {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes slide-top {\n    0% {\n      transform: translateY(-100%);\n      opacity: 0;\n    }\n    100% {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes slide-top {\n    0% {\n      transform: translateY(-100%);\n      opacity: 0;\n    }\n    100% {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes overlay-transition {\n    0% {\n      opacity: 0;\n    }\n    100% {\n      opacity: var(--overlay-opacity);\n    }\n  }\n}\n</style>\n", "import { Plugin } from 'vue';\nimport SidePanel from './components/SidePanel.vue';\n\nexport default {\n  install(app) {\n    app.component('VueSidePanel', SidePanel);\n  },\n} as Plugin;\n"], "mappings": ";;;;AAAA,SAAS,mBAAmB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG,GAAG;AAAE,aAAS,IAAI,GAAG,OAAO,MAAM,IAAI,MAAM,GAAG,IAAI,IAAI,QAAQ,KAAK;AAAE,WAAK,CAAC,IAAI,IAAI,CAAC;AAAA,IAAG;AAAE,WAAO;AAAA,EAAM,OAAO;AAAE,WAAO,MAAM,KAAK,GAAG;AAAA,EAAG;AAAE;AAOlM,IAAI,mBAAmB;AACvB,IAAI,OAAO,WAAW,aAAa;AAC7B,uBAAqB;AAAA,IACvB,IAAI,UAAU;AACZ,yBAAmB;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,iBAAiB,eAAe,MAAM,kBAAkB;AAC/D,SAAO,oBAAoB,eAAe,MAAM,kBAAkB;AACpE;AARM;AAUN,IAAI,cAAc,OAAO,WAAW,eAAe,OAAO,aAAa,OAAO,UAAU,aAAa,iBAAiB,KAAK,OAAO,UAAU,QAAQ,KAAK,OAAO,UAAU,aAAa,cAAc,OAAO,UAAU,iBAAiB;AAGvO,IAAI,QAAQ,CAAC;AACb,IAAI,wBAAwB;AAC5B,IAAI,iBAAiB;AACrB,IAAI,8BAA8B;AAClC,IAAI,uBAAuB;AAC3B,IAAI,2BAA2B;AAG/B,IAAI,iBAAiB,SAASA,gBAAe,IAAI;AAC/C,SAAO,MAAM,KAAK,SAAU,MAAM;AAChC,QAAI,KAAK,QAAQ,kBAAkB,KAAK,QAAQ,eAAe,EAAE,GAAG;AAClE,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,iBAAiB,SAASC,gBAAe,UAAU;AACrD,MAAI,IAAI,YAAY,OAAO;AAM3B,MAAI,eAAe,EAAE,MAAM,GAAG;AAC5B,WAAO;AAAA,EACT;AAGA,MAAI,EAAE,QAAQ,SAAS,EAAG,QAAO;AAEjC,MAAI,EAAE,eAAgB,GAAE,eAAe;AAEvC,SAAO;AACT;AAEA,IAAI,oBAAoB,SAASC,mBAAkB,SAAS;AAE1D,MAAI,6BAA6B,QAAW;AAC1C,QAAI,uBAAuB,CAAC,CAAC,WAAW,QAAQ,wBAAwB;AACxE,QAAI,eAAe,OAAO,aAAa,SAAS,gBAAgB;AAEhE,QAAI,wBAAwB,eAAe,GAAG;AAC5C,UAAI,2BAA2B,SAAS,OAAO,iBAAiB,SAAS,IAAI,EAAE,iBAAiB,eAAe,GAAG,EAAE;AACpH,iCAA2B,SAAS,KAAK,MAAM;AAC/C,eAAS,KAAK,MAAM,eAAe,2BAA2B,eAAe;AAAA,IAC/E;AAAA,EACF;AAGA,MAAI,gCAAgC,QAAW;AAC7C,kCAA8B,SAAS,KAAK,MAAM;AAClD,aAAS,KAAK,MAAM,WAAW;AAAA,EACjC;AACF;AAEA,IAAI,yBAAyB,SAASC,0BAAyB;AAC7D,MAAI,6BAA6B,QAAW;AAC1C,aAAS,KAAK,MAAM,eAAe;AAInC,+BAA2B;AAAA,EAC7B;AAEA,MAAI,gCAAgC,QAAW;AAC7C,aAAS,KAAK,MAAM,WAAW;AAI/B,kCAA8B;AAAA,EAChC;AACF;AAEA,IAAI,mBAAmB,SAASC,oBAAmB;AACjD,SAAO,OAAO,sBAAsB,WAAY;AAE9C,QAAI,yBAAyB,QAAW;AACtC,6BAAuB;AAAA,QACrB,UAAU,SAAS,KAAK,MAAM;AAAA,QAC9B,KAAK,SAAS,KAAK,MAAM;AAAA,QACzB,MAAM,SAAS,KAAK,MAAM;AAAA,MAC5B;AAGA,UAAI,UAAU,QACV,UAAU,QAAQ,SAClB,UAAU,QAAQ,SAClB,cAAc,QAAQ;AAE1B,eAAS,KAAK,MAAM,WAAW;AAC/B,eAAS,KAAK,MAAM,MAAM,CAAC;AAC3B,eAAS,KAAK,MAAM,OAAO,CAAC;AAE5B,iBAAW,WAAY;AACrB,eAAO,OAAO,sBAAsB,WAAY;AAE9C,cAAI,kBAAkB,cAAc,OAAO;AAC3C,cAAI,mBAAmB,WAAW,aAAa;AAE7C,qBAAS,KAAK,MAAM,MAAM,EAAE,UAAU;AAAA,UACxC;AAAA,QACF,CAAC;AAAA,MACH,GAAG,GAAG;AAAA,IACR;AAAA,EACF,CAAC;AACH;AAEA,IAAI,yBAAyB,SAASC,0BAAyB;AAC7D,MAAI,yBAAyB,QAAW;AAEtC,QAAI,IAAI,CAAC,SAAS,SAAS,KAAK,MAAM,KAAK,EAAE;AAC7C,QAAI,IAAI,CAAC,SAAS,SAAS,KAAK,MAAM,MAAM,EAAE;AAG9C,aAAS,KAAK,MAAM,WAAW,qBAAqB;AACpD,aAAS,KAAK,MAAM,MAAM,qBAAqB;AAC/C,aAAS,KAAK,MAAM,OAAO,qBAAqB;AAGhD,WAAO,SAAS,GAAG,CAAC;AAEpB,2BAAuB;AAAA,EACzB;AACF;AAGA,IAAI,iCAAiC,SAASC,gCAA+B,eAAe;AAC1F,SAAO,gBAAgB,cAAc,eAAe,cAAc,aAAa,cAAc,eAAe;AAC9G;AAEA,IAAI,eAAe,SAASC,cAAa,OAAO,eAAe;AAC7D,MAAI,UAAU,MAAM,cAAc,CAAC,EAAE,UAAU;AAE/C,MAAI,eAAe,MAAM,MAAM,GAAG;AAChC,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,cAAc,cAAc,KAAK,UAAU,GAAG;AAEjE,WAAO,eAAe,KAAK;AAAA,EAC7B;AAEA,MAAI,+BAA+B,aAAa,KAAK,UAAU,GAAG;AAEhE,WAAO,eAAe,KAAK;AAAA,EAC7B;AAEA,QAAM,gBAAgB;AACtB,SAAO;AACT;AAEO,IAAI,oBAAoB,SAASC,mBAAkB,eAAe,SAAS;AAEhF,MAAI,CAAC,eAAe;AAElB,YAAQ,MAAM,gHAAgH;AAC9H;AAAA,EACF;AAGA,MAAI,MAAM,KAAK,SAAUC,OAAM;AAC7B,WAAOA,MAAK,kBAAkB;AAAA,EAChC,CAAC,GAAG;AACF;AAAA,EACF;AAEA,MAAI,OAAO;AAAA,IACT;AAAA,IACA,SAAS,WAAW,CAAC;AAAA,EACvB;AAEA,UAAQ,CAAC,EAAE,OAAO,mBAAmB,KAAK,GAAG,CAAC,IAAI,CAAC;AAEnD,MAAI,aAAa;AACf,qBAAiB;AAAA,EACnB,OAAO;AACL,sBAAkB,OAAO;AAAA,EAC3B;AAEA,MAAI,aAAa;AACf,kBAAc,eAAe,SAAU,OAAO;AAC5C,UAAI,MAAM,cAAc,WAAW,GAAG;AAEpC,yBAAiB,MAAM,cAAc,CAAC,EAAE;AAAA,MAC1C;AAAA,IACF;AACA,kBAAc,cAAc,SAAU,OAAO;AAC3C,UAAI,MAAM,cAAc,WAAW,GAAG;AAEpC,qBAAa,OAAO,aAAa;AAAA,MACnC;AAAA,IACF;AAEA,QAAI,CAAC,uBAAuB;AAC1B,eAAS,iBAAiB,aAAa,gBAAgB,mBAAmB,EAAE,SAAS,MAAM,IAAI,MAAS;AACxG,8BAAwB;AAAA,IAC1B;AAAA,EACF;AACF;AA4BO,IAAI,mBAAmB,SAASC,kBAAiB,eAAe;AACrE,MAAI,CAAC,eAAe;AAElB,YAAQ,MAAM,8GAA8G;AAC5H;AAAA,EACF;AAEA,UAAQ,MAAM,OAAO,SAAU,MAAM;AACnC,WAAO,KAAK,kBAAkB;AAAA,EAChC,CAAC;AAED,MAAI,aAAa;AACf,kBAAc,eAAe;AAC7B,kBAAc,cAAc;AAE5B,QAAI,yBAAyB,MAAM,WAAW,GAAG;AAC/C,eAAS,oBAAoB,aAAa,gBAAgB,mBAAmB,EAAE,SAAS,MAAM,IAAI,MAAS;AAC3G,8BAAwB;AAAA,IAC1B;AAAA,EACF;AAEA,MAAI,aAAa;AACf,2BAAuB;AAAA,EACzB,OAAO;AACL,2BAAuB;AAAA,EACzB;AACF;;;ACjRA,IAAA,WAAe,gBAAgB;EAC7B,MAAM;EACN,OAAO,CAAC,OAAO;CAChB;;;;;;;;;;;;;;;;;;;ACSD,IAAA,SAAe,gBAAgB;EAC7B,MAAM;EACN,YAAY;0BACVC;;EAEF,OAAO;IACL,QAAQ;MACN,MAAM;MACN,SAAS;;IAEX,cAAc;MACZ,MAAM;MACN,SAAS;;IAEX,SAAS;MACP,MAAM;MACN,SAAS;;IAEX,MAAM;MACJ,MAAM;MACN,WAAW,CAAC,UAAkB,CAAC,OAAO,SAAS,UAAU,MAAM,EAAE,SAAS,KAAK;MAC/E,SAAS;;IAEX,UAAU;MACR,MAAM;MACN,SAAS;;IAEX,QAAQ;MACN,MAAM,CAAC,QAAQ,MAAM;MACrB,SAAS;;IAEX,OAAO;MACL,MAAM;MACN,SAAS;;IAEX,QAAQ;MACN,MAAM;MACN,SAAS;;IAEX,YAAY;MACV,MAAM;MACN,SAAS;;IAEX,gBAAgB;MACd,MAAM;MACN,SAAS;;IAEX,YAAY;MACV,MAAM;MACN,SAAS;MACT,UAAU;;IAEZ,cAAc;MACZ,MAAM;MACN,SAAS;;IAEX,gBAAgB;MACd,MAAM;MACN,SAAS;;IAEX,iBAAiB;MACf,MAAM;MACN,SAAS;;IAEX,YAAY;MACV,MAAM;MACN,SAAS;;IAEX,eAAe;MACb,MAAM;MACN,SAAS;;IAEX,gBAAgB;MACd,MAAM;MACN,SAAS;;IAEX,aAAa;MACX,MAAM;MACN,SAAS;;IAEX,WAAW;MACT,MAAM;MACN,SAAS;;IAEX,aAAa;MACX,MAAM;MACN,SAAS;;;EAGb,OAAO,CAAC,qBAAqB,UAAU,QAAQ;EAC/C,MAAM,OAAO,EAAE,MAAM,MAAA,GAAO;AAC1B,QAAI,oBAAoB;AACxB,UAAM,QAAQ,IAAwB,IAAI;AAC1C,UAAM,UAAU,IAAwB,IAAI;AAC5C,UAAM,SAAS,IAAwB,IAAI;AAC3C,UAAM,SAAS,IAAwB,IAAI;AAC3C,UAAM,OAAO,IAAwB,IAAI;AACzC,UAAM,eAAe,IAAI,CAAC;AAC1B,UAAM,mBAAmB,IAAI,CAAC;AAC9B,UAAM,eAAe,IAAI,CAAC;AAC1B,UAAM,cAAc,IAAI,CAAC;AACzB,UAAM,eAAe,IAAI,CAAC;AAC1B,UAAM,SAAS,IAAG;AAClB,UAAM,sBAAsB,IAAI,KAAK;AAErC,UAAM,qBAAqB,YAAA;AACzB,UAAI,QAAQ,cAAc;AAAG,qBAAa,QAAQ,OAAO;AAEzD,mBAAa,QAAQ,OAAO,QAAQ,OAAO,MAAM,eAAe;AAChE,mBAAa,QAAQ,OAAO,QAAQ,OAAO,MAAM,eAAe;AAChE,uBAAiB,QAAQ,KAAK,QAAQ,KAAK,MAAM,eAAe;AAChE,kBAAY,QAAQ,MAAM,QAAQ,MAAM,MAAM,eAAe;;AAG/D,UAAM,aAAa,MAAM,KAAK,qBAAqB,KAAK;AAExD,UAAM,uBAAuB,CAAC,MAAmB,SAAa;AAC5D,UAAI,MAAM;AACR,mBAAW,MAAA;AACT,4BAAkB,MAAM,EAAE,qBAAqB,KAAA,CAAM;AACrD,cAAI,MAAM;AAAgB,qBAAS,gBAAgB,MAAM,WAAW;WACnE,CAAC;AACJ;;AAGF,uBAAiB,IAAI;AACrB,UAAI,MAAM;AAAgB,iBAAS,gBAAgB,MAAM,eAAe,UAAU;;AAGpF,UAAM,eAAe,MACnB,KAAK,IACH,GAAG,MAAM,KAAK,SAAS,iBAAiB,QAAQ,GAAG,CAAC,OAClD,WAAW,OAAO,iBAAiB,EAAE,EAAE,MAAM,CAAC,EAC9C,OAAO,CAACC,YAAW,CAAC,OAAO,MAAMA,OAAM,CAAC,GAC1C,CAAC;AAGL,cAAU,MAAA;AACR,aAAO,QAAQ,MAAM,WAAW,SAAS,aAAY,IAAK,MAAM;KACjE;AAED,kBAAc,MAAA;AACZ,YAAM,uBAAuB,SAAS,eAAe,MAAM,MAAM;AACjE,UAAI,CAAC,CAAC;AAAsB;AAC5B,0BAAoB,SAAS,cAAc,KAAK;AAChD,wBAAkB,aAAa,MAAM,MAAM,MAAM;AACjD,eAAS,KAAK,YAAY,iBAAiB;KAC5C;AAED,oBAAgB,MAAA;AACd,YAAM,EAAE,YAAY,WAAA,IAAe;AACnC,UAAI,cAAc,MAAM,SAAS;AAAY,6BAAqB,MAAM,OAAO,KAAK;AACpF,UAAI;AAAmB,iBAAS,KAAK,YAAY,iBAAiB;AAClE,aAAO,oBAAoB,UAAU,kBAAkB;KACxD;AAED,UACE,MAAM,CAAC,OAAO,OAAO,OAAO,OAAO,MAAM,QAAQ,MAAM,OAAO,MAAM,MAAM,MAAM,UAAU,GAC1F,MAAA;AACE,eAAS,MAAM,mBAAkB,CAAE;KACpC;AAGH,UACE,MAAM,CAAC,MAAM,YAAY,MAAM,KAAK,GACpC,CAAC,MAAM,SAAI;AACT,YAAM,WAAW,OAAQ,KAAK,CAAC,IAAgB;AAC/C,YAAM,CAAC,SAAS,OAAO,IAAI;AAC3B,YAAM,YAAY;AAClB,YAAM,YAAY,YAAY,CAAC;AAC/B,UAAI,CAAC;AAAS;AACd,UAAI;AAAW,4BAAoB,QAAQ,CAAC,CAAC,SAAS,KAAK,MAAM;AAEjE,UAAI,SAAS;AACX,YAAI,MAAM;AAAY,+BAAqB,SAAS,IAAI;AACxD,2BAAkB;AAClB,eAAO,iBAAiB,UAAU,kBAAkB;AACpD;;AAGF,UAAI,CAAC,MAAM,cAAc,CAAC,aAAa,oBAAoB;AAAO;AAElE,iBAAW,MAAA;AACT,YAAI;AAAS,+BAAqB,SAAS,KAAK;SAC/C,MAAM,aAAa;AACtB,aAAO,oBAAoB,UAAU,kBAAkB;OAEzD,EAAE,WAAW,KAAA,CAAM;AAGrB,UAAM,aAAa,SAA6B,MAAA;AAC9C,UAAI,CAAC,YAAY;AAAO;AAExB,YAAM,iBAAiB,iBAAiB,QAAQ,aAAa,QAAQ,aAAa;AAClF,UAAI,SAAS,YAAY,QAAQ,aAAa,QAAQ,aAAa;AAEnE,UAAI,CAAC,OAAO,QAAQ,EAAE,SAAS,MAAM,IAAI,KAAK,MAAM,WAAW,QAAQ;AACrE,iBACE,aAAa,SAAS,iBAClB,iBAAiB,QACjB,aAAa,QAAQ,aAAa,QAAQ,aAAa;;AAG/D,aAAO;KACR;AAED,UAAM,gBAAgB,SAAS,OAAO;MACpC,QAAQ,OAAO;MACf,mBAAmB,GAAG,MAAM,eAAe;MAC3C,qBAAqB,MAAM;MAC3B,SAAS,MAAM,aAAa,MAAM,iBAAiB;MACnD,iBAAiB,MAAM;MACvB,eAAe,CAAC,MAAM,aAAa,SAAS;MAC5C;AAEF,UAAM,cAAc,SAAS,OAAO;MAClC,OAAO,CAAC,QAAQ,OAAO,EAAE,SAAS,MAAM,IAAI,IAAI,MAAM,QAAQ;MAC9D,UAAU;MAEV,GAAI,CAAC,OAAO,QAAQ,EAAE,SAAS,MAAM,IAAI,IACrC;;QAEE,QAAQ,MAAM;QACd,WAAW;UAEb,CAAA;MAEJ,QAAQ,OAAO;MACf,iBAAiB,MAAM;MACvB,mBAAmB,GAAG,MAAM,aAAa;MAEzC,GAAG,OAAO,OAAO,CAAA,GAAI,MAAM,KAAK;MAChC;AAEF,WAAO;MACL;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;CAGL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClQD,IAAA,qBAAe;EACb,QAAQ,KAAG;AACT,QAAI,UAAU,gBAAgBC,MAAS;;;", "names": ["allowTouchMove", "preventDefault", "setOverflowHidden", "restoreOverflowSetting", "setPositionFixed", "restorePositionSetting", "isTargetElementTotallyScrolled", "handleScroll", "disableBodyScroll", "lock", "enableBodyScroll", "SidePanelCloseButton", "zIndex", "SidePanel"]}