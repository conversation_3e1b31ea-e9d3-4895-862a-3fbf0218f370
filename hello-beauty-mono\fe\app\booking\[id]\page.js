"use client";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { api } from "../../_helper/api";
import { useParams } from "next/navigation";
import moment from "../../_helper/moment";
import convertorp from "../../_helper/convertorp";
import LoadingFull from "../../_component/loadingFull";
import StatusDisplay from "@/app/_component/StatusDisplay";
import RatingReview from "../../_component/RatingReview";

const PaymentButton = ({ booking, paymentInfo }) => {
  if (!paymentInfo.linkPayment) return null;
  
  const buttonText = booking.dp === 100 
    ? "Bayar Sekarang" 
    : `Bayar ${paymentInfo.isDp} Sekarang`;

  return (
    <Link
      href={paymentInfo.linkPayment}
      className="btn-primary my-2 flex justify-center"
    >
      {buttonText}
    </Link>
  );
};

const PaymentCard = ({ item, dpPercentage }) => (
  <div className="bg-white p-3 border rounded-xl flex justify-between items-center">
    <div>
      <div className="text-xs text-gray-600 flex items-center gap-2">
        <div>#{item.payId}</div>
        {item.isDp && (
          <div className="text-xs bg-hb-pink-light-2 text-hb-pink px-2 rounded-full">
            DP {dpPercentage}%
          </div>
        )}
        {!item.isDp && (
          <div className="text-xs bg-hb-pink-light-2 text-hb-pink px-2 rounded-full">
            Pelunasan
          </div>
        )}
      </div>
      <div className="flex items-center gap-2">
        <div className="font-semibold text-lg">
          {convertorp(item.totalBayar)}
        </div>
      </div>
    </div>
    <div>
      {item.status === "PAID" ? (
        <span className="text-xs text-green-500">Lunas</span>
      ) : (
        <span className="text-xs text-red-500">Belum Lunas</span>
      )}
    </div>
  </div>
);

const RemainingPaymentInfo = ({ pay }) => {
  const remainingAmount = pay
    .filter((item) => item.status === "UNPAID")
    .reduce((acc, item) => acc + item.totalBayar, 0);

  return (
    <div className="p-3 bg-white border border-hb-pink text-hb-pink rounded-xl">
      <p className="text-xs">Sisa Pembayaran</p>
      <div className="font-semibold">{convertorp(remainingAmount)}</div>
      <p className="text-xs">Pelunasan dibayar sebelum H-1 dari Waktu Booking</p>
    </div>
  );
};

export default function DetailBooking() {
  const [booking, setBooking] = useState(null);
  const [pay, setPay] = useState([]);
  const [loading, setLoading] = useState(true);
  const params = useParams();

  const fetchBookingData = async () => {
    try {
      setLoading(true);
      const [bookingRes, paymentRes] = await Promise.all([
        api("GET", `/order/${params.id}`),
        api("GET", `/pay/list/${params.id}`)
      ]);
      setBooking(bookingRes.data);
      setPay(paymentRes.data);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const paymentInfo = useMemo(() => {
    if (!pay.length) return { linkPayment: "", isDp: "" };
    
    const unpaidPayment = pay.find(p => p.status === "UNPAID");
    if (!unpaidPayment) return { linkPayment: "", isDp: "" };
    
    return {
      linkPayment: `/pay/${unpaidPayment.payId}`,
      isDp: unpaidPayment.isDp ? "DP" : "Pelunasan"
    };
  }, [pay]);

  const handleReviewSuccess = () => {
    fetchBookingData();
  };

  useEffect(() => {
    fetchBookingData();
    document.title = "Booking Detail #" + params.id;
  }, []);

  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4 justify-between max-w-[480px]">
          <Link href={`/booking/history`}>
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
        </div>
        <div>
          <h3 className="text-2xl font-semibold text-center mb-6">
            Booking
            <br /> Detail<span className="text-hb-pink">.</span>
          </h3>
          {loading && <LoadingFull />}
          <div className="mt-4 p-3">
            {!loading && booking && (
              <div className="px-3">
                <div className="mb-3">
                  <span className="text-xs">Booking ID</span>
                  <h4 className="text-sm font-semibold">#{booking.trxId}</h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Paket</span>
                  <h4 className="text-sm font-semibold">
                    {booking.packageItemName} {booking.packageName}
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Harga Paket</span>
                  <h4 className="text-sm font-semibold">
                    {convertorp(booking.packagePrice)}
                  </h4>
                </div>
                {booking.discountAmount > 0 && (
                  <div className="mb-3">
                    <span className="text-xs">Potongan Voucher</span>
                    <h4 className="text-sm font-semibold text-green-600">
                      -{convertorp(booking.discountAmount)}
                    </h4>
                  </div>
                )}
                <div className="mb-3">
                  <span className="text-xs">Total Harga</span>
                  <h4 className="text-sm font-semibold">
                    {convertorp(booking.totalHarga)}
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Jumlah</span>
                  <h4 className="text-sm font-semibold">
                    {booking.pax} Pax
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Kontak</span>
                  <h4 className="text-sm font-semibold">{booking.name}</h4>
                  <h4 className="text-sm font-semibold">
                    {booking.phone} / {booking.email}
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Alamat</span>
                  <h4 className="text-sm font-semibold">
                    {booking.address}
                    <br />
                    {booking.locationName}
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Waktu Booking</span>
                  <h4 className="text-sm font-semibold">
                    {moment(booking.bookingDate).format("dddd, DD MMM YYYY")},
                    Jam {booking.bookingTime}
                  </h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Status Pembayaran</span>
                  <div className="mt-1">
                    {booking.statusPayment === 'PAID' ? (
                      <div className="inline-block text-xs bg-green-100 text-green-600 px-3 py-1 rounded-full font-medium">
                        <span className="flex items-center">
                          <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5"></span>
                          Lunas
                        </span>
                      </div>
                    ) : booking.statusPayment === 'DP_PAID' ? (
                      <div className="inline-block text-xs bg-amber-100 text-amber-600 px-3 py-1 rounded-full font-medium">
                        <span className="flex items-center">
                          <span className="w-1.5 h-1.5 bg-amber-500 rounded-full mr-1.5"></span>
                          DP Lunas
                        </span>
                      </div>
                    ) : (
                      <div className="inline-block text-xs bg-red-100 text-red-600 px-3 py-1 rounded-full font-medium">
                        <span className="flex items-center">
                          <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-1.5"></span>
                          Belum Lunas
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Status</span>
                  <StatusDisplay status={booking.status} />
                </div>
                {booking.note?.length > 0 && (
                  <div className="mb-3">
                    <span className="text-xs">Catatan</span>
                    <h4 className="text-sm font-semibold">{booking.note}</h4>
                  </div>
                )}
                <div className="pt-3 border-t border-dotted">
                  <h3 className="text-lg font-semibold">Pembayaran</h3>
                  {!loading && pay && pay.length > 0 && (
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {pay.map((item, index) => (
                        <div key={index}>
                          <PaymentCard item={item} dpPercentage={booking.dp} />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <div className="my-2">
                  {booking.dp !== 100 && pay.some((item) => item.status === "UNPAID") && (
                    <RemainingPaymentInfo pay={pay} />
                  )}
                  {!pay.every((item) => item.status === "PAID") && (
                    <PaymentButton booking={booking} paymentInfo={paymentInfo} />
                  )}
                </div>
                {booking.status === "DONE" && (
                  <div className="mt-6">
                    <RatingReview 
                      bookingId={booking.trxId} 
                      initialRating={booking.rating || 0} 
                      initialReview={booking.review || ""} 
                      onSuccess={handleReviewSuccess}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
