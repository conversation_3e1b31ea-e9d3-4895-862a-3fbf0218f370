<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="font-bold text-xl">Pengaturan Sistem</h1>
      </div>

      <div class="grid grid-cols-2 gap-2 py-4 border-t">
        <div class="col-span-2 text-lg font-semibold">Referral</div>
        <div>
          <label class="block text-xs">Komisi Referral (<PERSON><PERSON>)</label>
          <input
            v-model="form.komisiReferral"
            class="form-input"
            type="number"
            min="0"
            max="100"
            placeholder="Komisi Referral"
          />
        </div>
      </div>

      <div class="grid grid-cols-2 gap-2 py-4 border-t">
        <div class="col-span-2 text-lg font-semibold">Down Payment</div>

        <div class="col-span-2">
          <table class="min-w-full bg-white">
            <thead>
              <tr>
                <th class="py-2"><PERSON><PERSON></th>
                <th class="py-2"><PERSON><PERSON></th>
                <th class="py-2"></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in form.settingDP" :key="index">
                <td class="border px-4 py-2">
                  <input
                    v-model="item.persen"
                    class="form-input"
                    type="number"
                    min="0"
                    max="100"
                  />
                </td>
                <td class="border px-4 py-2">
                  <input
                    v-model="item.min_order_day"
                    class="form-input"
                    type="number"
                    min="0"
                  />
                </td>
                <td class="border px-4 py-2">
                  <button
                    @click="removeDownPayment(index)"
                    class="text-red-500"
                  >
                    Hapus
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
          <button
            @click="addDownPayment"
            class="mt-2 bg-blue-500 text-white px-4 py-2 rounded"
          >
            Tambah
          </button>
        </div>
      </div>

      <div class="flex justify-start mt-4">
        <button
          @click="saveSettings"
          class="btn-primary flex items-center justify-center"
        >
          <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
          Save Settings
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Pengaturan Sistem",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "team",
    },
  ],
});

const form = ref({
  komisiReferral: 5,
  settingDP: [
    { persen: 100, min_order_day: 3 },
    { persen: 75, min_order_day: 100 },
  ],
});

function addDownPayment() {
  form.value.settingDP.push({ persen: 0, min_order_day: 0 });
}

function removeDownPayment(index) {
  form.value.settingDP.splice(index, 1);
}

const loading = ref(false);

const saveSettings = async () => {
  try {
    loading.value = true;
    const { data } = await adminPost("/setting", form.value);

    loading.value = false;
    $toast.success(data.message);
  } catch (error) {
    $toast.error(error.response.data.message);
  }
};

const getSetting = async () => {
  try {
    const { data } = await adminGet("/setting");

    if (data.data) {
      form.value = data.data;
    }
  } catch (error) {
    $toast.error(error.response.data.message);
  }
};
onMounted(() => {
  getSetting();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
