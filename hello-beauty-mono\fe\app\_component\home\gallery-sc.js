// Import Swiper React components
import {Swiper, SwiperSlide} from 'swiper/react';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';

// import './styles.css';
// import required modules
import {Pagination} from 'swiper/modules';
import Image from 'next/image';

const GallerySc = () => {
  return (
    <Swiper slidesPerView={2} autoplay={{delay: 2500,disableOnInteraction: false,}} centeredSlides={true} spaceBetween={8} pagination={{clickable: true,}} modules={[Pagination]}>
        <SwiperSlide>
          <Image src="/images/images-1.png" width="400" height="320" className="h-[320px] object-cover" alt="Gallery" />
        </SwiperSlide>
         <SwiperSlide>
          <Image src="/images/images-2.jpeg" width="400" height="320" className="h-[320px]  object-cover" alt="Gallery" />
        </SwiperSlide>
         <SwiperSlide>
          <Image src="/images/images-3.jpeg" width="400" height="320" className="h-[320px] object-cover" alt="Gallery" />
        </SwiperSlide>
         <SwiperSlide>
          <Image src="/images/images-4.jpeg" width="400" height="320" className="h-[320px] object-cover" alt="Gallery" />
        </SwiperSlide>
         <SwiperSlide>
          <Image src="/images/images-5.jpeg" width="400" height="320" className="h-[320px] object-cover" alt="Gallery" />
        </SwiperSlide>
         <SwiperSlide>
          <Image src="/images/images-6.jpeg" width="400" height="320" className="h-[320px] object-cover" alt="Gallery" />
        </SwiperSlide>
      
      </Swiper>
  );
}

export default GallerySc;
