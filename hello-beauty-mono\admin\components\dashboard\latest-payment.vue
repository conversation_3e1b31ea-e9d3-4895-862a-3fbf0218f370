<template>
  <div class="border-t ">
    <h1 class="text-sm font-semibold py-4">
      Pembayaran Terbaru
    </h1>
    <div>
      <table class="w-full">
        <thead class="bg-gray-100">
          <tr>
            <th class="text-left p-2 text-xs">Order ID</th>
            <th class="text-left p-2 text-xs">Total Bayar</th>
            <th class="text-left p-2 text-xs">Status</th>
            <th class="text-left p-2 text-xs">Waktu Booking</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in list" :key="item.id" class="border-b hover:bg-gray-100">
            <td class="p-2 text-left text-xs">
              <nuxt-link :to="'/order/' + item.trxId" target="new_tab" class="hover:font-bold hover:text-primary">
                {{ item.trxId }}
                </nuxt-link>
              </td>
            <td class="p-2 text-left text-xs font-semibold">{{ useRupiah(item.totalBayar) }}</td>
            <td class="p-2 text-left text-xs">
              <status-pay :status="item.status" />
            </td>
            <td class="p-2 text-left text-xs">
              {{useMoment(item.paidAt).format('DD MMM YYYY, HH:mm')}}
              <span class="text-[10px] block text-gray-500">
                {{useMoment(item.paidAt).fromNow()}}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
 
    </div>
  </div>
</template>


<script setup>

const list = ref([])
const loading = ref(false)
const getList = async () => {
  loading.value = true
  try {
    const { data } = await adminGet('/payment/success')
    list.value = data.data
  } catch (error) {
    errorMsg.value = error.response.data.message
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getList()
})
</script>

