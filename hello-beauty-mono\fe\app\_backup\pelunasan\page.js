import Link from 'next/link'

export default function Home() {
  return (
    <div className="max-w-[480px] min-h-screen mx-auto ">
      <div className="h-14 flex items-center px-4">
        {/* <Link href="/">
        <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link> */}
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="dot-bg min-h-[400px]">
         <div className="pt-12 px-4">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Pelunasan<span className="text-hb-pink">.</span></h3>

          <div className="rounded-xl border p-4 grid grid-cols-1 gap-3 bg-white">
            <div className="grid grid-cols-2 border-b">
              <div className="py-3">
                Nama
              </div>
              <div className="text-right bg-hb-pink py-3 px-2 text-white rounded-tr-lg">
                J<PERSON>
              </div>
            </div>
            <div className="grid grid-cols-2 border-b">
              <div className="py-3">
                Nomor Booking
              </div>
              <div className="text-right bg-hb-pink py-3 px-2 text-white rounded-tr-lg">
                493
              </div>
            </div>
            <div className="grid grid-cols-2 border-b">
              <div className="py-3">
                Nominal Pesanan
              </div>
              <div className="text-right bg-hb-pink py-3 px-2 text-white rounded-tr-lg">
                2150K
              </div>
            </div>
          </div>

          <p className="mt-6 text-gray-500 text-center">
            ex Praesent elit ipsum non. dignissim, Nam convallis. dignissim, in nisl. adipiscing In Nam amet.
          </p>
      </div>
      

      </div>

      
      <div className="mt-20 px-4">
        <Link href="/" className="btn-primary flex justify-center">
          PAY
        </Link>
        </div>
      </div>
  );
}
