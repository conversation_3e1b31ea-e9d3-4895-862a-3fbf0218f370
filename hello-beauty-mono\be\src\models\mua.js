const mongoose = require("mongoose");

const muaSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    muaName: {
      type: String,
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    slug: {
      type: String,
    },
    phone: {
      type: String,
      required: false,
      unique: true,
      sparse: true,
    },
    password: {
      type: String,
      required: true,
    },
    googleId: {
      type: String,
    },
    profilePicture: {
      type: String,
    },
    isApproved: {
      type: Boolean,
      default: false,
    },
    // join with approval
    approval: {
      type: Object,
      default: {},
    },
    locationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "location",
    },
    packageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "package",
    },
    packageName: {
      type: String,
    },
    rating: {
      type: Number,
      default: 0,
    },
    resetPasswordToken:{
      type: String,
    },
    photo:{
      type:String
    },
    isInvalidData: {
      type: Boolean,
      default: false
    },
    invalidDataReason: {
      email: String,
      phone: String
    }
  },
  { timestamps: true },
);

module.exports = mongoose.model("mua", muaSchema);
