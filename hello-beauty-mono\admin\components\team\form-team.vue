<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed'), closePanel()"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">
              {{ payload._id ? 'Edit' : 'Tambah' }} Team
            </h2>
          </div>
          <div class="grid grid-cols-2 gap-4 !text-sm">
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Nama</label>
              <input
                v-model="payload.name"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              >
            </div>
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Email</label>
              <input
                v-model="payload.email"
                required
                :disabled="loading"
                type="email"
                class="form-input text-sm"
                placeholder=""
              >
            </div>
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Phone</label>
              <input
                v-model="payload.phone"
                required
                :disabled="loading"
                type="text"
                class="form-input text-sm"
                placeholder=""
              >
            </div>
             <div v-if="!payload._id" class="col-span-2">
              <label class="text-sm font-semibold block">Password</label>
              <input
                v-model="payload.password"
                required
                :disabled="loading"
                type="password"
                class="form-input text-sm"
                placeholder=""
              >
            </div>
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Menu</label>
              <div v-if="showMenu" class="grid grid-cols-2 gap-3">
                <div v-for="(mn,i) in menu" :key="i" class="rounded-lg cursor-pointer flex gap-2 border p-2" :class="selectedMenu.includes(mn) ?'bg-primary-light !text-primary border-primary':'text-gray-500 '" @click.prevent="addOrRemoveMenu(mn)" >
                  <Icon
                    :name="mn.icon"
                    size="20"/>
                  {{ mn .label}}
                </div>
              </div>
            </div>
          </div>
          <div class="h-4" />
          <div class="flex gap-2">
            <button
              class="btn-primary text-sm flex items-center gap-1"
              :disabled="loading"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? 'Menyimpan' : 'Simpan' }}
              </span>
            </button>
            <button
              class="btn-secondary text-sm flex items-center gap-1"
              :disabled="loading"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
import menu from '@/lib/menuadmin'
const { $toast } = useNuxtApp();
const emit = defineEmits(['closed', 'refresh']);


const props = defineProps({
  show: Boolean,
  data: Object
});

const showPanel = ref(false);

const selectedMenu = ref([])
const closePanel = () => {
  showPanel.value = false;
  selectedMenu.value = []
  payload.value = {}
};
const addOrRemoveMenu = (menuObject) => {
  const idx = selectedMenu.value.findIndex(m => m.label === menuObject.label)
  if (idx === -1) {
    selectedMenu.value.push(menuObject)
  } else {
    selectedMenu.value.splice(idx, 1)
  }
};

const payload = ref({
});
const showMenu = ref(false)
const loading = ref(false);

watch(
  () => props.show,
  val => {
    showPanel.value = !!val;

    if (props.data._id) {
      payload.value = { ...props.data };
      props.data.menu.forEach(m => {
        const idx = menu.findIndex(x => x.label === m.label)
        if (idx !== -1) {
          selectedMenu.value.push(menu[idx])
        }
      });
    }

    setTimeout(() => {
      showMenu.value = true
    }, 200);
  },
  { deep: true }
);

const save = async () => {
  try {
    loading.value = true;
    // sort menu
    selectedMenu.value = selectedMenu.value.sort((a, b) => a.sort - b.sort)
    const p = {
      ...payload.value,
      menu: selectedMenu.value
    };

    let res;
    if (props.data._id) {
      res = await adminPut(`/admin?id=${props.data._id}`, p);
    } else {
      res = await adminPost('/admin', p);
    }
    loading.value = false;
    $toast.success(res.data.message);
    emit('closed');
    emit('refresh');
    // window.location.reload();
  } catch (error) {
    console.log(error);
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
