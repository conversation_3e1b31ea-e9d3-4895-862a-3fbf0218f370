const approval = require("../../models/approval");
const { decodeJwtClient } = require("../../helper/jwt");

const addApproval = async (req, res) => {
  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const body = req.body;

    // Validasi field yang diperlukan
    const requiredFields = {
      name: "Nama tidak boleh kosong",
      profileName: "Nama profil tidak boleh kosong",
      serviceType: "<PERSON>is layanan tidak boleh kosong",
      instagram: "Instagram tidak boleh kosong",
      email: "Email tidak boleh kosong",
      phone: "Telepon tidak boleh kosong",
      address: "Alamat tidak boleh kosong",
      location: "Lokasi tidak boleh kosong",
      linkHb: "Link HB tidak boleh kosong",
      hasTraining: "Training tidak boleh kosong",
      hasParticipated: "Partisipasi tidak boleh kosong",
      isCertified: "Sertifikasi tidak boleh kosong",
      hasJob: "Pekerjaan tidak boleh kosong",
      hasCollaboration: "Kolaborasi tidak boleh kosong",
      // jumlah_job: "Jumlah pekerjaan tidak boleh kosong",
      locationid: "Lokasi tidak boleh kosong",
      locationName: "Lokasi tidak boleh kosong",
      lamaMua: "Lama MUA tidak boleh kosong",
    };

    for (const [field, errorMessage] of Object.entries(requiredFields)) {
      if (!body[field]) {
        return res.status(400).json({
          message: errorMessage,
        });
      }
    }

    const approvalData = new approval({
      muaId: u.id,
      name: body.name,
      profileName: body.profileName,
      serviceType: body.serviceType,
      instagram: body.instagram,
      email: body.email,
      phone: body.phone,
      address: body.address,
      location: body.location,
      linkHb: body.linkHb,
      hasTraining: body.hasTraining,
      hasParticipated: body.hasParticipated,
      isCertified: body.isCertified,
      hasJob: body.hasJob,
      hasCollaboration: body.hasCollaboration,
      jumlah_job: body.jumlah_job,
      collaboration: body.collaboration,
      certificates: body.certificates,
      trainings: body.trainings,
      locationid: body.locationid,
      locationName: body.locationName,
      lamaMua: body.lamaMua,
    });

    await approvalData.save();

    return res.status(200).json({
      message: "Persetujuan ditambahkan",
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Kesalahan server internal",
    });
  }
}

const checkApproval = async (req, res) => {
  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);

    let approvalData = await approval.find({
      muaId: u.id,
    }).sort({_id: -1}).limit(1); // Ensure the latest data is retrieved

    approvalData = approvalData[0];
    
    if(!approvalData){
      return res.status(404).json({
        message: "Approval not found",
      });
    }

    return res.status(200).json({
      message: "Approval checked",
      data: {
        status: approvalData.status,
        note: approvalData.note,
      }
    });
  } catch (error) {
    console.log(error);
    
    return res.status(500).json({
      message: "Internal server error",
    });
  }
}

// const detailBid = async
module.exports = {
  addApproval,
  checkApproval,
};
