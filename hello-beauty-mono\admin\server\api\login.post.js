import axios from 'axios';

export default defineEventHandler(async (event) => {
  try {
    const config = useRuntimeConfig();
    const uri = config.public.API_URL||"https://dev-api.hellobeauty.id/chio"
    const body = await readBody(event);
    if (!body.email || !body.password) {
      return {
        status: 0,
        message: 'Email dan Password harus diisi',
      };
    }
    const hitApi = await axios.post(`${uri}/auth/login`, body);
    const d = hitApi.data
    setCookie(event,'_toV0y', d.data.token, {
      maxAge: 60 * 60 * 24 * 30,
      httpOnly: true,
      secure: true,
      sameSite: 'strict',
    });
    setCookie(event,'user', JSON.stringify(d.data.data), {
      maxAge: 60 * 60 * 24 * 30,
      httpOnly: false,
      secure: true,
      sameSite: 'strict',
    });
    setCookie(event, "_logmin", true, {
      maxAge: 60 * 60 * 24 * 30,
    })
    console.log(d);
    
    return d
    
  } catch (e) {
    console.log(e);
    
    return {
      status: 0,
      message: e.response?.data?.message || 'An error occurred during login',
    };
  }
});
