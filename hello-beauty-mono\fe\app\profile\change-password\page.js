"use client"
import { useState } from 'react';
import { api } from '../../_helper/api';
import ErrorSpan from '../../_component/errorSpan';
import { Icon } from '@iconify/react';
import Link from 'next/link';

export default function ChangePassword() {
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      setError('');
      

      if (newPassword.length < 8) {
        throw new Error('Password harus terdiri dari minimal 8 karakter');
      }

      if (newPassword !== confirmNewPassword) {
        throw new Error('Password baru tidak cocok');
      }
      setLoading(true);
      await api('POST', '/me/change-password', { old_password:oldPassword, new_password: newPassword });
      setLoading(false);
      window.location.href = '/profile';
    } catch (error) {
      setError(error.message);
      setLoading(false);
    }
  };

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <Link href="/profile" className="h-14 flex items-center px-4">
        <img src="/icons/arrow-left.svg" alt="Kembali" className="" />
        <img src="/logo.png" alt="Logo" className="mx-auto h-8" />
      </Link>

      <div className="mt-28">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Ubah Password<span className="text-hb-pink">.</span>
        </h3>
      </div>

      <form className="" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 px-4 mt-20">
          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <img src="/icons/fingerprint.svg" alt="Password Lama" className="mr-2" />
            <input
              disabled={loading}
              required
              type={passwordVisible ? "text" : "password"}
              placeholder="Password Lama"
              value={oldPassword}
              onChange={(e) => setOldPassword(e.target.value)}
              className="w-full focus:outline-none"
            />
          </div>
          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <img src="/icons/fingerprint.svg" alt="Password Baru" className="mr-2" />
            <input
              disabled={loading}
              required
              type={passwordVisible ? "text" : "password"}
              placeholder="Password Baru"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="w-full focus:outline-none"
            />
          </div>
          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <img src="/icons/fingerprint.svg" alt="Konfirmasi Password Baru" className="mr-2" />
            <input
              disabled={loading}
              required
              type={passwordVisible ? "text" : "password"}
              placeholder="Konfirmasi Password Baru"
              value={confirmNewPassword}
              onChange={(e) => setConfirmNewPassword(e.target.value)}
              className="w-full focus:outline-none"
            />
            <button
              type="button"
              onClick={() => setPasswordVisible(!passwordVisible)}
              className="ml-2"
            >
              <Icon icon={passwordVisible ? "mdi:eye-off" : "mdi:eye"} />
            </button>
          </div>
        </div>
        <div className="px-4">
          {error && <ErrorSpan msg={error} />}
        </div>
        <div className="px-6">
          <button type="submit" className="btn-primary flex items-center justify-center">
            {loading && <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin mr-2" />}
            Ubah Password
          </button>
        </div>
      </form>

      <div className="h-40"></div>
    </div>
  );
}
