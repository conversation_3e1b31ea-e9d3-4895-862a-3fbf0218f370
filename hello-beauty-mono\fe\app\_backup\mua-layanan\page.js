import Link from 'next/link'

export default function Home() {
  return (
    <div className="max-w-[480px] mx-auto">
      <div className="bg-hb-pink h-[200px]"/>
      <div className="grid-bg min-h-[800px]"/>

      <div className="p-4 -mt-[992px]"  data-aos="fade-up" >
        <img src="/images/mua-1.jpeg" className="mx-auto rounded-xl h-[220px] shadow-2xl w-full object-cover" />
      </div>
      <div className="-mt-24"  data-aos="fade-up" >
        <img src="/images/mua-logo.png" className="mx-auto rounded-full h-[140px] " />
      </div>

      <div className="p-4">
      <div className="p-3 bg-white mb-4 rounded-xl border grid grid-cols-2"  data-aos="fade-up" >
        <Link href="/mua-layanan"  className="border-r pr-2">
          <div className="py-2 text-center bg-hb-pink text-white rounded-lg">
          Layanan
          </div>
        </Link>
        <Link href="/mua-portofolio" className="py-2 text-center">
          Portofolio
        </Link>

      </div>

      <div className="py-4 border-b"  data-aos="fade-up" >
        <h3 className="text-xl font-semibold">
          Basic Make Up
        </h3>
        <p className="text-gray-500">
          Party, Graduation Or Daily Make Up
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 mt-6">
        <div className="flex gap-2 w-full items-center"  data-aos="fade-up" >
          <div className="w-4/12">
            <img src="/images/images-1.png" className="h-28 w-28 object-cover rounded-xl"/>
          </div>
          <div>
            <h3 className="text-lg font-semibold">Makeup + Hairdo Bride Trial</h3>
            <p className="text-gray-500">
              Makeup + Hairdo Bride Trial - Pengerjaan di Studio
            </p>
          </div>

        </div>
         <div className="flex gap-2 w-full items-center"  data-aos="fade-up" >
          <div className="w-4/12">
            <img src="/images/images-2.jpeg" className="h-28 w-28 object-cover rounded-xl"/>
          </div>
          <div>
            <h3 className="text-lg font-semibold">Makeup + Hairdo Bride Trial</h3>
            <p className="text-gray-500">
              Makeup + Hairdo Bride Trial - Pengerjaan di Studio
            </p>
          </div>

        </div>
         <div className="flex gap-2 w-full items-center"  data-aos="fade-up" >
          <div className="w-4/12">
            <img src="/images/images-3.jpeg" className="h-28 w-28 object-cover rounded-xl"/>
          </div>
          <div>
            <h3 className="text-lg font-semibold">Makeup + Hairdo Bride Trial</h3>
            <p className="text-gray-500">
              Makeup + Hairdo Bride Trial - Pengerjaan di Studio
            </p>
          </div>

        </div>
      </div>



        </div>


    <div className="flex fixed bottom-0 left-0 h-[80px] items-center p-3 w-full z-50">
  <Link href="/prices" className="btn-primary">BOOK NOW</Link>
</div>
<div className=" w-full fixed bottom-0 left-0 h-[80px] backdrop-blur opacity-90 z-10"/>
    </div>
  );
}
