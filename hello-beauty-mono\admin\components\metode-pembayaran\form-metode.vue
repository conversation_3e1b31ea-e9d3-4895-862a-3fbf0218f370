<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">
              {{ payload._id ? "Edit" : "Tambah" }} Metode Pembayaran
            </h2>
          </div>
          <div class="grid grid-cols-2 gap-4 !text-sm">
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Nama Metode</label>
              <input
                v-model="payload.name"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>
            <div>
              <label class="text-sm font-semibold block">Vendor</label>
              <input
                v-model="payload.vendor"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>
            <div>
              <label class="text-sm font-semibold block">Kode Metode</label>
              <input
                v-model="payload.code"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Logo</label>
              <input
                v-model="payload.logo"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Deskripsi</label>
              <textarea
                v-model="payload.description"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div class="col-span-2">
              <label class="text-sm font-semibold block">Status</label>
              <select
                v-model="payload.isActive"
                required
                :disabled="loading"
                class="form-input text-sm"
              >
                <option value="" disabled selected>Pilih Status</option>
                <option :value="true">Aktif</option>
                <option :value="false">Non Aktif</option>
              </select>
            </div>
            <div>
              <label class="text-sm font-semibold block"
                >Biaya Admin Persen</label
              >
              <input
                v-model="payload.adminPercentage"
                type="number"
                step="0.01"
                required
                :disabled="loading"
                class="form-input text-sm"
              />
            </div>
            <div>
              <label class="text-sm font-semibold block"
                >Biaya Admin Flat</label
              >
              <input
                v-model="payload.adminFlat"
                required
                type="number"
                :disabled="loading"
                class="form-input text-sm"
              />
            </div>
          </div>

          <div class="h-4" />
          <div class="flex gap-2">
            <button
              class="btn-primary text-sm flex items-center gap-1"
              :disabled="loading"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              class="btn-secondary text-sm flex items-center gap-1"
              :disabled="loading"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);

const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);

const payload = ref({
  name: "",
  isActive: "",
  code: "",
  vendor: "",
  description: "",
  adminPercentage: "",
  adminFlat: "",
  logo: "",
});
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    if (props.data) {
      payload.value = { ...props.data };
    }
  },
  { deep: true }
);

const save = async () => {
  try {
    loading.value = true;
    const p = {
      name: payload.value.name,
      isActive: payload.value.isActive,
      code: payload.value.code,
      vendor: payload.value.vendor,
      description: payload.value.description,
      adminPercentage: payload.value.adminPercentage,
      adminFlat: payload.value.adminFlat,
      logo: payload.value.logo,
    };

    let res;
    if (props.data._id) {
      res = await adminPut(`/payment?id=${props.data._id}`, p);
    } else {
      res = await adminPost("/payment", p);
    }
    loading.value = false;

    $toast.success(res.data.message);
    emit("refresh");
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
