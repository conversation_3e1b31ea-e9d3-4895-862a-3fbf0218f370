require('dotenv').config();
const Mailjet = require('node-mailjet');

const mailjet = new Mailjet({
    apiKey: process.env.MAILJET_API_KEY,
    apiSecret: process.env.MAILJET_API_SECRET
});

async function sendEmail(to, subject, text) {
    console.log("Sending email...");
    
    // Validate email format
    if (!to || typeof to !== 'string' || !to.includes('@')) {
        throw new Error('Invalid email address');
    }

    // Extract name from email (everything before @)
    const name = to.split('@')[0] || 'User';
    
    const request = mailjet
        .post("send", {'version': 'v3.1'})
        .request({
            Messages: [
                {
                    From: {
                        Email: "<EMAIL>",
                        Name: "Hello Beauty"
                    },
                    To: [
                        {
                            Email: to,
                            Name: name
                        }
                    ],
                    Subject: subject,
                    TextPart: text,
                    HTMLPart: `<p>${text}</p>`
                }
            ]
        });

    try {
        const result = await request;
        console.log('Message sent successfully');
        return result.body;
    } catch (err) {
        console.error('Error sending email:', err);
        throw err;
    }
}

module.exports = sendEmail;
