<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-bold">
              Apakah Anda Yakin Sukseskan Pembayaran ?
            </h2>
          </div>
          <div class="mb-3">
            <label class="text-xs text-gray-600">ID Transaksi</label>
            <p class="text-sm font-semibold">#{{ data.trxId }}</p>
          </div>
          <div class="mb-3">
            <label class="text-xs text-gray-600">Nominal</label>
            <p class="text-sm font-semibold">
              {{ useRupiah(data.totalBayar) }}
            </p>
          </div>
          <div class="mb-3">
            <label class="text-xs text-gray-600"><PERSON><PERSON></label>
            <p class="text-sm font-semibold">
              {{ data.isDp ? "DP" : "Pelunasan" }}
            </p>
          </div>

          <div>
            <label class="text-xs text-gray-600">Bukti Bayar</label>
            <input
              id="bukti"
              type="file"
              class="form-input"
              required
              accept="image/*,application/pdf"
            />
          </div>

          <div class="h-4" />
          <div v-if="errorMsg" class="mb-3 p-2 rounded-lg bg-red-100">
            <p class="text-red-500 text-xs">{{ errorMsg }}</p>
          </div>
          <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Mohon Tunggu" : "Ya, Sukseskan" }}
              </span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);
const route = useRoute();
const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);

const payload = ref({
  trxId: route.params.id,
  isOpen: false,
});
const errorMsg = ref("");
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    if (props.data) {
      payload.value = {
        trxId: props.data.trxId,
        isOpen: props.data.isOpen,
      };
    }
  },
  { deep: true }
);

const save = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    const file = document.getElementById("bukti").files[0];
    const formData = new FormData();
    formData.append("file", file);

    const res = await adminFile(`/pay/confirm/${props.data.payId}`, formData);

    loading.value = false;

    $toast.success(res.data.message);
    window.location.reload();
  } catch (error) {
    errorMsg.value = error.response.data.message;
    $toast.error(error.response.data.message);
  } finally {
    loading.value = false;
  }
};
</script>
