const { getRandomDelay } = require('../paymentBot');

describe('getRandomDelay', () => {
  it('should generate a random delay between 30 and 60 seconds', () => {
    const delays = new Set();
    
    // Generate 1000 random delays
    for (let i = 0; i < 1000; i++) {
      const delay = getRandomDelay();
      delays.add(delay);
      
      // Check if delay is within the expected range
      expect(delay).toBeGreaterThanOrEqual(30000); // 30 seconds
      expect(delay).toBeLessThanOrEqual(60000);    // 60 seconds
    }
    
    // Check if we have a good distribution of delays
    expect(delays.size).toBeGreaterThan(500); // At least 500 unique values
  });

  it('should return an integer', () => {
    const delay = getRandomDelay();
    expect(Number.isInteger(delay)).toBe(true);
  });
}); 
