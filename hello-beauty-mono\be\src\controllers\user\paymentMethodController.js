const payment = require('../../models/payment-method')
const { logFunction, logError, logSuccess } = require('../../utils/logger')

const listPaymentMethod = async (req, res) => {
  const fnName = 'listPaymentMethod';
  logFunction(fnName);

  try {
    const packages = await payment.find()
    
    logSuccess(fnName, { 
      totalMethods: packages.length
    });
    
    return res.status(200).json({
      message: "List metode pembayaran",
      data: packages
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

module.exports = {
  listPaymentMethod
}
