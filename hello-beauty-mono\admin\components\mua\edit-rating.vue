<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        hide-close-btn
        lock-scroll
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">Ubah Rating MUA</h2>
          </div>

          <div class="mt-4">
            <label>Rating</label>
            <input
              required
              type="number"
              min="0"
              max="100"
              v-model="payload.rating"
              class="form-input"
            />
          </div>

          <div class="h-4" />
          <div class="flex gap-2">
            <button
              :disabled="loading"
              class="btn-primary text-sm flex items-center gap-1"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              :disabled="loading"
              class="btn-secondary text-sm flex items-center gap-1"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const emit = defineEmits(["closed", "refresh"]);
const route = useRoute();
const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);

const payload = ref({
  muaId: route.params.id,
  rating: "",
});
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
    if (props.data.rating) {
      payload.value.rating = props.data.rating;
    }
  },
  { deep: true }
);

const save = async () => {
  try {
    loading.value = true;
    const p = {
      ...payload.value,
    };

    const res = await adminPost(`/mua/rating`, p);
    loading.value = false;

    $toast.success(res.data.message);
    emit("refresh");
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
