"use client";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { api } from "./_helper/api";
import Gallery from "./_component/home/<USER>";
import Testimonials from "./_component/home/<USER>";
import Articles from "./_component/home/<USER>";
import bookingSteps from "./data/booking-steps.json";
// import GallerySc from "./_component/home/<USER>";

export default function Home() {
  const [listLocation, setListLocation] = useState([]);
  const [loadingLocation, setLoadingLocation] = useState(false);
  const [isLogin, setIsLogin] = useState(false);
  const [loadingMe, setLoadingMe] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY || window.pageYOffset);
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const getLocation = async () => {
    try {
      setLoadingLocation(true);
      const res = await api("GET", "/location");
      setLoadingLocation(false);
      setListLocation(res.data);
    } catch (error) {
      setLoadingLocation(false);
    }
  };

  const checkLogin = async () => {
    try {
      setLoadingMe(true);
      const res = await api("GET", "/me");
      setLoadingMe(false);
      setIsLogin(true);
      localStorage.setItem("data", JSON.stringify(res.data));
    } catch (error) {
      setLoadingMe(false);
      setIsLogin(false);
    }
  };
  useEffect(() => {
    getLocation();
    checkLogin();
  }, []);
  return (
    <div className="max-w-[480px] mx-auto !overflow-x-hidden overflow-clip">
      <div className="z-[999] absolute top-0 left-0 w-full">
        <div className="h-16 flex items-center px-4 justify-between bg-white max-w-[480px] mx-auto">
          <Link href="/">
            <img src="/logo.png" alt="Logo" className="h-8" />
          </Link>

          {!loadingMe &&
            (isLogin ? (
              <Link
                href="/profile"
                className="bg-hb-pink-light-2 p-2 rounded-full"
              >
                <Icon
                  icon="icon-park-twotone:people"
                  className="text-xl text-hb-pink"
                />
              </Link>
            ) : (
              <Link href="/login" className="ml-auto text-hb-pink underline font-semibold">
                SIGN IN
              </Link>
            ))}
        </div>
      </div>

      <div>
        <video
          className="w-full object-cover h-[calc(100vh-20px)]"
          preload="auto"
          src="/video/landing-2.mp4"
          poster="/images/images-1.png"
          autoPlay
          playsInline
          loop
          muted
          loading="lazy"
        >
          <source src="/video/landing-2.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div className="h-[calc(100vh-20px)] absolute top-0 left-0 w-full">
          <div className="max-w-[480px] h-full mx-auto bg-black opacity-30 z-[99]" />
        </div>

        <div className="h-[calc(100vh-20px)] absolute top-0 left-0 w-full rel">
          <div className="max-w-[480px] h-full mx-auto  z-[99] relative flex items-center justify-center">
            <div>
              <div className="mb-8">
                <img src="/logo-white.png" className="h-16" />
              </div>
              <div className="text-center">
                <Link
                  href="/booking"
                  className="btn-primary tracking-wider px-12 mx-auto py-4"
                >
                  BOOKING MAKEUP
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* <div className="text-[140px] w-full absolute overflow-clip z-[99999] -mt-36 font-extrabold text-white">
          hellobeauty
        </div> */}

        <div className="px-8 py-20">
          <h2 className="text-2xl mb-6 text-hb-pink" data-aos="fade-up">
            Ingin Tampil Cantik
            <br /><strong>di Moment Spesial Kamu ?</strong>
          </h2>
          <p
            data-aos="fade-up"
            className=" text-sm leading-6 text-gray-700 "
          >
            Booking Makeup dengan HelloBeauty memiliki lebih dari 3000+ MUA yang
            tersebar di seluruh Indonesia sehingga bisa melayani kebutuhan
            Makeup kalian kapan saja dan di mana saja tanpa perlu ribet dan
            khawatir.
          </p>
        </div>

         <div className="bg-white py-6 px-8">
          <h2 className="font-semibold text-hb-pink text-3xl" data-aos="fade-left">
            HelloBeauty Hadir
            <br/>
            <strong>
            di Beberapa Kota!
            </strong>
          </h2>
          <div className="py-1 flex  gap-2 text-sm mt-4 flex-wrap">
            {listLocation.map((item, index) => (
              <div
                key={index}
                data-aos="fade-left"
                className="border-r pr-3 border-hb-pink flex justify-between items-center"
              >
                <div className="">
                  {item.name}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="px-4 py-20 bg-hb-pink-light-2 text-center">
          <div className="text-sm">
            <p>
              Kotamu tidak ada di-list?
            </p>
            <p className="font-semibold">
              Tenang, bisa konsultasikan ke kami ya ! ☺️
            </p>
            <div className="h-8">

            </div>

            <a href="https://wa.me/6281299111038" className="btn-primary mt-12">
              KONSULTASI WHATSAPP
            </a>

          </div>
        </div>

         <div className="px-4 py-20 text-center">
          <div className="text-sm">
            <h3 className="text-hb-pink text-2xl font-semibold" data-aos="fade-up">
              We Provide Services For
            </h3>
            <ul className="flex justify-center flex-wrap gap-2 mt-6 font-semibold">
              <li className="border-r-2 pr-2 border-hb-pink" data-aos="fade-up">
                Graduation
              </li>
              <li className="border-r-2 pr-2 border-hb-pink" data-aos="fade-up">
                Wedding
              </li>
              <li className="border-r-2 pr-2 border-hb-pink" data-aos="fade-up">
                Party
              </li>
              <li className="border-r-2 pr-2 border-hb-pink" data-aos="fade-up">
                Birthday
              </li>
              <li className="border-r-2 pr-2 border-hb-pink" data-aos="fade-up">
                Daily
              </li>
              <li className="border-r-2 pr-2 border-hb-pink" data-aos="fade-up">
                Photoshoot
              </li>
              <li className="border-r-2 pr-2 border-hb-pink" data-aos="fade-up">
                Hairdo
              </li>
              <li className="border-r-2 pr-2 border-hb-pink" data-aos="fade-up">
                Hijabdo
              </li>
            </ul>
            <p className="mt-2 font-semibold" data-aos="fade-up">
              etc.
            </p>
          </div>
        </div>

        <div className="px-4 py-12">
          <div className="grid grid-cols-2 gap-1" data-aos="fade-up">
            <img src="/images/mua-3.png" className="w-full rounded-xl object-cover h-[280px]" />
            <img src="/images/mua-4.png" className="w-full rounded-xl object-cover h-[280px]" />
            <img src="/images/beauty.png" className="w-full col-span-2 rounded-xl object-cover h-[480px]" />
            <img src="/images/mua-1.png" className="w-full rounded-xl object-cover h-[280px]" />
            <img src="/images/mua-2.png" className="w-full rounded-xl object-cover h-[280px]" />

          </div>
        </div>

        <div className="p-4">
          <h3 data-aos="fade-up" className="text-3xl leading-10 text-hb-pink">
            Kenapa harus Booking
            <br /> Makeup<span> HelloBeauty</span>
            <br />
            <strong>Home Service? </strong>
          </h3>
        </div>

        <div className="bg-hb-pink-light-2 px-4 py-6">
          <div className="text-center mb-12" data-aos="fade-up">
            <img src="/icon/1.png" className="h-20 mx-auto mb-3" />
            <span className="text-center bg-hb-pink px-1 text-xl text-white">
              Lebih dari 3000+ MakeUp Artist
            </span>
          </div>
          <div className="text-center mb-12" data-aos="fade-up">
            <img src="/icon/2.png" className="h-20 mx-auto mb-3" />
            <span className="text-center bg-hb-pink px-1 text-xl text-white">
              Dipercaya Lebih dari <br/>1.000.000+ Appointment
            </span>
          </div>
          <div className="text-center mb-12" data-aos="fade-up">
            <img src="/icon/3.png" className="h-12 mx-auto mb-3" />
            <span className="text-center bg-hb-pink px-1 text-xl text-white">
              5-Star Reviews From Many Clients
            </span>
          </div>
        </div>
        <div className="px-4 py-6">
          <div className="text-center mb-12 " data-aos="fade-up">
            <img src="/icon/4.png" className="h-20 mx-auto mb-3" />
            <h3 className="text-center text-hb-pink font-semibold">
              Convenience Services & Booking Tanpa Ribet
            </h3>
            <p className="text-xs text-gray-600 px-12">
              Proses booking, pembayaran, sampai atur jadwal semua bisa langsung lewat platform HelloBeauty.
            </p>
          </div>
           <div className="text-center mb-12 " data-aos="fade-up">
            <img src="/icon/5.png" className="h-20 mx-auto mb-3" />
            <h3 className="text-center text-hb-pink font-semibold">
              Free Transport Fee
            </h3>
            <p className="text-xs text-gray-600 px-12">
              Biaya makeup yang tertera sudah termasuk ke dalam fee transport sehingga tidak perlu biaya tambahan.
            </p>
          </div>
           <div className="text-center mb-12 " data-aos="fade-up">
            <img src="/icon/6.png" className="h-20 mx-auto mb-3" />
            <h3 className="text-center text-hb-pink font-semibold">
              Pelayanan Personal
            </h3>
            <p className="text-xs text-gray-600 px-12">
              Dengan memahami kebutuhan klien, HelloBeauty siap memberikan pelayanan yang sesuai preferensi klien.
            </p>
          </div>

          <div className="text-center mb-12 " data-aos="fade-up">
            <img src="/icon/7.png" className="h-20 mx-auto mb-3" />
            <h3 className="text-center text-hb-pink font-semibold">
              Keahlian Professional 
            </h3>
            <p className="text-xs text-gray-600 px-12">
              HelloBeauty memiliki tim MUA yang sangat terampil dan berpengalaman.
            </p>
          </div>

           <div className="text-center mb-12 " data-aos="fade-up">
            <img src="/icon/8.png" className="h-20 mx-auto mb-3" />
            <h3 className="text-center text-hb-pink font-semibold">
              Keragaman Gaya Makeup
            </h3>
            <p className="text-xs text-gray-600 px-12">
             HelloBeauty menyediakan berbagai pilihan gaya makeup, mulai dari makeup natural hingga glamour.
            </p>
          </div>

          <div className="text-center mb-12 " data-aos="fade-up">
            <img src="/icon/9.png" className="h-20 mx-auto mb-3" />
            <h3 className="text-center text-hb-pink font-semibold">
              Konsultasi Makeup
            </h3>
            <p className="text-xs text-gray-600 px-12">
             Tim HelloBeauty dapat membantu klien memilih gaya makeup yang sesuai dengan kebutuhan klien.
            </p>
          </div>
        </div>

        <div className="px-4 py-6" data-aos="fade-up">
          <div className="relative">
            <h3 className="text-4xl font-semibold z-50 pl-6 text-hb-pink">Testimony</h3>
            <div className="bg-hb-pink-light-2 -z-10 h-6 w-40 absolute top-0 left-0"></div>
          </div>

          <div>
          <Testimonials />

          </div>
        </div>

         <div className=" py-6">
          <div className="relative mx-4 mb-4" data-aos="fade-up">
            <h3 className="text-4xl font-semibold z-50 pl-6 text-hb-pink">Our Portfolio</h3>
            <div className="bg-hb-pink-light-2 -z-10 h-6 w-40 absolute top-0 left-0"></div>
          </div>
          <Gallery/>
        </div>

        <div className="px-4 pt-6">
          <div className="relative mb-4" data-aos="fade-up">
            <h3 className="text-4xl font-semibold z-50 pl-6 text-hb-pink">Follow <br/>Our Instagram</h3>
            <div className="bg-hb-pink-light-2 -z-10 h-6 w-40 absolute top-0 left-0"></div>
          </div>
          <iframe data-aos="fade-up"
            src="https://www.instagram.com/hellobeauty.id/embed"
            width="100%"
            height="520"
            frameborder="0"
            scrolling="no"
            allowtransparency="true"
          ></iframe>
        </div>
        <div className="bg-hb-pink-light-2 px-8 mt-12 py-6">
          <h2 className="font-bold text-2xl" data-aos="fade-up">
            How to book
          </h2>
          <ul className="py-4">
            {bookingSteps.steps.map((step) => (
              <li key={step.step} data-aos="fade-up" className="py-4 border-b border-black">
                <span className="bg-hb-pink text-sm text-white px-2">Step {step.step}</span>
                <p dangerouslySetInnerHTML={{ __html: step.description }} />
              </li>
            ))}
          </ul>
        </div>

          <div className="py-6">
          <div className="relative mx-4 mb-4" data-aos="fade-up">
            <h3 className="text-4xl font-semibold z-50 pl-6 text-hb-pink">Highlights</h3>
            <div className="bg-hb-pink-light-2 -z-10 h-6 w-40 absolute top-0 left-0"></div>
            <div className="bottom-0 left-0 h-[6px] w-16 rounded-full bg-hb-pink ml-6"></div>
          </div>
          <div >
          <img src="/highlight.png" className="w-full" />
          </div>
        </div>


        <div className="px-4 py-6">
          <div className="relative mb-4" data-aos="fade-up">
            <h3 className="text-4xl font-semibold z-50 pl-6 text-hb-pink">Articles</h3>
            <div className="bg-hb-pink-light-2 -z-10 h-6 w-40 absolute top-0 left-0"></div>
            <div className="bottom-0 left-0 h-[6px] w-16 rounded-full bg-hb-pink ml-6"></div>

          </div>
          <div className="px-4" data-aos="fade-up">
            <Articles />
          </div>
        </div>

         <div className="px-4 py-6 bg-hb-pink-light-2">
          <div className="relative mb-4" data-aos="fade-up">
            <h3 className="text-4xl font-semibold z-50 pl-6 text-hb-pink">Contact Us</h3>
            <div className="bg-hb-pink-light-2 -z-10 h-6 w-40 absolute top-0 left-0"></div>
            <div className="bottom-0 left-0 h-[6px] w-16 rounded-full bg-hb-pink ml-6"></div>
          </div>

          <div className="px-6" data-aos="fade-up">
            <img src="/logo.png" className="h-10" />
          </div>

          <div data-aos="fade-up" className="flex gap-1 px-4 mb-4 mb-2 mt-4">
            <div>
              <Icon icon="mdi:map-marker" className="text-hb-pink text-2xl" />
            </div>
            <div className="text-sm pt-1">
              <p>Plaza Sunter Terrace Blok A20</p>
              <p>Jl. Danau Sunter Agung, RT.2/RW.15</p>
              <p>Sunter Agung, Jakarta Utara, Jakarta 14350</p>
            </div>
          </div>

          <div data-aos="fade-up" className="flex gap-1 px-4 mb-4">
            <div>
              <Icon icon="mdi:phone" className="text-hb-pink text-2xl" />
            </div>
            <div className="text-sm pt-1">
              <p>(021) 6500 711</p>
            </div>
          </div>

          <div data-aos="fade-up" className="flex gap-1 px-4 mb-4">
            <div>
              <Icon icon="mdi:whatsapp" className="text-hb-pink text-2xl" />
            </div>
            <div className="text-sm pt-1">
              <p>0812 99 11 1038</p>
            </div>
          </div>

          <div data-aos="fade-up" className="flex gap-1 px-4 mb-4">
            <div>
              <Icon icon="mdi:email" className="text-hb-pink text-2xl" />
            </div>
            <div className="text-sm pt-1">
             
              <a href="mailto:<EMAIL>" className="hover:underline"><EMAIL></a>
            </div>
          </div>

        </div>
        <div>
        </div>
       

        

      
        <div
          data-aos="fade-up"
          className="bg-gradient-to-b from-[#EF81BB] via-pinkMiddle to-[#EE5D53] py-4 px-12 flex gap-6 items-center justify-center"
        >
          <Icon icon="mdi:instagram" className="text-white text-3xl" />
          <Icon icon="ic:baseline-tiktok" className="text-white text-3xl" />
          <Icon icon="ic:baseline-facebook" className="text-white text-3xl" />
          <Icon icon="ri:twitter-x-fill" className="text-white text-2xl" />
        </div>
        
      </div>

      {scrollPosition > 640 && (
        <div
          className="w-full bg-white fixed bottom-0 left-0 z-[99999]"
          data-aos="fade-up"
        >
          <div className="max-w-[480px] mx-auto grid grid-cols-2 gap-3 h-[80px] items-center p-3 w-full ">
            <Link href="/booking" className="btn-primary bg-black border-black text-center">
              BOOKING
            </Link>
            <a href="https://wa.me/6281299111038" className="btn-secondary bg-white text-center">KONSULTASI</a>
          </div>
        </div>
      )}

      {/* <div className=" w-full fixed bottom-0 left-0 h-[80px] backdrop-blur opacity-90 z-10" /> */}
    </div>
  );
}
