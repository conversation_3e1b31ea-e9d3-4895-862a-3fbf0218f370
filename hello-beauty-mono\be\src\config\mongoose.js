const dotenv = require('dotenv');
const mongoose = require('mongoose');
dotenv.config();
const mongoURI = process.env.MONGO_URI;
const dbName = process.env.DB_NAME;

const connectDB = async () => {
  try {
    await mongoose.connect(mongoURI, {
      // useNewUrlParser: true,
      // useUnifiedTopology: true,
      serverSelectionTimeoutMS: 10000,
      dbName:dbName
    });
    console.log('MongoDB connected successfully');
  } catch (err) {
    console.error('MongoDB connection failed:', err.message);
    // process.exit(1);
  }
};

module.exports = connectDB;
