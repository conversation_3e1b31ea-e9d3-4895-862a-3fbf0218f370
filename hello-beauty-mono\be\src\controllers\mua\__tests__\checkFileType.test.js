const { checkFileType } = require('../uploadController');

describe('checkFileType', () => {
  const mockFile = (originalname, mimetype) => ({
    originalname,
    mimetype
  });

  it('should accept valid image files', () => {
    const validFiles = [
      mockFile('test.jpg', 'image/jpeg'),
      mockFile('test.png', 'image/png'),
      mockFile('test.gif', 'image/gif'),
      mockFile('test.pdf', 'application/pdf')
    ];

    validFiles.forEach(file => {
      const cb = jest.fn();
      checkFileType(file, cb);
      expect(cb).toHaveBeenCalledWith(null, true);
    });
  });

  it('should reject invalid file types', () => {
    const invalidFiles = [
      mockFile('test.txt', 'text/plain'),
      mockFile('test.exe', 'application/x-msdownload'),
      mockFile('test.js', 'application/javascript')
    ];

    invalidFiles.forEach(file => {
      const cb = jest.fn();
      checkFileType(file, cb);
      expect(cb).toHaveBeenCalledWith('Error: Images only!');
    });
  });

  it('should reject files with valid extension but invalid mimetype', () => {
    const file = mockFile('test.jpg', 'text/plain');
    const cb = jest.fn();
    checkFileType(file, cb);
    expect(cb).toHaveBeenCalledWith('Error: Images only!');
  });

  it('should reject files with valid mimetype but invalid extension', () => {
    const file = mockFile('test.txt', 'image/jpeg');
    const cb = jest.fn();
    checkFileType(file, cb);
    expect(cb).toHaveBeenCalledWith('Error: Images only!');
  });
}); 
