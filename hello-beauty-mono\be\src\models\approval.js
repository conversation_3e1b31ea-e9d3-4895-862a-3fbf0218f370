const mongoose = require('mongoose');
const approvalSchema = new mongoose.Schema({
  muaId: { type: mongoose.Schema.Types.ObjectId, ref: 'mua' },
  name: { type: String, required: true },
  profileName: { type: String },
  serviceType: { type: Array },
  instagram: { type: String },
  email: { type: String, required: true },
  phone: { type: String },
  address: { type: String },
  location: { type: String },
  linkHb: { type: String },
  hasTraining: { type: Boolean, default: false },
  hasParticipated: { type: Boolean, default: false },
  isCertified: { type: Boolean, default: false },
  hasJob: { type: Boolean, default: false },
  hasCollaboration: { type: Boolean, default: false },
  jumlah_job: { type: Number, default: 0 },
  collaboration: { type: String},
  certificates: [{ type: String }],
  trainings: [{ type: String }],
  locationid: { type: String },
  locationName: { type: String },
  status: { type: String, default: 'pending' },
  lamaMua: { type: String },
  note: { type: String },
}, { timestamps: true },);

module.exports = mongoose.model('approval', approvalSchema);
