<template>
  <div>
    <div v-if="!loading">
      <div class="flex justify-between items-center">
        <h1 class="font-bold">Revenue</h1>
        <p class="text-xs text-gray-600">
          {{ useMoment(range[0]).format('DD MMM YY') }} s.d
          {{ useMoment(range[1]).format('DD MMM YY') }}
        </p>
      </div>
      <ClientOnly>
        <DatePicker
          v-model:value="range"
          :disabled-date="notBeforeToday"
          range
          placeholder="Tanggal"
          style="width: 100%"
        />
      </ClientOnly>

      <apexchart
        v-if="!loading && !errorMsg"
        :key="series"
        height="300"
        width="100%"
        :options="options"
        :series="series"
      />
      
    </div>

    <div v-if="loading">
      <div
        class="h-[300px] bg-gray-300 animate animate-pulse w-full rounded-xl"
      />
    </div>
  </div>
</template>


<script setup>
import moment from 'moment';
const range = ref([new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()]);
const notBeforeToday = date => date > new Date(new Date().setHours(0, 0, 0, 0));

const graph = ref([])
const loading = ref(false)

watch(range, () => {
  getGraph()
})

const options = ref({
  plotOptions: {
    bar: {
      dataLabels: {
        position: 'center' // top, center, bottom
      }
    }
  },
  chart: {
    type: 'area',
    toolbar: {
      show: false
    },
    zoom: {
      enabled: false
    }
  },
  stroke: {
    curve: 'smooth',
    width: 1
  },
  animations: {
    enabled: true
  },

  colors: ["#FF5E5E",'#FFF2F1'],
  dataLabels: {
    enabled: true
  },
  yaxis: {
    labels: {
      formatter(value) {
        if (value) {
          // return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
          // format rupiah
          return 'Rp. ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        }
      }
    }
  },
  xaxis: {
    type: 'category',
    tickPlacement: 'on',
    categories: [],
    crosshairs: {
      stroke: {
        dashArray: 0
      },
      dropShadow: {
        show: false
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    },
    labels: {
      style: {
        colors: '#a8a29e'
      }
    }
  }
});

const series = computed(() => {
  return [
    {
      name: "Revenue",
      data: graph.value.map((item) => item.total)
    }
  ];
});
const getGraph = async () => {
  try {
    loading.value = true
    const { data } = await adminGet('/statistics/revenue?start='+moment(range.value[0]).format('YYYY-MM-DD')+'&end='+moment(range.value[1]).format('YYYY-MM-DD'))
    setTimeout(() => {
      loading.value = false
    }, 1000)
    graph.value = data
    
    if(data) {
      options.value.xaxis.categories = data.map((item) => useMoment(item.date).format('DD MMM YY'))
      
    }
  } catch (error) {
    console.log(error);
    
  }
}

onMounted(() => {
  getGraph()
})
</script>
