const {  decode } = require('../../helper/jwt');
const admin = require('../../models/admin');
const bcrypt = require('bcrypt');
const { logFunction, logError, logSuccess } = require('../../utils/logger');
// const {encode} = require('../../helper/jwt');

const listAdmin = async (req, res) => {
    const fnName = 'listAdmin';
    logFunction(fnName, { 
        page: req.query.page || 1,
        limit: req.query.limit || 10
    });

    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;
      let admins = await admin.find().skip(skip).limit(limit);
      admins = admins.map((admin)=>{
        return {
          _id:admin._id,
          name:admin.name,
          email:admin.email,
          phone:admin.phone,
          level:admin.level,
          menu:admin.menu,
          createdAt:admin.createdAt,
        }
      })

      logSuccess(fnName, { 
        totalAdmins: admins.length,
        page,
        limit
      });

      return res.status(200).json({ message: 'List admin', data: admins });
    } catch (error) {
        logError(fnName, error);
        return res.status(500).json({ message: 'Internal server error' });
    }
}

const createAdmin = async (req, res) => {
    const fnName = 'createAdmin';
    logFunction(fnName, { 
        email: req.body.email,
        name: req.body.name
    });

    try {
        const body = req.body;
        const searchAdmin = await admin.findOne({ email: body.email });
        if(searchAdmin) {
            logFunction(fnName, { error: 'Email already used', email: body.email }, 'warn');
            return res.status(400).json({ message: 'Email sudah digunakan' });
        }
        const encPassword = await bcrypt.hashSync(body.password, 10);

        const newAdmin = await new admin({
            name: body.name,
            email: body.email,
            password: encPassword,
            phone: body.phone,
            level: body.level,
            menu: body.menu
        });

        await newAdmin.save();
        delete newAdmin.password;

        logSuccess(fnName, { 
            adminId: newAdmin._id,
            email: newAdmin.email,
            level: newAdmin.level
        });

        return res.status(201).json({ message: 'Berhasil menambahkan admin' });

    } catch (error) {
        logError(fnName, error);
        res.status(500).json({ message: error.message });
    }
}

const updateAdmin = async (req, res) => {
    const fnName = 'updateAdmin';
    logFunction(fnName, { 
        adminId: req.query.id,
        updates: {
            name: req.body.name,
            email: req.body.email,
            level: req.body.level
        }
    });

    try {
        const body = req.body;
        const id = req.query.id;
        if(!id) {
            logFunction(fnName, { error: 'ID not provided' }, 'warn');
            return res.status(400).json({ message: 'ID tidak ditemukan' });
        }
        const updateAdmin = await admin.findByIdAndUpdate(id, {
            name: body.name,
            email: body.email,
            phone: body.phone,
            level: body.level,
            menu: body.menu
        });
        await updateAdmin.save();

        logSuccess(fnName, { 
            adminId: id,
            updates: {
                name: body.name,
                email: body.email,
                level: body.level
            }
        });

        return res.status(201).json({ message: 'Berhasil mengubah admin' });

    } catch (error) {
        logError(fnName, error);
        res.status(500).json({ message: error.message });
    }
}

const deleteAdmin = async (req, res) => {
    const fnName = 'deleteAdmin';
    logFunction(fnName, { adminId: req.params.id });

    try {
        const id = req.params.id;
        if(!id) {
            logFunction(fnName, { error: 'ID not provided' }, 'warn');
            return res.status(400).json({ message: 'ID tidak ditemukan' });
        }
        await admin.findByIdAndDelete(id);

        logSuccess(fnName, { adminId: id });
        return res.status(200).json({ message: 'Berhasil menghapus admin' });

    } catch (error) {
        logError(fnName, error);
        res.status(500).json({ message: error.message });
    }
}

const detailAdmin = async (req, res) => {
    const fnName = 'detailAdmin';
    logFunction(fnName, { 
        userId: req.headers.authorization ? 'authenticated' : 'anonymous'
    });

    try {
          const auth = req.headers.authorization;
          const u = await decode(auth);
          const id = u.id
        if(!id) {
            logFunction(fnName, { error: 'ID not provided' }, 'warn');
            return res.status(400).json({ message: 'ID tidak ditemukan' });
        }
        const detailAdmin = await admin.findById(id);
        delete detailAdmin.password;

        logSuccess(fnName, { 
            adminId: id,
            email: detailAdmin.email,
            level: detailAdmin.level
        });

        return res.status(200).json({ message: 'Detail admin', data: detailAdmin });
    } catch (error) {
        logError(fnName, error);
        res.status(500).json({ message: error.message });
    }
}

const changePasswordAdmin = async (req, res) => {
    const fnName = 'changePasswordAdmin';
    logFunction(fnName, { 
        userId: req.headers.authorization ? 'authenticated' : 'anonymous'
    });

    try {
        const body = req.body;
        const auth = req.headers.authorization;
        const u = await decode(auth);
        const id = u.id;
        if(!id) {
            logFunction(fnName, { error: 'ID not provided' }, 'warn');
            return res.status(400).json({ message: 'ID tidak ditemukan' });
        }

        // search admin by id
        const searchAdmin = await admin.findById(id);
        if(!searchAdmin) {
            logFunction(fnName, { error: 'Admin not found', adminId: id }, 'warn');
            return res.status(400).json({ message: 'Admin tidak ditemukan' });
        }

        // compare password
        const compare = await bcrypt.compare(body.old_password, searchAdmin.password);

        if(!compare) {
            logFunction(fnName, { error: 'Invalid old password', adminId: id }, 'warn');
            return res.status(400).json({ message: 'Password lama tidak sesuai' });
        }

        // encrypt new password
        const encPassword = await bcrypt.hashSync(body.new_password, 10);

        // validasi password must be 8 char, 1 number, 1 uppercase, 1 lowercase
        const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
        if(!regex.test(body.new_password)) {
            logFunction(fnName, { error: 'Invalid password format', adminId: id }, 'warn');
            return res.status(400).json({ message: 'Password harus terdiri dari 8 karakter, 1 huruf besar, 1 huruf kecil, dan 1 angka' });
        }
        // update password
        const updatePassword = await admin.findByIdAndUpdate(id, { password: encPassword });
        await updatePassword.save();

        logSuccess(fnName, { adminId: id });
        return res.status(201).json({ message: 'Berhasil mengubah password' });

    } catch (error) {
        logError(fnName, error);
        res.status(500).json({ message: error.message });
    }
}

const resetPasswordAdmin = async (req, res) => {
    const fnName = 'resetPasswordAdmin';
    logFunction(fnName, { 
        adminId: req.query.id,
        resetBy: req.headers.authorization ? 'authenticated' : 'anonymous'
    });

    try {
        const body = req.body;
        const id = req.query.id;
        if(!id) {
            logFunction(fnName, { error: 'ID not provided' }, 'warn');
            return res.status(400).json({ message: 'ID tidak ditemukan' });
        }

        // search admin by id
        const searchAdmin = await admin.findById(id);
        if(!searchAdmin) {
            logFunction(fnName, { error: 'Admin not found', adminId: id }, 'warn');
            return res.status(400).json({ message: 'Admin tidak ditemukan' });
        }

        // hanya superadmin yang bisa reset password
        if(searchAdmin.level !== 'superadmin') {
            logFunction(fnName, { error: 'Not authorized', adminId: id, level: searchAdmin.level }, 'warn');
            return res.status(400).json({ message: 'Hanya superadmin yang bisa mereset password' });
        }

        // check password must be 8 char, 1 number, 1 uppercase, 1 lowercase
        const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
        if(!regex.test(body.password)) {
            logFunction(fnName, { error: 'Invalid password format', adminId: id }, 'warn');
            return res.status(400).json({ message: 'Password harus terdiri dari 8 karakter, 1 huruf besar, 1 huruf kecil, dan 1 angka' });
        }

        // encrypt new password
        const encPassword = await bcrypt.hashSync(body.password, 10);

        // update password
        const updatePassword = await admin.findByIdAndUpdate(id, { password: encPassword });
        await updatePassword.save();

        logSuccess(fnName, { adminId: id });
        return res.status(201).json({ message: 'Berhasil mereset password' });

    } catch (error) {
        logError(fnName, error);
        res.status(500).json({ message: error.message });
    }
}

module.exports = {
    listAdmin,
    createAdmin,
    updateAdmin,
    deleteAdmin,
    detailAdmin,
    changePasswordAdmin,
    resetPasswordAdmin
}
