<template>
  <div>
    <span
      :class="colorStatus"
      class="text-[10px] font-semibold uppercase px-2 py-1 rounded-full"
    >
      {{ props.status }}
    </span>
  </div>
</template>

<script setup>
const props = defineProps({
  status: {
    type: String,
    required: true
  }
});

const colorStatus = computed(() => {
  if (props.status === 'dikirim') {
    return 'text-orange-600 bg-orange-100';
  }

  if (props.status === 'diproses') {
    return 'text-yellow-600 bg-yellow-100';
  }

  if (props.status === 'dibatalkan') {
    return 'text-red-600 bg-red-100';
  }

  if (props.status === 'diterima') {
    return 'text-blue-600 bg-blue-100';
  }

  return 'text-gray-600';
});
</script>
