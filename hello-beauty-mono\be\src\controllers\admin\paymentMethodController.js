const payment = require("../../models/payment-method");

const listPayment = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const payments = await payment.find().skip(skip).limit(limit);
    return res.status(200).json({
      message: "List metode pembayaran",
      data: payments
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const createPayment = async (req, res) => {
  try {
    const body = req.body;
    const newPayment = await new payment({
      name: body.name,
      code: body.code,
      vendor: body.vendor,
      isActive: body.isActive,
      description: body.description,
      adminPercentage: body.adminPercentage,
      adminFlat: body.adminFlat,
      logo: body.logo,
    });
    await newPayment.save();
    return res.status(201).json({ message: 'Berhasil menambahkan metode pembayaran'})
    
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

const updatePayment = async (req, res) => {
  try {
    const body = req.body;
    const id = req.query.id;
    const updatePayment = await payment.findByIdAndUpdate({"_id":id}, {
      name: body.name,
      code: body.code,
      vendor: body.vendor,
      isActive: body.isActive,
      description: body.description,
      adminPercentage: body.adminPercentage,
      adminFlat: body.adminFlat,
      logo: body.logo,
    },{new: true});
    return res.status(201).json({ message: 'Berhasil mengubah metode pembayaran'})
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

const deletePayment = async (req, res) => {
  try {
    const id = req.query.id;
    await payment.findByIdAndDelete(id);
    return res.status(200).json({ message: 'Berhasil menghapus metode pembayaran'})
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
}

module.exports = {
  listPayment,
  createPayment,
  updatePayment,
  deletePayment
}
