<template>
  <div>
    <NuxtLayout :name="layout">
      <div class="min-h-screen w-full grid grid-cols-1 md:grid-cols-5">
        <div class="col-span-2 flex items-center p-4 md:px-32">
          <form class="w-full" @submit.prevent="loginAct">
            <img src="/logo.png" class="mx-auto" />
            <div class="mb-2">
              <label class="text-label">Email</label>
              <input
                v-model.trim="user.email"
                required
                type="email"
                placeholder="Contoh: <EMAIL>"
                :disabled="loading"
                class="form-input"
              />
            </div>
            <div class="mb-2">
              <label class="text-label">Password</label>
              <input
                v-model.trim="user.password"
                required
                type="password"
                placeholder="Password Anda"
                :disabled="loading"
                class="form-input"
              />
            </div>

            <div
              v-if="errorMsg"
              class="text-xs p-2 bg-red-100 text-red-600 rounded-md my-2"
            >
              {{ errorMsg }}
            </div>

            <!-- <label
              class="text-label flex text-gray-600 items-center gap-1 mt-2 mb-6"
            >
              <input
                type="checkbox"
                class="form-checkbox"
                checked
                :disabled="loading"
              />
              I agree to
              <button
                type="button"
                class="text-primary"
                @click.prevent="isOpened = true"
              >
                Terms & Privacy Policy
              </button>
            </label> -->
            <button
              type="submit"
              class="btn-primary w-full flex items-center justify-center gap-4 transition-all duration-300 ease-in-out"
              :disabled="loading"
            >
              <icon v-if="loading" name="eos-icons:loading" />
              Login
            </button>
          </form>
        </div>
        <div
          class="bg-primary hidden col-span-3 min-h-screen md:flex items-center justify-center"
        >
          <img src="/login.png" class="h-screen w-full object-cover" />
        </div>
      </div>
    </NuxtLayout>
  </div>
</template>

<script setup>
import axios from "axios";
// import { useReCaptcha } from 'vue-recaptcha-v3';

definePageMeta({
  layout: "no-auth",
});

useHead({
  title: "Login Admin",
});

// const recaptchaInstance = useReCaptcha();

const layout = "no-auth";
// const router = useRouter();
const user = ref({
  email: "",
  password: "",
});

const errorMsg = ref("");

const loading = ref(false);
// const isOpened = ref(false);

const loginAct = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";

    const res = await axios.post("/api/login", {
      ...user.value,
    });

    console.log();

    if (res.data && res.data.data.token) {
      loading.value = false;
      window.location = "/";
    } else {
      loading.value = false;
      errorMsg.value = res.data.message;
    }
  } catch (e) {
    loading.value = false;
    errorMsg.value = e;
  }
};
</script>
