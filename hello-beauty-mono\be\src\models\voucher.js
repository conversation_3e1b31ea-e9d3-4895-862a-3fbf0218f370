const mongoose = require('mongoose');

const voucherSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  name: {
    type: String,
    required: true
  },
  description: String,
  discountType: {
    type: String,
    enum: ['percentage', 'fixed'],
    required: true
  },
  discountValue: {
    type: Number,
    required: true,
    min: 0
  },
  minPurchase: {
    type: Number,
    default: 0
  },
  maxDiscount: {
    type: Number,
    default: null
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  usageLimit: {
    type: Number,
    default: null
  },
  usedCount: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Add index for code and date range
voucherSchema.index({ code: 1 });
voucherSchema.index({ startDate: 1, endDate: 1 });

// Pre-save middleware to update updatedAt
voucherSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Method to check if voucher is valid
voucherSchema.methods.isValid = function() {
  const now = new Date();
  return (
    this.isActive &&
    now >= this.startDate &&
    now <= this.endDate &&
    (this.usageLimit === null || this.usedCount < this.usageLimit)
  );
};

// Method to calculate discount
voucherSchema.methods.calculateDiscount = function(totalAmount) {
  if (!this.isValid() || totalAmount < this.minPurchase) {
    return 0;
  }

  let discount = 0;
  if (this.discountType === 'percentage') {
    discount = (totalAmount * this.discountValue) / 100;
    if (this.maxDiscount && discount > this.maxDiscount) {
      discount = this.maxDiscount;
    }
  } else {
    discount = this.discountValue;
  }

  return discount;
};

const Voucher = mongoose.model('Voucher', voucherSchema);

module.exports = Voucher; 
