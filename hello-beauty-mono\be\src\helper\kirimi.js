const axios = require('axios');

const sendWhatsapp = async (phone, message) => {
  try {
    const send = await axios.post(
      'https://api.kirimi.id/v1/send-message',
      {
        user_code: 'KMGF9R1124',
        device_id: 'D-0TVV3',
        receiver: phone,
        message,
        secret: '5ad00a5d6ca7d438e7582e714b2c61e7a37556c7e7d5ea5ec8c9261c0f7269f3',
      },
      {
        timeout: 1200000,
      },
    );
    return send;
  } catch (error) {
    return error;
  }
};

module.exports = {
  sendWhatsapp,
};
