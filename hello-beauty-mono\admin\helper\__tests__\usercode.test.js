import generateUserCode from '../usercode';

describe('generateUserCode', () => {
  it('should generate a valid user code', () => {
    const userCode = generateUserCode();
    
    // Check if the code has the correct format: YY + 4 chars + 3 numbers
    expect(userCode).toMatch(/^[0-9]{2}[A-Za-z0-9]{4}[0-9]{3}$/);
    
    // Check if the first two digits are the current year
    const currentYear = new Date().getFullYear().toString().substring(2, 4);
    expect(userCode.substring(0, 2)).toBe(currentYear);
    
    // Check if the last three characters are numbers
    const lastThree = userCode.substring(6);
    expect(Number(lastThree)).toBeGreaterThanOrEqual(100);
    expect(Number(lastThree)).toBeLessThanOrEqual(999);
  });

  it('should generate unique codes', () => {
    const codes = new Set();
    const iterations = 1000;
    
    for (let i = 0; i < iterations; i++) {
      codes.add(generateUserCode());
    }
    
    // Check if all generated codes are unique
    expect(codes.size).toBe(iterations);
  });
}); 
