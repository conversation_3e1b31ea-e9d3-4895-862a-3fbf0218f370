"use client";
import { api } from "@/app/_helper/api";
import Link from "next/link";
import { useEffect, useState, useRef, useCallback } from "react";
import moment from "../../_helper/moment";
import { Icon } from "@iconify/react";
import StatusDisplay from "../../_component/StatusDisplay";

export default function BookingHistory() {
  const [bookingHistory, setBookingHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [searchInput, setSearchInput] = useState("");
  const observer = useRef();

  const getBookingHistory = async (pageNum = 1, search = "") => {
    try {
      setLoading(true);
      const res = await api("GET", `/order?page=${pageNum}&limit=10${search ? `&search=${search}` : ""}`);
      setLoading(false);
      
      if (pageNum === 1) {
        setBookingHistory(res.data);
      } else {
        setBookingHistory(prev => [...prev, ...res.data]);
      }
      
      setHasMore(res.pagination.hasMore);
    } catch (error) {
      setLoading(false);
      setError(error);
    }
  };

  const lastBookingElementRef = useCallback(node => {
    if (loading) return;
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        setPage(prevPage => prevPage + 1);
      }
    });
    if (node) observer.current.observe(node);
  }, [loading, hasMore]);

  useEffect(() => {
    setPage(1);
    getBookingHistory(1, searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    if (page > 1) {
      getBookingHistory(page, searchQuery);
    }
  }, [page]);

  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4 justify-between max-w-[480px]">
          <Link href="/profile">
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
        </div>
        <div>
          <h3 className="text-2xl font-semibold text-center mb-6">
            Booking
            <br /> History<span className="text-hb-pink">.</span>
          </h3>
          <div className="mt-4 p-3">
            <div className="mb-4 relative">
              <input
                type="text"
                placeholder="Search by ID or package name..."
                className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-hb-pink pr-10"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    setSearchQuery(searchInput);
                    setPage(1);
                  }
                }}
              />
              {searchInput && (
                <button
                  onClick={() => {
                    setSearchInput("");
                    setSearchQuery("");
                    setPage(1);
                  }}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <Icon icon="mdi:close" className="text-xl" />
                </button>
              )}
            </div>
            {!loading && bookingHistory.length === 0 && (
              <div className="py-20 text-center">
                <Icon
                  icon="hugeicons:cursor-circle-selection-02"
                  className="text-hb-pink mx-auto text-5xl"
                />
                <h4 className="text-hb-pink text-lg mt-4">
                  No booking history
                </h4>
                <div className="px-20 mt-4">
                  <Link href="/booking">
                    <button className="btn-secondary">Book Now</button>
                  </Link>
                </div>
              </div>
            )}
            <ul>
              {bookingHistory.map((item, index) => (
                <li
                  key={index}
                  ref={index === bookingHistory.length - 1 ? lastBookingElementRef : null}
                  className="bg-white mb-2 p-3 border rounded-xl"
                >
                  <Link
                    href={`/booking/${item.trxId}`}
                    className="flex items-center justify-between"
                  >
                    <div>
                      <h4 className="text-[10px] text-gray-500 font-semibold">#{item.trxId}</h4>
                      <div className="text-gray-600 text-sm font-semibold">
                        {item.packageItemName} {item.packageName}
                      </div>
                      <StatusDisplay status={item.status} />
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-400">
                        {moment(item.createdAt).format("DD MMM YYYY")}
                      </p>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
            {loading && (
              <div className="py-4 text-center">
                <Icon
                  icon="mingcute:loading-fill"
                  className="text-4xl text-hb-pink mx-auto animate-spin"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
