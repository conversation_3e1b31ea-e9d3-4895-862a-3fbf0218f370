<template>
  <div>
    <span
      :class="colorStatus"
      class="text-[10px] font-semibold px-2 py-1 rounded-full"
    >
      {{ props.status }}
    </span>
  </div>
</template>

<script setup>
const props = defineProps({
  status: {
    type: String,
    required: true
  }
});

const colorStatus = computed(() => {
  if(props.status === 'approved') {
    return 'text-green-600 bg-green-100';
  }
  if(props.status === 'rejected') {
    return 'text-red-600 bg-red-100';
  }
  if(props.status === 'pending') {
    return 'text-yellow-600 bg-yellow-100';
  }

  return 'text-gray-600 bg-gray-100';
});
</script>
