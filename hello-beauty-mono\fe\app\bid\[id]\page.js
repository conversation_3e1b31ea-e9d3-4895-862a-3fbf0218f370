"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import moment from "../../_helper/moment";
import LoadingFull from "@/app/_component/loadingFull";
import AjukanBid from "@/app/_component/mua/bid/ajukanBid";
import { apiMua, getAuth } from "@/app/_helper/api-mua";
import convertRp from "@/app/_helper/convertorp";

export default function DetailBid() {
  const [booking, setBooking] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [login, setLogin] = useState(false);
  const params = useParams();

  const [showBid, setShowBid] = useState(false);

  const getBooking = async () => {
    try {
      setLoading(true);
      const res = await apiMua("GET", `/order/${params.id}`);
      setLoading(false);
      setBooking(res.data);
    } catch (error) {
      console.log(error);
      setLoading(false);
      setError(error);
    }
  };

   const getMe = async () => {
    try {
      const response = await apiMua("GET", "/me");
      const me = response.data
      if(!me.isApproved) {
        window.location = "/mua/verify"
      }
    } catch (error) {
      // window.location = "/mua/verify"
    }
  }
  const checkBid = async () => {
    try {
      // setLoading(true);
      const res = await apiMua("GET", `/bid/check/${params.id}`);
      // setLoading(false);
      setShowBid(false);
    } catch (error) {
      console.log(error);
      // setLoading(false);
      setShowBid(true);
    }
  };

  useEffect(() => {
    getMe()
    getBooking();
    // const token = getAuth();
    checkBid();

    // if(token) {
    //   setLogin(true);
    // } else {
    //   setLogin(false);
    //   setShowBid(true);
    // }

    document.title = "Bid Detail #" + params.id;
  }, []);
  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4 justify-between  max-w-[480px]">
          <Link href={`/mua/bid`}>
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
        </div>
        <div>
          <h3 className="text-2xl font-semibold text-center mb-6">
            Bid
            <br /> Detail<span className="text-hb-pink">.</span>
          </h3>
          {loading && <LoadingFull />}
          <div className="mt-4 p-3">
            {!loading && (
              <div className="px-3">
                <div className="mb-3">
                  <span className="text-xs">Booking ID</span>
                  <h4 className="text-sm font-semibold">#{booking.trxId}</h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Budget</span>
                  <h4 className="text-sm font-semibold">{convertRp(booking.muaShare||0)}</h4>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Paket</span>
                  <div>
                    <div className="flex  py-1 items-center px-2 text-xs rounded-lg gap-1 text-hb-pink">
                      <img src="/icons/coin.svg" className="h-3" />
                      <span>
                      {booking.packageItemName}
                      </span>
                    </div>
                    
                  </div>
                </div>
                <div className="mb-3">
                  <span className="text-xs">Total Pax</span>

                  <div className="font-semibold text-sm">
                      {booking.pax} Orang
                    </div>
                    </div>
               

                <div className="mb-3">
                  <span className="text-xs">Waktu Booking</span>
                  <h4 className="text-sm font-semibold">
                    {moment(booking.bookingDate).format("dddd, DD MMM YYYY")},
                    Jam {booking.bookingTime}
                  </h4>
                </div>

                <div className="mb-3">
                  <span className="text-xs">Alamat</span>
                  <h4 className="text-sm font-semibold">
                    {booking.address}
                    <br />
                    {booking.locationName}
                  </h4>
                </div>
                {booking.note?.length > 0 && (
                  <div className="mb-3">
                    <span className="text-xs">Catatan</span>
                    <h4 className="text-sm font-semibold">{booking.note}</h4>
                  </div>
                )}

                <div className="mb-3">
                  <span className="text-xs">Status Bid</span>
                  <h4 className="text-sm font-semibold">
                    {booking.isOpen ? (
                      <span className=" gap-1 py-1 items-center px-2 text-xs rounded-lg bg-green-100 text-green-600">
                        Open
                      </span>
                    ) : (
                      <span className=" gap-1 py-1 mr-2 items-center px-2 text-xs rounded-lg bg-red-100 text-red-600">
                        Close
                      </span>
                    )}
                    {booking.selectedMuaId && (
                      <span className=" gap-1 py-1 items-center px-2 text-xs rounded-lg bg-gray-100 text-gray-600">
                        MUA Telah Dipilih
                      </span>
                    )}
                  </h4>
                </div>

               
                
                {
                  //   check if booking is open
                  (booking.isOpen && showBid) && <AjukanBid data={booking} check={showBid} />
                }
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
