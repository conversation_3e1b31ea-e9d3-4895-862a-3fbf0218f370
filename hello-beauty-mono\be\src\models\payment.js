const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
    trxId: {
        type: String,
        required: true
    },
    payId: {
        type: String,
        required: true,
        unique: true
    },
   userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
    required: true
},
    status: {
        type: String,
        required: true
    },
    totalBayar:{
        type: Number,
        required: true
    },
    totalHarga:{
        type: Number,
        required: true
    },
    isDp: {
        type: Boolean,
        required: true
    },
    percent: {
        type: Number,
        required: true
    },
    paidAt: {
        type: Date
    },
    buktiBayar:{
        type: String
    },
    paymentMethodId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "paymentMethod",
    },
    snapToken: {
        type: String
    }
  },{timestamps:true}); 

module.exports = mongoose.model('payment', paymentSchema);
