<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div
        class="px-4 pt-4 flex flex-col md:flex-row md:justify-between md:items-center gap-4"
      >
        <h1 class="font-bold text-xl">Portofolio</h1>
        <div class="flex flex-wrap gap-2 items-center">
          <button
            v-if="selectedImage.length > 0"
            class="btn-secondary"
            @click.prevent="getData"
            aria-label="Hapus Pilihan"
            title="Hapus Pilihan"
          >
            <icon name="mdi:close-circle-outline" class="mr-1" /> Hapus Pilihan
          </button>
          <button
            v-if="selectedImage.length > 0"
            class="btn-primary bg-red-600 flex items-center"
            @click.prevent="deleteImage(selectedImage)"
            aria-label="Hapus Gambar"
            title="Hapus Gambar"
          >
            <icon name="mdi:delete" class="mr-1" /> Hapus
            {{ selectedImage.length }} Gambar
          </button>
          <button
            v-if="selectedImage.length > 0"
            class="btn-primary bg-blue-600 flex items-center"
            @click.prevent="showPaket = true"
            aria-label="Ubah Paket"
            title="Ubah Paket"
          >
            <icon name="mdi:package-variant" class="mr-1" /> Ubah Paket
          </button>
          <label
            class="btn-primary flex items-center gap-2 cursor-pointer"
            aria-label="Upload Image"
            title="Upload Image"
          >
            <icon name="uil:image-upload" />
            Upload Image
            <input
              ref="file"
              class="hidden"
              type="file"
              accept="image/*"
              multiple
              @change="uploadFile"
            />
          </label>
        </div>
      </div>
      <div
        v-if="!loading && list.length > 0"
        class="p-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"
      >
        <div
          v-for="(g, i) in list"
          :key="i"
          class="border rounded-xl cursor-pointer relative group transition-shadow duration-200 hover:shadow-lg"
          :class="
            selectedImage.includes(g)
              ? 'border-2 border-primary ring-2 ring-primary'
              : ''
          "
          @click.prevent="addImage(g)"
          tabindex="0"
          :aria-pressed="selectedImage.includes(g)"
          :aria-label="'Pilih gambar ' + (g.packageName || i)"
        >
          <img
            :src="g.fullUrl"
            class="w-full h-40 object-contain rounded-xl bg-gray-50"
          />
          <div
            v-if="uploadingFiles.includes(g.name)"
            class="absolute inset-0 flex items-center justify-center bg-white/60 rounded-xl z-10"
          >
            <loader />
          </div>
          <div
            v-if="g.packageName"
            class="absolute bottom-2 left-2 rounded-lg text-primary border-primary px-2 py-1 text-xs bg-white/80 border"
          >
            {{ g.packageName }}
          </div>
          <transition name="fade">
            <div
              v-if="selectedImage.includes(g)"
              class="absolute inset-0 bg-primary/20 flex items-center justify-center rounded-xl"
            >
              <icon
                name="lets-icons:check-fill"
                class="text-primary text-4xl drop-shadow"
              />
            </div>
          </transition>
        </div>
      </div>
      <div
        v-if="!loading && list.length === 0"
        class="p-8 flex flex-col items-center justify-center text-gray-400"
      >
        <icon name="mdi:image-off-outline" class="text-6xl mb-2" />
        <p class="text-lg">Belum ada gambar portofolio</p>
      </div>
      <div
        v-if="loading"
        class="absolute inset-0 bg-white/70 flex items-center justify-center z-10"
      >
        <loader />
      </div>
    </div>

    <assign-paket
      :show="showPaket"
      :data="selectedImage"
      @closed="showPaket = false"
      @refresh="getData(), (showPaket = false), (selectedImage = [])"
    />

    <VueSidePanel
      v-model="showDeleteConfirm"
      lock-scroll
      hide-close-btn
      side="right"
      width="640px"
      @closed="showDeleteConfirm = false"
    >
      <template #default>
        <div class="p-4 flex flex-col items-center">
          <h2 class="text-xl font-bold mb-4 text-center">Konfirmasi Hapus</h2>
          <p class="text-center mb-2">
            Apakah Anda yakin ingin menghapus
            <span class="font-semibold">{{ selectedImage.length }}</span>
            gambar?
          </p>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4 w-full">
            <div
              v-for="(image, index) in selectedImage"
              :key="index"
              class="border rounded-lg overflow-hidden bg-gray-50"
            >
              <img
                :src="image.fullUrl"
                class="w-full h-32 object-contain border rounded-xl"
                :alt="'Preview ' + (image.packageName || index)"
              />
            </div>
          </div>
          <div class="flex gap-2 mt-6 w-full justify-center">
            <button
              class="btn-primary bg-red-600 flex items-center"
              @click="confirmDeleteImage"
              :disabled="deleteLoading"
              aria-label="Konfirmasi Hapus"
              title="Konfirmasi Hapus"
            >
              <icon name="mdi:check-bold" class="mr-1" />
              <span v-if="deleteLoading">Menghapus...</span>
              <span v-else>Konfirmasi</span>
            </button>
            <button
              class="btn-secondary"
              @click="showDeleteConfirm = false"
              aria-label="Batal"
              title="Batal"
            >
              <icon name="mdi:close" class="mr-1" /> Batal
            </button>
          </div>
        </div>
      </template>
    </VueSidePanel>

    <div
      v-if="isUploading"
      class="fixed inset-0 bg-black/30 z-50 flex items-center justify-center"
    >
      <div
        class="bg-white rounded-lg p-6 shadow-lg w-full max-w-md flex flex-col items-center"
      >
        <h2 class="font-bold mb-4">Mengunggah Berkas...</h2>
        <div class="mb-4 text-lg font-semibold text-primary">
          {{ uploadCount }}/{{ uploadTotal }}
        </div>
        <loader />
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: "auth-admin",
});

const { $toast } = useNuxtApp();

useHead({
  title: "Portofolio",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "Portofolio",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);
const showPaket = ref(false);
const showDeleteConfirm = ref(false);
const deleteLoading = ref(false);

const selectedImage = ref([]);
const isUploading = ref(false);
const uploadingFiles = ref([]);
const uploadCount = ref(0);
const uploadTotal = ref(0);

const uploadFile = (e) => {
  const files = e.target.files;
  isUploading.value = true;
  uploadCount.value = 0;
  uploadTotal.value = files.length;
  let uploaded = 0;
  let failed = 0;
  const total = files.length;
  for (let i = 0; i < files.length; i++) {
    uploadingFiles.value.push(files[i].name);
    uploadSingleFile(files[i], i)
      .then(() => {
        uploaded++;
      })
      .catch(() => {
        failed++;
      })
      .finally(() => {
        const idx = uploadingFiles.value.indexOf(files[i].name);
        if (idx !== -1) uploadingFiles.value.splice(idx, 1);
        uploadCount.value++;
        if (uploaded + failed === total) {
          isUploading.value = false;
          if (uploaded > 0) {
            $toast.success(`${uploaded} berkas berhasil diunggah`);
          }
          if (failed > 0) {
            $toast.error(`${failed} berkas gagal diunggah`);
          }
        }
      });
  }
};

const addImage = (image) => {
  // check if image already selected
  if (selectedImage.value.includes(image)) {
    selectedImage.value = selectedImage.value.filter((i) => i !== image);
    return;
  }
  selectedImage.value.push(image);
};

const uploadSingleFile = async (file, _idx) => {
  try {
    const formData = new FormData();
    formData.append("file", file);
    const res = await adminFile("/portofolio", formData, {
      onUploadProgress: (_progressEvent) => {
        // progress diabaikan
      },
    });
    if (res.data) {
      getData();
      selectedImage.value = [];
    }
  } catch (error) {
    console.log(error);
    throw error;
  }
};

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(`/portofolio?page=${page.value}&limit=20`);
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
      $toast.info("Data tidak ditemukan");
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response?.data?.message;
    $toast.error("Gagal mengambil data");
  }
};

const deleteImage = (images) => {
  showDeleteConfirm.value = true;
};

const confirmDeleteImage = async () => {
  try {
    deleteLoading.value = true;
    for (const image of selectedImage.value) {
      await adminDelete(`/portofolio/${image._id}`);
    }
    getData();
    selectedImage.value = [];
    showDeleteConfirm.value = false;
    $toast.success("Gambar berhasil dihapus");
  } catch (error) {
    console.log(error);
    $toast.error("Gagal menghapus gambar");
  } finally {
    deleteLoading.value = false;
  }
};

onMounted(() => {
  getData();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
