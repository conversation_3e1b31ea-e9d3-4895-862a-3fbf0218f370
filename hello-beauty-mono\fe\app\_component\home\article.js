"use client";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import { Icon } from "@iconify/react";
import { FreeMode } from "swiper/modules";
import { useEffect, useState } from "react";
import Link from "next/link";
import decodeHTMLEntities from "@/app/_helper/decodeHtml";

const Articles = () => {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(false);

  const getArticle = async () => {
    try {
      setLoading(true);
      const res = await fetch("/api/wp/posts");
      setLoading(false);
      const data = await res.json();
      setArticles(Array.isArray(data) ? data : []);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
      getArticle();
  }, []);

  return (
    <>
      {articles.length === 0 && !loading && (
        <div className="text-center py-6">Data not found</div>
      )}
      <Swiper
        slidesPerView={"auto"}
        centeredSlides={true}
        spaceBetween={8}
        freeMode={true}
        modules={[FreeMode]}
      >
        {articles.map((article, index) => (
          <SwiperSlide key={index}>
            <div className="px-3">
              <span className="uppercase text-sm tracking-[.2rem]">
                {decodeHTMLEntities(article._embedded["wp:term"][0][0].name)}
              </span>
            
              <h3 className="text-2xl font-bold py-6">
                {article.title.rendered}
              </h3>
              <p className="text-sm mb-6">
                {article.excerpt.rendered.slice(0, 200)}
              </p>

              <Link
                href={`/blog/${article.slug}`}
                className="uppercase tracking-[.2rem] text-hb-pink text-sm flex items-center gap-4"
              >
                <div>learn more</div>
                <Icon
                  icon="guidance:left-arrow"
                  className="text-lg text-hb-pink"
                />
              </Link>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </>
  );
};

export default Articles;
