{"name": "fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 8902", "build": "next build", "start": "next start -p 8902", "lint": "next lint"}, "dependencies": {"@iconify/react": "^5.0.2", "aos": "^2.3.4", "axios": "^1.7.7", "browser-image-compression": "^2.0.2", "http-proxy": "^1.18.1", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "next": "14.2.5", "react": "^18", "react-dom": "^18", "react-medium-image-zoom": "^5.2.10", "react-responsive-modal": "^6.4.2", "swiper": "^11.1.14"}, "devDependencies": {"@types/node": "22.7.5", "@types/react": "18.3.11", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.6.3"}, "engines": {"node": "20.x"}}