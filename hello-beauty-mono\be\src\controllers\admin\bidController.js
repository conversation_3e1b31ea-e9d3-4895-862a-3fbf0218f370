// const transactionPay = require("../../models/transaction-pay");

const transaction = require("../../models/transaction");
const historyMua = require("../../models/history-mua");
const bid = require("../../models/bid");
const mua = require("../../models/mua");
const { logFunction, logError, logSuccess } = require('../../utils/logger');

const listBid = async (req, res) => {
  const fnName = 'listBid';
  logFunction(fnName, { trxId: req.params.id });

  try {
    const id = req.params.id;
    const searchBid = await bid.find({ trxId: id });

    logSuccess(fnName, { 
      trxId: id,
      totalBids: searchBid.length
    });

    return res.status(200).json({ message: "List bid", data: searchBid });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({ message: error.message });
  }
};

const updateBid = async (req, res) => {
  const fnName = 'updateBid';
  logFunction(fnName, { 
    trxId: req.params.id,
    isOpen: req.body.isOpen
  });

  try {
    const id = req.params.id;
    const body = req.body;

    const checkTrx = await transaction.findOne({
      trxId: id,
    });
    if (!checkTrx) {
      logFunction(fnName, { error: 'Transaction not found', trxId: id }, 'warn');
      return res.status(400).json({ message: "Transaksi tidak ditemukan" });
    }
    //   update isOpen
    checkTrx.isOpen = body.isOpen;
    checkTrx.status = 'FIND_MUA';
    await checkTrx.save();

    logSuccess(fnName, { 
      trxId: id,
      isOpen: body.isOpen,
      status: 'FIND_MUA'
    });

    return res.status(200).json({ message: "Berhasil update status" });
  } catch (e) {
    logError(fnName, e);
    return res.status(500).json({ message: "Terjadi Kesalahan" });
  }
};

const selectedMua = async (req, res) => {
  const fnName = 'selectedMua';
  logFunction(fnName, { 
    trxId: req.body.trxId,
    muaId: req.body.muaId
  });

  try {
    const { trxId, muaId } = req.body;
    // check mua
    const searchMua = await mua.findOne({ _id: muaId });
    if (!searchMua) {
      logFunction(fnName, { error: 'MUA not found', muaId }, 'warn');
      return res.status(404).json({ message: "Mua not found" });
    }
    const searchBid = await bid.findOne({ trxId, muaId: muaId });
    if (!searchBid) {
      logFunction(fnName, { error: 'Bid not found', trxId, muaId }, 'warn');
      return res.status(404).json({ message: "Bid not found" });
    }

    //   update selected
    searchBid.selected = true;
    await searchBid.save();

    // update transaction
    const searchTrx = await transaction.findOne({ trxId });
    searchTrx.isOpen = false;
    searchTrx.selectedMuaId = muaId;
    searchTrx.selectedMuaName = searchMua.muaName || searchMua.name;
    searchTrx.selectedMuaPhone = searchMua.phone;
    searchTrx.status = "SELECTED_MUA";
    await searchTrx.save();

    logSuccess(fnName, { 
      trxId,
      muaId,
      muaName: searchMua.muaName || searchMua.name,
      status: 'SELECTED_MUA'
    });

    return res.status(200).json({ message: "Berhasil memilih MUA" });
  } catch (e) {
    logError(fnName, e);
    return res.status(500).json({ message: "Terjadi Kesalahan" });
  }
};

const cancelMua = async (req, res) => {
  const fnName = 'cancelMua';
  logFunction(fnName, { 
    trxId: req.body.trxId,
    muaId: req.body.muaId,
    alasan: req.body.alasan
  });

  try {
    const { trxId, muaId, alasan } = req.body;
    // check mua
    const searchMua = await mua.findOne({ _id: muaId });
    if (!searchMua) {
      logFunction(fnName, { error: 'MUA not found', muaId }, 'warn');
      return res.status(404).json({ message: "Mua not found" });
    }

    const searchBid = await bid.findOne({ trxId, muaId: muaId });

    if (!searchBid) {
      logFunction(fnName, { error: 'Bid not found', trxId, muaId }, 'warn');
      return res.status(404).json({ message: "Bid not found" });
    }

    //   update selected
    searchBid.selected = false;
    await searchBid.save();

    // update transaction
    const searchTrx = await transaction
      .findOne({ trxId })
      .populate("selectedMuaId");
    searchTrx.isOpen = true;
    searchTrx.selectedMuaId = null;
    searchTrx.selectedMuaName = null;
    searchTrx.selectedMuaPhone = null;
    searchTrx.status = "FIND_MUA";
    await searchTrx.save();

    // insert alasan to historyMua
    await historyMua.create({
      trxId,
      muaId,
      alasan,
      createdAt: new Date(),
    });

    logSuccess(fnName, { 
      trxId,
      muaId,
      alasan,
      status: 'FIND_MUA'
    });

    return res.status(200).json({ message: "Berhasil membatalkan MUA" });
  } catch (e) {
    logError(fnName, e);
    return res.status(500).json({ message: "Terjadi Kesalahan" });
  }
};

const historyPemilihanMua = async (req, res) => {
  const fnName = 'historyPemilihanMua';
  logFunction(fnName, { trxId: req.params.id });

  try {
    const trxId = req.params.id;
    const searchHistory = await historyMua.find({ trxId }).populate('muaId');

    logSuccess(fnName, { 
      trxId,
      totalHistory: searchHistory.length
    });

    return res.status(200).json({ message: "List history", data: searchHistory });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({ message: error.message });
  }
};

module.exports = {
  listBid,
  updateBid,
  selectedMua,
  cancelMua,
  historyPemilihanMua,
};
