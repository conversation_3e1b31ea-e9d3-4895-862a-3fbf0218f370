// Import Swiper React components
import {Swiper, SwiperSlide} from 'swiper/react';
import { useState } from 'react';
import { Modal } from 'react-responsive-modal';
import 'react-responsive-modal/styles.css';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import './testimonial.css';

// import './styles.css';
// import required modules
import {Pagination} from 'swiper/modules';
import {Icon} from '@iconify/react';

const Testimonials = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const openModal = (imageSrc) => {
    setSelectedImage(imageSrc);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  };

  return (
    <div className=" px-6 py-12">
    <Swiper slidesPerView={2} autoplay={{delay: 2500,disableOnInteraction: false,}} centeredSlides={true} spaceBetween={8} modules={[]}>
        <SwiperSlide>
          <img src="/testimoni/3.png" alt="testimoni" onClick={() => openModal("/testimoni/3.png")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/4.jpg" alt="testimoni" onClick={() => openModal("/testimoni/4.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/5.jpg" alt="testimoni" onClick={() => openModal("/testimoni/5.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/6.jpg" alt="testimoni" onClick={() => openModal("/testimoni/6.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/7.jpg" alt="testimoni" onClick={() => openModal("/testimoni/7.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/8.jpg" alt="testimoni" onClick={() => openModal("/testimoni/8.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/9.jpg" alt="testimoni" onClick={() => openModal("/testimoni/9.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/10.jpg" alt="testimoni" onClick={() => openModal("/testimoni/10.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/11.jpg" alt="testimoni" onClick={() => openModal("/testimoni/11.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/12.jpg" alt="testimoni" onClick={() => openModal("/testimoni/12.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/13.jpg" alt="testimoni" onClick={() => openModal("/testimoni/13.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/14.jpg" alt="testimoni" onClick={() => openModal("/testimoni/14.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/15.jpg" alt="testimoni" onClick={() => openModal("/testimoni/15.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/16.jpg" alt="testimoni" onClick={() => openModal("/testimoni/16.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/17.jpg" alt="testimoni" onClick={() => openModal("/testimoni/17.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/18.jpg" alt="testimoni" onClick={() => openModal("/testimoni/18.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/19.jpg" alt="testimoni" onClick={() => openModal("/testimoni/19.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/20.jpg" alt="testimoni" onClick={() => openModal("/testimoni/20.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/21.jpg" alt="testimoni" onClick={() => openModal("/testimoni/21.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/22.jpg" alt="testimoni" onClick={() => openModal("/testimoni/22.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/23.jpg" alt="testimoni" onClick={() => openModal("/testimoni/23.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/24.jpg" alt="testimoni" onClick={() => openModal("/testimoni/24.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/25.jpg" alt="testimoni" onClick={() => openModal("/testimoni/25.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/26.jpg" alt="testimoni" onClick={() => openModal("/testimoni/26.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/27.jpg" alt="testimoni" onClick={() => openModal("/testimoni/27.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/28.jpg" alt="testimoni" onClick={() => openModal("/testimoni/28.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/29.jpg" alt="testimoni" onClick={() => openModal("/testimoni/29.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/30.jpg" alt="testimoni" onClick={() => openModal("/testimoni/30.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/31.jpg" alt="testimoni" onClick={() => openModal("/testimoni/31.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        <SwiperSlide>
          <img src="/testimoni/32.jpg" alt="testimoni" onClick={() => openModal("/testimoni/32.jpg")} className="cursor-pointer" />
        </SwiperSlide>
        
      </Swiper>

      <Modal open={isModalOpen} onClose={closeModal} center showCloseIcon={false} styles={{
        modal: {
          maxWidth: '90vw',
          maxHeight: '90vh',
          padding: 0,
          borderRadius: '8px',
          background: 'transparent',
          boxShadow: 'none'
        },
        overlay: {
          background: 'rgba(0, 0, 0, 0.8)'
        }
      }}>
        <div className="relative">
          <img 
            src={selectedImage} 
            alt="Testimonial" 
            className="max-w-full max-h-[90vh] object-contain"
          />
          <button 
            onClick={closeModal}
            className="absolute top-4 right-4 text-white text-3xl bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75 transition-all"
          >
            &times;
          </button>
        </div>
      </Modal>
    </div>
  );
}

export default Testimonials;
