"use client"
import Link from 'next/link'
import {useState} from 'react'
import {apiPublic} from '../_helper/api'
import ErrorSpan from '../_component/errorSpan'
import {Icon} from '@iconify/react'
import {setCookie} from '@/app/_helper/cookie';


export default function Home() {
  const [email, setEmail] = useState('')
  const [name, setName] = useState('')
  const [phone, setPhone] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [passwordVisible, setPasswordVisible] = useState(false);

  const handleSubmit = async (e) => {
    try {
      e.preventDefault()
      setError('')
      setLoading(true)
      const res = await apiPublic('POST', '/auth/register', {name, email, phone, password})
      setCookie('hb_token', res.data.token);
      setCookie('user', JSON.stringify(res.data.user));
      setLoading(false)
      window.location.href = '/';
    } catch (error) {
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div className="h-14 flex items-center px-4">
        <Link href="/">
        <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link>
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="mt-28">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Register<span className="text-hb-pink">.</span></h3>
      </div>

      <form className="" onSubmit={handleSubmit}>
      <div className="grid grid-cols-1  px-4 mt-20">
        <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/sms.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} required type="text" placeholder="Nama Lengkap" value={name} onChange={(e) => setName(e.target.value)} className="w-full  focus:outline-none"  />
        </div>
        <div  className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/form-nama.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} required type="email" placeholder="Email " value={email} onChange={(e) => setEmail(e.target.value)}  className="w-full  focus:outline-none"  />
        </div>

         <div  className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/call.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} required type="text" inputmode="numeric" placeholder="Nomor Whatsapp" value={phone} onChange={(e) => setPhone(e.target.value)}  className="w-full  focus:outline-none"  />
        </div>


        <div  className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/fingerprint.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} required type={passwordVisible ? "text" : "password"} placeholder="Password" value={password} onChange={(e) => setPassword(e.target.value)}  className="w-full  focus:outline-none"  />
          <button
            type="button"
            onClick={() => setPasswordVisible(!passwordVisible)}
            className="ml-2"
          >
            <Icon icon={passwordVisible ? "mdi:eye-off" : "mdi:eye"} />
          </button>
        </div>
      </div>
      <div className="px-4">
        {
          error && <ErrorSpan msg={error} />
        }

      </div>
      <div className=" px-6">
        <button type="submit"  className="btn-primary flex items-center justify-center">
          {
            loading && <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin mr-2" />
          }
          Register
        </button>
        {/* <Link href="/login" className="btn-secondary mt-2 flex justify-center">
          Login
        </Link> */}
        <div className="py-6 w-full text-center text-gray-400 text-xs">
          Sudah Punya Akun ? <Link href="/login" className="text-hb-pink underline">Login</Link>
        </div>
         {/* <Link href="/register" className=" rounded-full w-full text-center py-2 border border-gray-500 text-gray-500 mt-2 flex justify-center">
         <img src="/google.jpeg" alt="Logo" className="h-6 mr-4" />
         Sign Up With Google
        </Link> */}
        </div>
      </form>

        <div className="h-40"></div>
      </div>
  );
}
