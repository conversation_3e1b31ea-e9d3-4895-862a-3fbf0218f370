const mongoose = require('mongoose');

const itemsSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    price: {
        type: Number,
        required: true
    },
    priceCoret: {
        type: Number,
        required: true
    },
    priceShare: {
        type: Number,
        required: true
    },
    isActive: {
        type: Boolean,
        required: true
    },
    description: {
        type: String,
        required: true
    },
}, { timestamps: true });

const packageSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    isActive: {
        type: Boolean,
        required: true
    },
    items: [itemsSchema],
}, { timestamps: true });

module.exports = mongoose.model('package', packageSchema);
