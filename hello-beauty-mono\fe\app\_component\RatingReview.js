import { useState, useEffect } from "react";
import { api } from "../_helper/api";

export default function RatingReview({ bookingId, initialRating = 0, initialReview = "", onSuccess }) {
  const [rating, setRating] = useState(initialRating);
  const [review, setReview] = useState(initialReview);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (initialRating || initialReview) {
      setIsEditing(false);
    } else {
      setIsEditing(true);
    }
  }, [initialRating, initialReview]);

  const handleRating = (rate) => {
    setRating(rate);
  };

  const handleSubmit = async () => {
    try {
      setError(null);
      setLoading(true);
      const res = await api("POST", `/review`, {
        bookingId,
        rating,
        review,
      });
      setSuccess(true);
      setIsEditing(false);
      setLoading(false);
      if (onSuccess) onSuccess();
    } catch (error) {
      setError("Terjadi kesalahan saat mengirim review");
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-white border rounded-xl">
      <h3 className="text-lg font-semibold mb-2">Rating & Review</h3>
      {success ? (
        <p className="text-gray-500">Terima kasih atas review Anda!</p>
      ) : (
        <>
          {isEditing ? (
            <>
              <div className="flex gap-1 mb-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => handleRating(star)}
                    className={`text-2xl ${star <= rating ? "text-yellow-500" : "text-gray-300"}`}
                  >
                    ★
                  </button>
                ))}
              </div>
              <textarea
                className="w-full p-2 border rounded"
                rows="4"
                placeholder="Tulis review Anda..."
                value={review}
                onChange={(e) => setReview(e.target.value)}
              ></textarea>
              {error && <p className="text-red-500">{error}</p>}
              <button
                onClick={handleSubmit}
                className="btn-primary mt-2 w-full"
                disabled={loading}
              >
                {loading ? "Mengirim..." : initialRating || initialReview ? "Edit Review" : "Kirim Review"}
              </button>
            </>
          ) : (
            <div>
              <div className="flex gap-1 mb-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <span
                    key={star}
                    className={`text-2xl ${star <= rating ? "text-yellow-500" : "text-gray-300"}`}
                  >
                    ★
                  </span>
                ))}
              </div>
              <p>{review}</p>
              <button
                onClick={() => setIsEditing(true)}
                className="btn-primary mt-2 w-full"
              >
                Edit
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
