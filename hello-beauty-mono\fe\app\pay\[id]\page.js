"use client";
import LoadingFull from "@/app/_component/loadingFull";
import { api } from "@/app/_helper/api";
import convertRp from "@/app/_helper/convertorp";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

export default function Home() {
  const [listPayment, setListPayment] = useState([]);
  const [selectedPayment, setSelectedPayment] = useState("");
  const [loadingPayment, setLoadingPayment] = useState(true);
  const [errorPayment, setErrorPayment] = useState("");
  const [order, setOrder] = useState({});
  const [loadingOrder, setLoadingOrder] = useState(true);
  const [errorOrder, setErrorOrder] = useState("");
  const [pay, setPay] = useState([]);
  const [loadingPay, setLoadingPay] = useState(true);
  const [errorPay, setErrorPay] = useState("");
  const [token, setToken] = useState("");
  const params = useParams();

  const getDetailOrder = async (trxId) => {
    try {
      setLoadingOrder(true);
      setErrorOrder("");
      const res = await api("GET", `/order/${trxId}`);
      setLoadingOrder(false);
      setOrder(res.data);
    } catch (error) {
      setErrorOrder("Terjadi kesalahan");
      setLoadingOrder(false);
      console.log(error);
    }
  };

  const getDetailPay = async () => {
    try {
      setLoadingPay(true);
      setErrorPay("");
      const res = await api("GET", `/pay/${params.id}`);
      setLoadingPay(false);
      
      if (!res.data) {
        setErrorPay("Data pembayaran tidak ditemukan");
        return;
      }

      setPay(res.data);
      
      if (res.data.trxId) {
        await getDetailOrder(res.data.trxId);
      }
      
      if (res.data.paymentMethodId) {
        setSelectedPayment(res.data.paymentMethodId);
      }
      
      if (res.data.snapToken && res.data.status !== "PAID" && window.snap) {
        window.snap.pay(res.data.snapToken, {
          onSuccess: function (result) {
            console.log("Payment success:", result);
            window.location.reload();
          },
          onPending: function (result) {
            console.log("Payment pending:", result);
            // Show pending status to user
            setErrorOrder("Pembayaran sedang diproses");
          },
          onError: function (result) {
            console.log("Payment error:", result);
            setErrorOrder("Terjadi kesalahan saat memproses pembayaran");
          },
          onClose: function() {
            console.log("Payment popup closed");
          }
        });
      }
    } catch (error) {
      setLoadingPay(false);
      setErrorPay("Terjadi kesalahan saat memuat data pembayaran");
      console.error("Error in getDetailPay:", error);
    }
  };

  const getPaymentMethod = async () => {
    try {
      setLoadingPayment(true);
      const res = await api("GET", "/payment-method");
      setListPayment(res.data);
      setLoadingPayment(false);
    } catch (error) {
      setLoadingPayment(false);
      console.log(error);
    }
  }

  const createPay = async (id) => {
    try {
      if (token) {
        setErrorOrder("Pembayaran sedang diproses");
        return;
      }

      if (!selectedPayment) {
        setErrorOrder("Pilih metode pembayaran terlebih dahulu");
        return;
      }

      const res = await api("POST", `/pay/create/${id}`, {
        paymentMethod: selectedPayment.code,
      });

      if (!res.data) {
        setErrorOrder("Gagal membuat token pembayaran");
        return;
      }

      setToken(res.data);
      
      if (window.snap) {
        window.snap.pay(res.data, {
          onSuccess: function (result) {
            console.log("Payment success:", result);
            window.location.reload();
          },
          onPending: function (result) {
            console.log("Payment pending:", result);
            setErrorOrder("Pembayaran sedang diproses");
          },
          onError: function (result) {
            console.log("Payment error:", result);
            setErrorOrder("Terjadi kesalahan saat memproses pembayaran");
          },
          onClose: function() {
            console.log("Payment popup closed");
          }
        });
      } else {
        setErrorOrder("Gagal memuat halaman pembayaran");
      }
    } catch (error) {
      console.error("Error in createPay:", error);
      setErrorOrder("Terjadi kesalahan saat membuat pembayaran");
    }
  };

  const checkPayment = async (id) => {
    try {
      const res = await api("GET", `/pay/${id}`);
      if (!res.data) {
        console.error("No payment data received");
        return null;
      }
      return res.data;
    } catch (error) {
      console.error("Error checking payment:", error);
      return null;
    }
  };

  const countTotal = (payment) => {
    if (!payment || !pay.totalBayar) return 0;
    
    let totalBayar = Number(pay.totalBayar);
    
    if (payment.adminPercentage) {
      const adminPercentage = Number(payment.adminPercentage);
      totalBayar += totalBayar * (adminPercentage / 100);
    }
    
    if (payment.adminFlat) {
      const adminFlat = Number(payment.adminFlat);
      totalBayar += adminFlat;
    }
    
    return Math.round(totalBayar);
  };

  const countAdmin = (payment) => {
    if (!payment || !pay.totalBayar) return 0;
    
    let admin = 0;
    const totalBayar = Number(pay.totalBayar);
    
    if (payment.adminPercentage) {
      const adminPercentage = Number(payment.adminPercentage);
      admin += totalBayar * (adminPercentage / 100);
    }
    
    if (payment.adminFlat) {
      const adminFlat = Number(payment.adminFlat);
      admin += adminFlat;
    }
    
    return Math.round(admin);
  };

  const totalBayar = useMemo(() => {
    if (!pay || !pay.totalBayar) return 0;
    return Number(pay.totalBayar);
  }, [pay?.totalBayar]);

  const injectMidtrans = () => {
    if (document.querySelector('#snap-midtrans')) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.id = 'snap-midtrans';
      script.src = process.env.NEXT_PUBLIC_MIDTRANS_SNAP_URL;
      script.setAttribute("data-client-key", process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY);
      script.async = true;
      
      script.onload = () => {
        console.log("Midtrans script loaded successfully");
        resolve();
      };
      
      script.onerror = (error) => {
        console.error("Failed to load Midtrans script:", error);
        reject(error);
      };
      
      document.body.appendChild(script);
    });
  };

  useEffect(() => {
    let intervalId = null;
    
    const init = async () => {
      try {
        await injectMidtrans();
        await Promise.all([getDetailPay(), getPaymentMethod()]);
      } catch (error) {
        console.error('Error initializing payment:', error);
        setErrorPay("Terjadi kesalahan saat memuat halaman pembayaran");
      }
    };
    
    init();

    // Set up payment status check interval
    const startPaymentCheck = () => {
      if (intervalId) {
        clearInterval(intervalId);
      }

      intervalId = setInterval(async () => {
        try {
          const paymentData = await checkPayment(params.id);
          if (paymentData && paymentData.status === "PAID") {
            clearInterval(intervalId);
            setPay(paymentData);
          }
        } catch (error) {
          console.error("Error checking payment status:", error);
        }
      }, 5000);
    };

    startPaymentCheck();

    // Cleanup interval on unmount
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [params.id]);

  const handlePay = () => {
    try {
      if (!selectedPayment) {
        setErrorOrder("Pilih metode pembayaran terlebih dahulu");
        return;
      }
      
      if (pay.status === "PAID") {
        setErrorOrder("Pembayaran sudah dilakukan");
        return;
      }
      
      createPay(params.id);
    } catch (error) {
      console.error("Error in handlePay:", error);
      setErrorOrder("Terjadi kesalahan saat memproses pembayaran");
    }
  };

  const handlePaymentSelect = (item) => {
    if (!item.isActive) return;
    setSelectedPayment(item);
    setErrorOrder("");
    
    // Scroll to admin fee section
    const adminFeeSection = document.getElementById('admin-fee-section');
    if (adminFeeSection) {
      adminFeeSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  if (errorPay) {
    return (
      <div className="max-w-[480px] min-h-screen mx-auto grid-bg flex items-center justify-center">
        <div className="text-center p-4">
          <Icon icon="solar:danger-circle-bold-duotone" className="text-[160px] mx-auto text-red-500" />
          <h3 className="text-2xl font-semibold mt-6">Terjadi Kesalahan</h3>
          <p className="text-gray-700 mt-4">{errorPay}</p>
          <Link href="/booking" className="btn-primary mt-8 inline-block">
            Kembali
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      {(loadingOrder || loadingPay || loadingPayment) && <LoadingFull />}
      <div>
        <div className="h-14 flex items-center px-4">
          <Link href={`/booking/${pay.trxId}`}>
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </Link>
          <img src="/logo.png" alt="Logo" className="mx-auto h-8" />
        </div>

        {pay.status === 'paid' ? (
          <div className="mt-12 text-center">
            <Icon icon="solar:check-circle-bold-duotone" className="text-[160px] mx-auto text-green-500" />
            <h3 className="text-2xl font-semibold text-center mt-6">
              Terima Kasih
            </h3>
            <p className="text-gray-700 mt-4">
              Pembayaran Anda telah berhasil.
            </p>

            <div className="mt-12">
              <h3 className="text-lg font-semibold mb-3">
                Detail Pembayaran
              </h3>
              <div className="grid grid-cols-1 gap-3">
                <div className="border border-gray-200 rounded-lg p-3">
                   <div className="flex justify-between py-2 border-b">
                    <span>Booking ID</span>
                    <span>{pay.trxId}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span>Metode Pembayaran</span>
                    <span>{pay.paymentMethodId?.name}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span>Biaya Admin</span>
                    <span>{convertRp(countAdmin(pay.paymentMethodId))}</span>
                  </div>
                  <div className="flex font-semibold justify-between py-2">
                    <span>Total Pembayaran</span>
                    <span>{convertRp(countTotal(pay.paymentMethodId))}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="mt-12 px-4">
              <h3 className="text-2xl font-semibold text-center mb-6">
                <span className="block">Pembayaran</span>
                {pay.isDp ? "Down Payment" : "Pelunasan"}
                <span className="text-hb-pink">.</span>
              </h3>
            </div>

             <div className="mt-12 text-center">
              <span className="text-gray-700">Order ID</span>
              <span className="block text-xl text-hb-pink font-semibold">
                #{pay.trxId}
              </span>
            </div>

            <div className="mt-4 text-center">
              <span className="text-gray-700">Nominal</span>
              <span className="block text-3xl font-semibold">
                {convertRp(totalBayar)}
              </span>
            </div>

            <div className="mt-12 px-4">
               {errorOrder && (
              <div className="p-3 text-sm rounded-lg mt-4 bg-red-50 text-red-500">{errorOrder}</div>
            )}
            </div>
            

            <div className="mt-12 px-4" >
              <h3 className="text-lg font-semibold mb-3">
                Pilih Metode Pembayaran
              </h3>
              <div className="grid grid-cols-1 gap-3 cursor-pointer">
                {listPayment.map((item) => (
                  <div
                    key={item._id}
                    onClick={() => item.isActive ? handlePaymentSelect(item) : null}
                    className={`border border-gray-200 rounded-lg p-3 flex items-center justify-between ${!item.isActive ? "opacity-50 cursor-not-allowed bg-gray-100" : ""} ${selectedPayment._id === item._id ? "bg-hb-pink-light-2 border-hb-pink text-hb-pink font-semibold" : ""}`}
                  >
                    <div className="flex gap-1 items-center">
                      <img src={item.logo} alt={item.name} className="h-8" />
                    </div>
                    <div className="text-right">
                      <span className="text-xs">
                        {item.name}
                      </span>
                      <span className="block text-xs text-gray-500">
                        {convertRp(countTotal(item))}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {selectedPayment && (
              <div id="admin-fee-section" className="px-4">
                <div className="border rounded-lg bg-gray-50 mt-4">
                  <div className="p-3">
                    <div className="flex justify-between border-b py-2">
                      <span>Biaya Admin</span>
                      <span>{convertRp(countAdmin(selectedPayment))}</span>
                    </div>
                    <div className="flex justify-between py-2 font-semibold">
                      <span>Total Pembayaran</span>
                      <span className="">{convertRp(countTotal(selectedPayment))}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            

            <div className="h-40">

            </div>

           
            <div className="mt-3 p-4 fixed bottom-0 w-full bg-white max-w-[480px]">
              <button
                onClick={handlePay}
                className="btn-primary flex justify-center"
              >
                {
                  !selectedPayment ? "Bayar Sekarang" : `Bayar dengan ${selectedPayment.name}`
                }
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
