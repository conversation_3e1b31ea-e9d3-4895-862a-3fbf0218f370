<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Payment</h1>
      </div>
      <div class="grid grid-cols-2 md:grid-cols-4 p-3 gap-2 mt-4">
        <input
          v-model="search.search"
          type="text"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Order ID..."
        />
        <select
          v-model="search.status"
          class="p-2 border border-gray-200 rounded-lg"
        >
          <option value="">All Status</option>
          <option value="PAID">Paid</option>
          <option value="UNPAID">Unpaid</option>
        </select>
        <div class="grid grid-cols-2 gap-2">
          <button class="btn-primary" @click.prevent="getData()">Cari</button>
          <button
            class="btn-secondary"
            @click.prevent="
              (search.search = ''), (search.status = ''), getData()
            "
          >
            Reset
          </button>
        </div>
      </div>

      <table class="w-full text-sm mt-4">
        <thead>
          <tr class="bg-gray-100 font-semibold">
            <td class="p-2">
              <p>OrderID</p>
            </td>
            <td>Customer</td>
            <td>Tipe</td>
            <td>Total Bayar</td>
            <td>Total Harga</td>
            <td>Sisa</td>
            <td>Status</td>

            <td class="p-2">Waktu Pembayaran</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(d, i) in list" :key="i" class="hover:bg-gray-50 border-b">
            <td class="px-2 py-3">
              <nuxt-link
                :to="`/order/${d.trxId}`"
                class="text-primary underline"
              >
                {{ d.trxId }}
              </nuxt-link>
            </td>
            <td class="px-2 py-3">
              <nuxt-link
                v-if="d.userId"
                :to="`/customer/${d.userId}`"
                class="text-primary underline"
              >
                {{ d.name }}
              </nuxt-link>

              <p>{{ d.phone }}</p>
              <p>{{ d.email }}</p>
            </td>
            <td class="px-2 py-3">
              <span
                :class="
                  d.isDp
                    ? 'bg-green-100 text-green-500'
                    : 'bg-blue-100 text-blue-500'
                "
                class="px-2 py-1 rounded-lg text-center font-semibold"
              >
                {{ d.isDp ? "DP" : "Pelunasan" }}
              </span>
            </td>
            <td class="px-2 py-2">
              {{ useRupiah(d.totalBayar) }}
            </td>

            <td class="px-2 py-2">
              {{ useRupiah(d.totalHarga) }}
            </td>

            <td class="px-2 py-2">
              {{ d.isDp ? useRupiah(d.totalHarga - d.totalBayar) : "Rp0" }}
            </td>

            <td class="px-2 py-2">
              <span
                :class="
                  d.status.toUpperCase() === 'PAID'
                    ? 'bg-green-100 text-green-500'
                    : 'bg-red-100 text-red-500'
                "
                class="px-2 py-1 rounded-lg text-center font-semibold"
              >
                {{
                  d.status.toUpperCase() === "PAID" ? "Lunas" : "Belum Lunas"
                }}
              </span>
            </td>

            <td class="px-2 py-3">
              <p class="text-sm">
                {{
                  d.paidAt ? useMoment(d.paidAt).format("DD MMM, HH:mm") : "-"
                }}
              </p>
            </td>
            <!-- <td class="px-2 py-3">
              <p>{{ d }}</p>
            </td> -->
          </tr>
        </tbody>

        <tbody v-if="errorMsg && !loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              <icon name="icon-park-twotone:data" class="text-2xl block" />
              {{ errorMsg }}
            </td>
          </tr>
        </tbody>

        <!-- tbody loding -->
        <tbody v-if="loading">
          <tr>
            <td class="p-2 text-center text-gray-500 py-6" colspan="10">
              Memuat
              <icon name="svg-spinners:3-dots-bounce" class="text-2xl" />
            </td>
          </tr>
        </tbody>
      </table>

      <div class="p-3">
        <div class="flex justify-end gap-2 items-center mx-auto">
          <button
            v-if="page > 1"
            class="btn-secondary"
            @click.prevent="page--, page < 1 ? (page = 1) : '', getData()"
          >
            Prev
          </button>
          <form @submit="getData()">
            <input
              v-model="page"
              class="text-sm py-2 w-10 text-center border rounded-lg"
              type="number"
            />
          </form>

          <button class="btn-secondary" @click.prevent="page++, getData()">
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Payment",
  meta: [
    {
      hid: "description",
      name: "description",
      content: "Payment",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const page = ref(1);
const search = ref({
  search: "",
  status: "",
});

const getData = async () => {
  try {
    loading.value = true;
    errorMsg.value = "";
    list.value = [];
    const { data } = await adminGet(
      `/pay?page=${page.value}&limit=30&search=${search.value.search}&status=${search.value.status}`
    );
    loading.value = false;
    list.value = data.data;
    if (data.data.length === 0) {
      errorMsg.value = "Data tidak ditemukan";
    }
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

onMounted(() => {
  getData();
});
</script>

<style>
.input-group .select2-container {
  height: 40px !important;
}
</style>
