'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Icon } from '@iconify/react';
import ErrorSpan from '../../_component/errorSpan';

export default function CompleteProfile() {
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const token = searchParams.get('token');
    const missing = searchParams.get('missing');
    
    if (!token || missing !== 'phone') {
      router.push('/login');
    } else {
      // Store token temporarily
      localStorage.setItem('tempToken', token);
    }
  }, [router, searchParams]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const tempToken = localStorage.getItem('tempToken');
      
      if (!tempToken) {
        throw new Error('Token tidak ditemukan');
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8900'}/auth/update-phone`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tempToken}`
        },
        body: JSON.stringify({ phone })
      });

      const data = await response.json();

      if (data.success) {
        // Remove temp token and store permanent token
        localStorage.removeItem('tempToken');
        localStorage.setItem('token', tempToken);
        
        // Redirect to home
        router.push('/');
      } else {
        setError(data.message || 'Gagal memperbarui nomor telepon');
      }
    } catch (error) {
      console.error('Update phone error:', error);
      setError(error.message || 'Terjadi kesalahan');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div className="h-14 flex items-center px-4">
        <Link href="/login">
          <img src="/icons/arrow-left.svg" alt="Back" className="" />
        </Link>
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="mt-28">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Lengkapi Profil<span className="text-hb-pink">.</span>
        </h3>
        <p className="text-center text-gray-600 px-4">
          Untuk melanjutkan, silakan masukkan nomor WhatsApp Anda
        </p>
      </div>

      <form className="" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 px-4 mt-20">
          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white">
            <img src="/icons/call.svg" alt="Phone" className="mr-2" />
            <input 
              disabled={loading} 
              required 
              type="tel" 
              placeholder="Nomor WhatsApp" 
              value={phone} 
              onChange={(e) => setPhone(e.target.value)} 
              className="w-full focus:outline-none"  
            />
          </div>
        </div>
        
        <div className="px-4">
          {error && <ErrorSpan msg={error} />}
        </div>

        <div className="px-6">
          <button type="submit" disabled={loading} className="btn-primary flex items-center justify-center">
            {loading && <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin mr-2" />}
            Simpan & Lanjutkan
          </button>
        </div>
      </form>

      <div className="h-40"></div>
    </div>
  );
}