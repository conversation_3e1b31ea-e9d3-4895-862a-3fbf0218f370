<template>
  <div class="mt-4">
    <div class="border-t">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Total Bayar
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              <PERSON><PERSON>
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Buk<PERSON>
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Wak<PERSON>ar
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            />
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="(l, i) in list" :key="i">
            <td class="px-6 py-4 whitespace-nowrap">
              {{ useRupiah(l.totalBayar) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ l.isDp ? "DP" : "Pelunasan" }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <status-pay :status="l.status" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <a
                v-if="l.buktiBayar"
                :href="l.buktiBayar"
                target="_blank"
                class="btn-secondary text-xs"
                >Lihat</a
              >
              <span v-else class="text-gray-500">Belum ada</span>
            </td>
            <td class="text-xs px-6 text-gray-700">
              <div v-if="l.paidAt">
                {{ useMoment(l.paidAt).format("DD MMM, HH:mm") }}
                <br />
                {{ useMoment(l.paidAt).fromNow() }}
              </div>
            </td>
            <td class="px-6">
              <button
                v-if="l.status.toUpperCase() !== 'PAID'"
                class="btn-success-secondary text-xs flex items-center gap-1"
                @click.prevent="
                  (showKonfirmasi = true), (selectedKonfirmasi = l)
                "
              >
                <icon class="text-lg" name="ph:check-circle" />
                Konfirmasi
              </button>
            </td>
          </tr>
        </tbody>
      </table>
      <div
        v-if="sisaBayar > 0"
        class="p-3 mt-2 font-semibold text-gray-500 bg-gray-100 border rounded-xl"
      >
        Sisa Pembayaran
        <span class="strong">
          {{ useRupiah(sisaBayar) }}
        </span>
      </div>
    </div>
    <konfirmasi-pembayaran
      :show="showKonfirmasi"
      :data="selectedKonfirmasi"
      @closed="showKonfirmasi = false"
      @refresh="refreshAction"
    />
  </div>
</template>

<script setup>
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const list = ref([]);
const loading = ref(false);
const errorMsg = ref("");
const showKonfirmasi = ref(false);
const selectedKonfirmasi = ref({});

const sisaBayar = computed(() => {
  return list.value.reduce((acc, curr) => {
    if (curr.status === "UNPAID") {
      return acc + curr.totalBayar;
    }
    return acc;
  }, 0);
});

const refreshAction = () => {
  getDetail();
};

const getDetail = async () => {
  try {
    loading.value = true;
    list.value = [];
    const { data } = await adminGet(`/pay/${props.id}`);
    loading.value = false;
    list.value = data.data;
  } catch (error) {
    loading.value = false;
    errorMsg.value = error.response.data.message;
  }
};

onMounted(() => {
  getDetail();
});
</script>
