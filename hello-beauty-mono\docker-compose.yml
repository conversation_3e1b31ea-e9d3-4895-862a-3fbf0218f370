version: '3.8'
services:
  be:
    build: ./be
    env_file:
      - ./be/.env
    ports:
      - "8900:8900"
    restart: unless-stopped
    networks:
      - hello-beauty

  fe:
    build: ./fe
    env_file:
      - ./fe/.env
    ports:
      - "8902:8902"
    restart: unless-stopped
    networks:
      - hello-beauty

  admin:
    build: ./admin
    env_file:
      - ./admin/.env
    ports:
      - "8901:8901"
    restart: unless-stopped
    networks:
      - hello-beauty

networks:
  hello-beauty:
    driver: bridge 
