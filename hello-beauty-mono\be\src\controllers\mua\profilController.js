const mua = require('../../models/mua')
const {decodeJwtClient} = require('../../helper/jwt')
const moment = require('moment')
const fs = require('fs')
const path = require('path')
const multer = require('multer')

const getProfile = async (req, res) => {
  try {
    const auth =  req.headers.authorization
    const u = await decodeJwtClient(auth)
    const searchUser = await mua.findById(u.id)
    if(!searchUser){
      return res.status(404).json({
        message:"User not found"
      })
    }
    const returnResponse = {
      name: searchUser.name,
      email: searchUser.email,
      muaName: searchUser.muaName,
      phone: searchUser.phone,
      id: searchUser._id,
      locationId: searchUser.locationId,
      isApproved: searchUser.isApproved,
      photo: searchUser.photo
    }
    return res.status(200).json({
      message:"Success",
      data: returnResponse
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const editProfile = async (req, res) => {
  try {
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const { name, email, phone, muaName } = req.body;
    const updatedUser = await mua.findByIdAndUpdate(
      u.id,
      { name, email, phone, muaName },
      { new: true }
    );
    if (!updatedUser) {
      return res.status(404).json({
        message: "User not found"
      });
    }
    const returnResponse = {
      name: updatedUser.name,
      email: updatedUser.email,
      phone: updatedUser.phone,
      id: updatedUser._id,
      locationId: updatedUser.locationId
    };
    return res.status(200).json({
      message: "Profile updated successfully",
      data: returnResponse
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};


const updatePhoto = async (req, res) => {
  try {
    const auth = req.headers.authorization
    const u = await decodeJwtClient(auth)
    const searchMua = await mua.findById(u.id)
    if(!searchMua){
      return res.status(404).json({
        message:"User not found"
      })
    }

    const currentMonthYear = moment().format('MM-YYYY');
    const uploadDir = `./src/public/uploads/${currentMonthYear}/mua/${u.id}`;
    if (!fs.existsSync(uploadDir)){
      fs.mkdirSync(uploadDir, { recursive: true,mode: 0o777 });
    }

    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, uploadDir)
      },
      filename: function (req, file, cb) {
        cb(null, file.fieldname + '-' + Date.now() + path.extname(file.originalname))
      }
    })

    const upload = multer({
      storage: storage,
      fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png/;
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = filetypes.test(file.mimetype);
        if (mimetype && extname) {
          return cb(null, true);
        } else {
          cb('Error: Images only!');
        }
      }
    }).single('file'); // Change payload name to 'file'

    upload(req, res, async (err) => {
      if (err) {
        if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          return res.status(400).json({
            message: 'Unexpected field'
          });
        }
        res.status(500).json({
          message: 'Internal server error',
          error: err.message
        });
      } else {
        if (!req.file) {
          return res.status(400).json({
            message: 'No file uploaded'
          });
        }
        const { filename, path } = req.file;
        const fullUrl = `https://assets.hellobeauty.id/uploads/${currentMonthYear}/mua/${u.id}/${filename}`;
        const currentPath = path.replace('src/public', '');
        searchMua.photo = fullUrl;
        await searchMua.save();
        res.status(201).json({
          message: 'Photo updated successfully',
          data: {
            fileName: filename,
            filePath: currentPath,
            fullUrl
          }
        });
      }
    });
  } catch (error) {
    console.error(error.message); // Log the error message
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

module.exports = {
  getProfile,
  editProfile,
  updatePhoto
}
