const multer = require('multer');
const path = require('path');
const portofolioMua = require('../../models/portofolio-mua');
const moment = require('moment');
const fs = require('fs');
const { decodeJwtClient } = require('../../helper/jwt');

const listPortofolio = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const portofolios = await portofolioMua.find({
      muaId: u.id
    }).skip(skip).limit(limit).sort({ createdAt: -1 });
    return res.status(200).json({
      message: "List portofolio",
      data: portofolios
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const checkFileType = (file, cb) => {
  const filetypes = /jpeg|jpg|png|gif/;
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = filetypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb('Error: Images only!');
  }
}

const uploadPortofolio = async(req,res) => {
  try{
    const currentMonthYear = moment().format('MM-YYYY');
    const auth = req.headers.authorization;
    const u = await decodeJwtClient(auth);
    const uploadDir = `./src/public/uploads/${currentMonthYear}/`;

    if (!fs.existsSync(uploadDir)){
      fs.mkdirSync(uploadDir, { recursive: true,mode: 0o777 });
    }

    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, uploadDir)
      },
      filename: function (req, file, cb) {
        cb(null, file.fieldname + '-' + Date.now() + path.extname(file.originalname))
      }
    })
  
  const upload = multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
      checkFileType(file, cb);
    }
  }).single('file');
  
  await upload(req, res, (err) => {
    if (err) {
      res.status(500).json({
        message: 'Internal server error',
        error: err.message
      });
    } else {
      const { filename, path } = req.file;
      const fullUrl = `https://assets.hellobeauty.id/uploads/${currentMonthYear}/${filename}`;
      // src/public/uploads/10-2024/file-1729743781008.png
      // ubah jadi /uploads/10-2024/file-1729743781008.png
      const currentPath = path.replace('src/public', '');
      portofolioMua.create({
        fileName: filename,
        filePath: currentPath,
        fullUrl,
        muaId: u.id,
      })
      .then((data) => {
        res.status(201).json({
          message: 'Portofolio uploaded successfully',
          data
        });
      })
      .catch((err) => {
        res.status(500).json({
          message: 'Internal server error',
          error: err.message
        });
      });
    }
  });
  } catch(err) {
    console.log(err);
    
    res.status(500).json({
      message: 'Internal server error',
      error: err.message
    });
  }
  
}

const deletePortofolio = async (req, res) => {
  try {
    const { id } = req.params;
    const portofolioData = await portofolioMua.findById(id);
    if (!portofolioData) {
      return res.status(404).json({
        message: 'Portofolio tidak ditemukan'
      });
    }

    // Delete the file from the filesystem
    const filePath = path.join(__dirname, '../../public', portofolioData.filePath);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    await portofolioMua.findByIdAndDelete(id);

    return res.status(200).json({
      message: 'Portofolio dihapus'
    });

  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: 'Internal server error'
    });
  }
}

module.exports = {
  uploadPortofolio,
  listPortofolio,
  deletePortofolio
};
