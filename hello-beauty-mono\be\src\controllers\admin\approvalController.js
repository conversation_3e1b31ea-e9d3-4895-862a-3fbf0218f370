const approval = require("../../models/approval");
const mua = require("../../models/mua");
const package = require("../../models/package");
const { logFunction, logError, logSuccess } = require('../../utils/logger');

const listApproval = async (req, res) => {
  const fnName = 'listApproval';
  logFunction(fnName, { 
    page: req.query.page || 1,
    limit: req.query.limit || 10,
    status: req.query.status
  });

  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    let approvals;
    if (status) {
      approvals = await approval.find({status: status}).skip(skip).limit(limit).sort({createdAt: -1});
    } else {
      approvals = await approval.find().skip(skip).limit(limit).sort({status: 1, createdAt: -1});
    }

    logSuccess(fnName, { 
      totalApprovals: approvals.length,
      page,
      limit,
      status: status || 'all'
    });

    return res.status(200).json({
      message: "List approval",
      data: approvals
    })
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const actionApprove = async (req, res) => {
  const fnName = 'actionApprove';
  logFunction(fnName, { 
    approvalId: req.params.id,
    status: req.body.status,
    packageId: req.body.packageId
  });

  try {
    const id = req.params.id;
    const status = req.body.status;
    const note = req.body.note;
    const packageId = req.body.packageId;
    
    // check if approval exists
    const checkApproval = await approval.findById(id);
    if (!checkApproval) {
      logFunction(fnName, { error: 'Approval not found', approvalId: id }, 'warn');
      return res.status(404).json({
        message: "Approval not found"
      });
    }
    
    await approval.findByIdAndUpdate(id, { status: status, note: note });

    if (status === "approved") {
      logFunction(fnName, { 
        approvalId: id,
        muaId: checkApproval.muaId,
        action: 'updating MUA profile'
      });

      // update profile mua
      const checkPackage = await package.findById(packageId);
      await mua.findByIdAndUpdate(checkApproval.muaId, { 
        isApproved: true, 
        approval: checkApproval, 
        packageId, 
        packageName: checkPackage.name, 
        locationId: checkApproval.locationId 
      });

      logSuccess(fnName, { 
        approvalId: id,
        muaId: checkApproval.muaId,
        packageId,
        status: 'approved'
      });
    } else {
      logSuccess(fnName, { 
        approvalId: id,
        status,
        note
      });
    }

    return res.status(200).json({
      message: "Approval updated"
    });
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({
      message: "Internal server error"
    });
  }
};

module.exports = {
  listApproval,
  actionApprove
}
