const setting = require("../../models/setting");
const { logFunction, logError, logSuccess } = require("../../utils/logger");

const getDpSetting = async(req,res) => {
  const fnName = 'getDpSetting';
  logFunction(fnName);

  try {
    const checkSetting = await setting.findOne()
    
    if (checkSetting && checkSetting.settingDP) {
      logSuccess(fnName, { 
        settingDP: checkSetting.settingDP
      });
      
      return res.json({
        data: checkSetting.settingDP
      });
    } else {
      logFunction(fnName, { error: 'Setting DP not found' }, 'warn');
      return res.status(404).json({ message: "settingDp not found" });
    }
  } catch (error) {
    logError(fnName, error);
    return res.status(500).json({ message: error.message });
  }
}

module.exports = {
  getDpSetting
}
