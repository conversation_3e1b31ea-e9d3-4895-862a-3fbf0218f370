const user = require("../../models/user");
const mongoose = require('mongoose');

const listUser = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const search = req.query.search || '';
    const filter = {
      $or: [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ]
    };

    const sortBy = req.query.sort_by || '_id';
    const sortOrder = req.query.sort_order === 'desc' ? 1 : -1;

    let users = await user.find(filter).skip(skip).limit(limit).sort({ [sortBy]: sortOrder });
    users = users.map((item) => {
      return {
        _id: item._id,
        name: item.name,
        email: item.email,
        phone: item.phone,
        createdAt: item.createdAt,
        point: item.point,
        saldoReferral: item.saldoReferral
      }
    })
    return res.status(200).json({
      message: "List user",
      data: users
    })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      message: "Internal server error"
    })
  }
}

const detailUser = async (req, res) => {
  try {
    const id = req.params.id;
    let checkUser;

    if (mongoose.Types.ObjectId.isValid(id)) {
      checkUser = await user.findOne({ _id: id });
    }

    if (!checkUser) {
      checkUser = await user.findOne({ referralCode: id });
    }

    if (!checkUser) {
      return res.status(400).json({ message: "User tidak ditemukan" });
    }

    return res.status(200).json({ message: "Detail user", data: {
      _id: checkUser._id,
      name: checkUser.name,
      email: checkUser.email,
      phone: checkUser.phone,
      createdAt: checkUser.createdAt,
      point: checkUser.point,
      saldoReferral: checkUser.saldoReferral,
      referralCode: checkUser.referralCode
    } });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
}

const deleteUser = async (req, res) => {
  try {
    const id = req.params.id;
    const checkUser = await user.findOne({
      _id: id,
    });

    if (!checkUser) {
      return res.status(400).json({ message: "User tidak ditemukan" });
    }

    await user.deleteOne({ _id: id });
    return res.status(200).json({ message: "User berhasil dihapus" });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
}

module.exports = {
  listUser,
  detailUser,
  deleteUser
}
