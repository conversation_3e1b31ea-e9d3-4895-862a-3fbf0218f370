
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'FormApproval': typeof import("../components/approval/form-approval.vue")['default']
    'HapusCustomer': typeof import("../components/customer/hapus-customer.vue")['default']
    'MutasiReferral': typeof import("../components/customer/mutasi-referral.vue")['default']
    'TarikSaldoReferral': typeof import("../components/customer/tarik-saldo-referral.vue")['default']
    'BookingToday': typeof import("../components/dashboard/booking-today.vue")['default']
    'LatestPayment': typeof import("../components/dashboard/latest-payment.vue")['default']
    'StatisticChart': typeof import("../components/dashboard/statistic-chart.vue")['default']
    'StatisticRevenue': typeof import("../components/dashboard/statistic-revenue.vue")['default']
    'FileManager': typeof import("../components/file-manager.vue")['default']
    'FormVoucher': typeof import("../components/form-voucher.vue")['default']
    'HapusVoucher': typeof import("../components/hapus-voucher.vue")['default']
    'LoaderFull': typeof import("../components/loader-full.vue")['default']
    'Loader': typeof import("../components/loader.vue")['default']
    'FormLokasi': typeof import("../components/lokasi/form-lokasi.vue")['default']
    'HapusLokasi': typeof import("../components/lokasi/hapus-lokasi.vue")['default']
    'FormMetode': typeof import("../components/metode-pembayaran/form-metode.vue")['default']
    'HapusMetode': typeof import("../components/metode-pembayaran/hapus-metode.vue")['default']
    'ChangePassword': typeof import("../components/mua/change-password.vue")['default']
    'EditProfile': typeof import("../components/mua/edit-profile.vue")['default']
    'EditRating': typeof import("../components/mua/edit-rating.vue")['default']
    'HapusMua': typeof import("../components/mua/hapus-mua.vue")['default']
    'AddSaldoReferral': typeof import("../components/order/add-saldo-referral.vue")['default']
    'CancelMua': typeof import("../components/order/cancel-mua.vue")['default']
    'EditOrder': typeof import("../components/order/edit-order.vue")['default']
    'JobBid': typeof import("../components/order/job-bid.vue")['default']
    'KonfirmasiPembayaran': typeof import("../components/order/konfirmasi-pembayaran.vue")['default']
    'OpenHistory': typeof import("../components/order/open-history.vue")['default']
    'PaymentDetail': typeof import("../components/order/payment-detail.vue")['default']
    'ShareJob': typeof import("../components/order/share-job.vue")['default']
    'FormItem': typeof import("../components/paket/form-item.vue")['default']
    'FormPaket': typeof import("../components/paket/form-paket.vue")['default']
    'HapusPaket': typeof import("../components/paket/hapus-paket.vue")['default']
    'AssignPaket': typeof import("../components/portofolio/assign-paket.vue")['default']
    'RupiahInput': typeof import("../components/rupiah-input.vue")['default']
    'SidebarAdmin': typeof import("../components/sidebar-admin.vue")['default']
    'StatusAkun': typeof import("../components/status-akun.vue")['default']
    'StatusApproval': typeof import("../components/status-approval.vue")['default']
    'StatusLokasi': typeof import("../components/status-lokasi.vue")['default']
    'StatusOrder': typeof import("../components/status-order.vue")['default']
    'StatusPay': typeof import("../components/status-pay.vue")['default']
    'StatusPayment': typeof import("../components/status-payment.vue")['default']
    'StatusPb': typeof import("../components/status-pb.vue")['default']
    'FormTeam': typeof import("../components/team/form-team.vue")['default']
    'HapusTeam': typeof import("../components/team/hapus-team.vue")['default']
    'TextEditor': typeof import("../components/text-editor.vue")['default']
    'NuxtWelcome': typeof import("../../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
    'ClientOnly': typeof import("../../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtTime': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
    'NuxtRouteAnnouncer': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
    'NuxtPicture': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
    'Icon': typeof import("../../node_modules/nuxt-icon/dist/runtime/Icon.vue")['default']
    'IconCSS': typeof import("../../node_modules/nuxt-icon/dist/runtime/IconCSS.vue")['default']
    'NuxtPage': typeof import("../../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']
      'LazyFormApproval': LazyComponent<typeof import("../components/approval/form-approval.vue")['default']>
    'LazyHapusCustomer': LazyComponent<typeof import("../components/customer/hapus-customer.vue")['default']>
    'LazyMutasiReferral': LazyComponent<typeof import("../components/customer/mutasi-referral.vue")['default']>
    'LazyTarikSaldoReferral': LazyComponent<typeof import("../components/customer/tarik-saldo-referral.vue")['default']>
    'LazyBookingToday': LazyComponent<typeof import("../components/dashboard/booking-today.vue")['default']>
    'LazyLatestPayment': LazyComponent<typeof import("../components/dashboard/latest-payment.vue")['default']>
    'LazyStatisticChart': LazyComponent<typeof import("../components/dashboard/statistic-chart.vue")['default']>
    'LazyStatisticRevenue': LazyComponent<typeof import("../components/dashboard/statistic-revenue.vue")['default']>
    'LazyFileManager': LazyComponent<typeof import("../components/file-manager.vue")['default']>
    'LazyFormVoucher': LazyComponent<typeof import("../components/form-voucher.vue")['default']>
    'LazyHapusVoucher': LazyComponent<typeof import("../components/hapus-voucher.vue")['default']>
    'LazyLoaderFull': LazyComponent<typeof import("../components/loader-full.vue")['default']>
    'LazyLoader': LazyComponent<typeof import("../components/loader.vue")['default']>
    'LazyFormLokasi': LazyComponent<typeof import("../components/lokasi/form-lokasi.vue")['default']>
    'LazyHapusLokasi': LazyComponent<typeof import("../components/lokasi/hapus-lokasi.vue")['default']>
    'LazyFormMetode': LazyComponent<typeof import("../components/metode-pembayaran/form-metode.vue")['default']>
    'LazyHapusMetode': LazyComponent<typeof import("../components/metode-pembayaran/hapus-metode.vue")['default']>
    'LazyChangePassword': LazyComponent<typeof import("../components/mua/change-password.vue")['default']>
    'LazyEditProfile': LazyComponent<typeof import("../components/mua/edit-profile.vue")['default']>
    'LazyEditRating': LazyComponent<typeof import("../components/mua/edit-rating.vue")['default']>
    'LazyHapusMua': LazyComponent<typeof import("../components/mua/hapus-mua.vue")['default']>
    'LazyAddSaldoReferral': LazyComponent<typeof import("../components/order/add-saldo-referral.vue")['default']>
    'LazyCancelMua': LazyComponent<typeof import("../components/order/cancel-mua.vue")['default']>
    'LazyEditOrder': LazyComponent<typeof import("../components/order/edit-order.vue")['default']>
    'LazyJobBid': LazyComponent<typeof import("../components/order/job-bid.vue")['default']>
    'LazyKonfirmasiPembayaran': LazyComponent<typeof import("../components/order/konfirmasi-pembayaran.vue")['default']>
    'LazyOpenHistory': LazyComponent<typeof import("../components/order/open-history.vue")['default']>
    'LazyPaymentDetail': LazyComponent<typeof import("../components/order/payment-detail.vue")['default']>
    'LazyShareJob': LazyComponent<typeof import("../components/order/share-job.vue")['default']>
    'LazyFormItem': LazyComponent<typeof import("../components/paket/form-item.vue")['default']>
    'LazyFormPaket': LazyComponent<typeof import("../components/paket/form-paket.vue")['default']>
    'LazyHapusPaket': LazyComponent<typeof import("../components/paket/hapus-paket.vue")['default']>
    'LazyAssignPaket': LazyComponent<typeof import("../components/portofolio/assign-paket.vue")['default']>
    'LazyRupiahInput': LazyComponent<typeof import("../components/rupiah-input.vue")['default']>
    'LazySidebarAdmin': LazyComponent<typeof import("../components/sidebar-admin.vue")['default']>
    'LazyStatusAkun': LazyComponent<typeof import("../components/status-akun.vue")['default']>
    'LazyStatusApproval': LazyComponent<typeof import("../components/status-approval.vue")['default']>
    'LazyStatusLokasi': LazyComponent<typeof import("../components/status-lokasi.vue")['default']>
    'LazyStatusOrder': LazyComponent<typeof import("../components/status-order.vue")['default']>
    'LazyStatusPay': LazyComponent<typeof import("../components/status-pay.vue")['default']>
    'LazyStatusPayment': LazyComponent<typeof import("../components/status-payment.vue")['default']>
    'LazyStatusPb': LazyComponent<typeof import("../components/status-pb.vue")['default']>
    'LazyFormTeam': LazyComponent<typeof import("../components/team/form-team.vue")['default']>
    'LazyHapusTeam': LazyComponent<typeof import("../components/team/hapus-team.vue")['default']>
    'LazyTextEditor': LazyComponent<typeof import("../components/text-editor.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtTime': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
    'LazyNuxtPicture': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
    'LazyIcon': LazyComponent<typeof import("../../node_modules/nuxt-icon/dist/runtime/Icon.vue")['default']>
    'LazyIconCSS': LazyComponent<typeof import("../../node_modules/nuxt-icon/dist/runtime/IconCSS.vue")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../../node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const FormApproval: typeof import("../components/approval/form-approval.vue")['default']
export const HapusCustomer: typeof import("../components/customer/hapus-customer.vue")['default']
export const MutasiReferral: typeof import("../components/customer/mutasi-referral.vue")['default']
export const TarikSaldoReferral: typeof import("../components/customer/tarik-saldo-referral.vue")['default']
export const BookingToday: typeof import("../components/dashboard/booking-today.vue")['default']
export const LatestPayment: typeof import("../components/dashboard/latest-payment.vue")['default']
export const StatisticChart: typeof import("../components/dashboard/statistic-chart.vue")['default']
export const StatisticRevenue: typeof import("../components/dashboard/statistic-revenue.vue")['default']
export const FileManager: typeof import("../components/file-manager.vue")['default']
export const FormVoucher: typeof import("../components/form-voucher.vue")['default']
export const HapusVoucher: typeof import("../components/hapus-voucher.vue")['default']
export const LoaderFull: typeof import("../components/loader-full.vue")['default']
export const Loader: typeof import("../components/loader.vue")['default']
export const FormLokasi: typeof import("../components/lokasi/form-lokasi.vue")['default']
export const HapusLokasi: typeof import("../components/lokasi/hapus-lokasi.vue")['default']
export const FormMetode: typeof import("../components/metode-pembayaran/form-metode.vue")['default']
export const HapusMetode: typeof import("../components/metode-pembayaran/hapus-metode.vue")['default']
export const ChangePassword: typeof import("../components/mua/change-password.vue")['default']
export const EditProfile: typeof import("../components/mua/edit-profile.vue")['default']
export const EditRating: typeof import("../components/mua/edit-rating.vue")['default']
export const HapusMua: typeof import("../components/mua/hapus-mua.vue")['default']
export const AddSaldoReferral: typeof import("../components/order/add-saldo-referral.vue")['default']
export const CancelMua: typeof import("../components/order/cancel-mua.vue")['default']
export const EditOrder: typeof import("../components/order/edit-order.vue")['default']
export const JobBid: typeof import("../components/order/job-bid.vue")['default']
export const KonfirmasiPembayaran: typeof import("../components/order/konfirmasi-pembayaran.vue")['default']
export const OpenHistory: typeof import("../components/order/open-history.vue")['default']
export const PaymentDetail: typeof import("../components/order/payment-detail.vue")['default']
export const ShareJob: typeof import("../components/order/share-job.vue")['default']
export const FormItem: typeof import("../components/paket/form-item.vue")['default']
export const FormPaket: typeof import("../components/paket/form-paket.vue")['default']
export const HapusPaket: typeof import("../components/paket/hapus-paket.vue")['default']
export const AssignPaket: typeof import("../components/portofolio/assign-paket.vue")['default']
export const RupiahInput: typeof import("../components/rupiah-input.vue")['default']
export const SidebarAdmin: typeof import("../components/sidebar-admin.vue")['default']
export const StatusAkun: typeof import("../components/status-akun.vue")['default']
export const StatusApproval: typeof import("../components/status-approval.vue")['default']
export const StatusLokasi: typeof import("../components/status-lokasi.vue")['default']
export const StatusOrder: typeof import("../components/status-order.vue")['default']
export const StatusPay: typeof import("../components/status-pay.vue")['default']
export const StatusPayment: typeof import("../components/status-payment.vue")['default']
export const StatusPb: typeof import("../components/status-pb.vue")['default']
export const FormTeam: typeof import("../components/team/form-team.vue")['default']
export const HapusTeam: typeof import("../components/team/hapus-team.vue")['default']
export const TextEditor: typeof import("../components/text-editor.vue")['default']
export const NuxtWelcome: typeof import("../../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
export const ClientOnly: typeof import("../../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtTime: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
export const NuxtRouteAnnouncer: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
export const NuxtPicture: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
export const Icon: typeof import("../../node_modules/nuxt-icon/dist/runtime/Icon.vue")['default']
export const IconCSS: typeof import("../../node_modules/nuxt-icon/dist/runtime/IconCSS.vue")['default']
export const NuxtPage: typeof import("../../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const LazyFormApproval: LazyComponent<typeof import("../components/approval/form-approval.vue")['default']>
export const LazyHapusCustomer: LazyComponent<typeof import("../components/customer/hapus-customer.vue")['default']>
export const LazyMutasiReferral: LazyComponent<typeof import("../components/customer/mutasi-referral.vue")['default']>
export const LazyTarikSaldoReferral: LazyComponent<typeof import("../components/customer/tarik-saldo-referral.vue")['default']>
export const LazyBookingToday: LazyComponent<typeof import("../components/dashboard/booking-today.vue")['default']>
export const LazyLatestPayment: LazyComponent<typeof import("../components/dashboard/latest-payment.vue")['default']>
export const LazyStatisticChart: LazyComponent<typeof import("../components/dashboard/statistic-chart.vue")['default']>
export const LazyStatisticRevenue: LazyComponent<typeof import("../components/dashboard/statistic-revenue.vue")['default']>
export const LazyFileManager: LazyComponent<typeof import("../components/file-manager.vue")['default']>
export const LazyFormVoucher: LazyComponent<typeof import("../components/form-voucher.vue")['default']>
export const LazyHapusVoucher: LazyComponent<typeof import("../components/hapus-voucher.vue")['default']>
export const LazyLoaderFull: LazyComponent<typeof import("../components/loader-full.vue")['default']>
export const LazyLoader: LazyComponent<typeof import("../components/loader.vue")['default']>
export const LazyFormLokasi: LazyComponent<typeof import("../components/lokasi/form-lokasi.vue")['default']>
export const LazyHapusLokasi: LazyComponent<typeof import("../components/lokasi/hapus-lokasi.vue")['default']>
export const LazyFormMetode: LazyComponent<typeof import("../components/metode-pembayaran/form-metode.vue")['default']>
export const LazyHapusMetode: LazyComponent<typeof import("../components/metode-pembayaran/hapus-metode.vue")['default']>
export const LazyChangePassword: LazyComponent<typeof import("../components/mua/change-password.vue")['default']>
export const LazyEditProfile: LazyComponent<typeof import("../components/mua/edit-profile.vue")['default']>
export const LazyEditRating: LazyComponent<typeof import("../components/mua/edit-rating.vue")['default']>
export const LazyHapusMua: LazyComponent<typeof import("../components/mua/hapus-mua.vue")['default']>
export const LazyAddSaldoReferral: LazyComponent<typeof import("../components/order/add-saldo-referral.vue")['default']>
export const LazyCancelMua: LazyComponent<typeof import("../components/order/cancel-mua.vue")['default']>
export const LazyEditOrder: LazyComponent<typeof import("../components/order/edit-order.vue")['default']>
export const LazyJobBid: LazyComponent<typeof import("../components/order/job-bid.vue")['default']>
export const LazyKonfirmasiPembayaran: LazyComponent<typeof import("../components/order/konfirmasi-pembayaran.vue")['default']>
export const LazyOpenHistory: LazyComponent<typeof import("../components/order/open-history.vue")['default']>
export const LazyPaymentDetail: LazyComponent<typeof import("../components/order/payment-detail.vue")['default']>
export const LazyShareJob: LazyComponent<typeof import("../components/order/share-job.vue")['default']>
export const LazyFormItem: LazyComponent<typeof import("../components/paket/form-item.vue")['default']>
export const LazyFormPaket: LazyComponent<typeof import("../components/paket/form-paket.vue")['default']>
export const LazyHapusPaket: LazyComponent<typeof import("../components/paket/hapus-paket.vue")['default']>
export const LazyAssignPaket: LazyComponent<typeof import("../components/portofolio/assign-paket.vue")['default']>
export const LazyRupiahInput: LazyComponent<typeof import("../components/rupiah-input.vue")['default']>
export const LazySidebarAdmin: LazyComponent<typeof import("../components/sidebar-admin.vue")['default']>
export const LazyStatusAkun: LazyComponent<typeof import("../components/status-akun.vue")['default']>
export const LazyStatusApproval: LazyComponent<typeof import("../components/status-approval.vue")['default']>
export const LazyStatusLokasi: LazyComponent<typeof import("../components/status-lokasi.vue")['default']>
export const LazyStatusOrder: LazyComponent<typeof import("../components/status-order.vue")['default']>
export const LazyStatusPay: LazyComponent<typeof import("../components/status-pay.vue")['default']>
export const LazyStatusPayment: LazyComponent<typeof import("../components/status-payment.vue")['default']>
export const LazyStatusPb: LazyComponent<typeof import("../components/status-pb.vue")['default']>
export const LazyFormTeam: LazyComponent<typeof import("../components/team/form-team.vue")['default']>
export const LazyHapusTeam: LazyComponent<typeof import("../components/team/hapus-team.vue")['default']>
export const LazyTextEditor: LazyComponent<typeof import("../components/text-editor.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtTime: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
export const LazyNuxtPicture: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
export const LazyIcon: LazyComponent<typeof import("../../node_modules/nuxt-icon/dist/runtime/Icon.vue")['default']>
export const LazyIconCSS: LazyComponent<typeof import("../../node_modules/nuxt-icon/dist/runtime/IconCSS.vue")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../../node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']>

export const componentNames: string[]
