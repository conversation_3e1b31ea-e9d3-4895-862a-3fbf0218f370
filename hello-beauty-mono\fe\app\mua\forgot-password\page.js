"use client"
import {Icon} from '@iconify/react';
import Link from 'next/link'
import {useState} from "react";
import ErrorSpan from '../../_component/errorSpan';
import {apiMuaPublic} from '../../_helper/api-mua';
import {setCookie} from '@/app/_helper/cookie';

export default function Home() {

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [success, setSuccess] = useState(""); // Add success state

   const handleSubmit = async (e) => {
    try {
      e.preventDefault()
      setError('')
      setSuccess('') // Reset success message
      setLoading(true)
      const res = await apiMuaPublic('POST', '/auth/forgot-password', {email})
      setSuccess('Password reset email sent successfully!') // Set success message
      setLoading(false)
    } catch (error) {
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <div className="max-w-[480px] min-h-screen mx-auto grid-bg">
      <div className="h-14 flex items-center px-4">
        <Link href="/login">
        <img src="/icons/arrow-left.svg" alt="Logo" className="" />
        </Link>
        <img
          src="/logo.png"
          alt="Logo"
          className="mx-auto h-8"
        />
      </div>

      <div className="mt-20">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Lupa Password MUA<span className="text-hb-pink">.</span></h3>
      </div>
      <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 px-4 mt-20">
        <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
          <img src="/icons/sms.svg" alt="Logo" className="mr-2" />
          <input disabled={loading} 
          required value={email} onChange={(e) => setEmail(e.target.value)}
          type="email" placeholder="Email" className="w-full  focus:outline-none"  />
        </div>

        
      </div>
       <div className="px-4">
        {
          error && <ErrorSpan msg={error} />
        }
        {
          success && <span className="text-green-500">{success}</span> // Display success message
        }
      </div>

      <div className="mt-4 px-6">
        <button type="submit" disabled={loading} className="btn-primary items-center flex justify-center">
         {
            loading && <Icon icon="svg-spinners:180-ring-with-bg" className="text-white animate-spin mr-2" />
          }
          Kirim
        </button>
        </div>
      </form>


      </div>
  );
}
