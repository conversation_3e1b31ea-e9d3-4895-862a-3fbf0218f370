<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <!-- :no-close="order.loading"
        :hide-close-btn="order.loading" -->

        <form class="p-4" @submit.prevent="save()">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">
              {{ payload._id ? "Edit" : "Tambah" }} Paket
            </h2>
          </div>
          <div class="grid grid-cols-2 gap-4 !text-sm">
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Nama Paket</label>
              <input
                v-model="payload.name"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>
            <div class="col-span-2">
              <label class="text-sm font-semibold block">Deskripsi</label>
              <textarea
                v-model="payload.description"
                required
                :disabled="loading"
                class="form-input text-sm"
                placeholder=""
              />
            </div>

            <div class="col-span-2">
              <label class="text-sm font-semibold block">Status</label>
              <select
                v-model="payload.isActive"
                required
                :disabled="loading"
                class="form-input text-sm"
              >
                <option value="" disabled selected>Pilih Status</option>
                <option :value="true">Aktif</option>
                <option :value="false">Non Aktif</option>
              </select>
            </div>
            <div class="col-span-2 grid grid-cols-2 gap-2">
              <div
                v-for="(it, i) in payload.items"
                :key="i"
                class="rounded-lg relative border p-4 border-gray-500 text-gray-700"
                draggable="true"
                @dragstart="dragStart(i)"
                @dragover.prevent
                @drop="drop(i)"
              >
                <span class="text-sm block font-semibold">
                  {{ it.name }}
                </span>
                <p class="text-xs py-1">
                  {{ it.description || "-" }}
                </p>
                <div class="mb-2">
                  <span class="text-sm font-semibold">
                    {{ useRupiah(it.price) }}
                  </span>
                  <span class="text-sm line-through ml-2">
                    {{ useRupiah(it.priceCoret || 0) }}
                  </span>
                </div>
                <span
                  class="text-xs bg-gray-100 text-gray-600 p-1 px-3 rounded-xl"
                >
                  Share MUA
                  <strong>{{ useRupiah(it.priceShare || 0) }}</strong>
                </span>
                <div class="h-12"></div>

                <div class="absolute bottom-2 right-2 gap-4 flex text-gray-600">
                  <button @click.prevent="deleteItem(i)">
                    <icon name="mingcute:delete-2-line" />
                    Delete
                  </button>
                  <button
                    @click.prevent="
                      (selectedEdit = { ...it, index: i }),
                        (showPanelItem = true)
                    "
                  >
                    <icon name="mingcute:edit-3-line" />
                    Edit
                  </button>
                </div>
              </div>
              <div
                class="rounded-lg p-3 flex border items-center justify-center min-h-32 text-gray-700 bg-gray-100 border-gray-700"
                @click.prevent="
                  (selectedEdit = { index: null }), (showPanelItem = true)
                "
              >
                + Tambah Item
              </div>
            </div>
          </div>

          <div class="h-4" />
          <div class="flex gap-2">
            <button
              class="btn-primary text-sm flex items-center gap-1"
              :disabled="loading"
              type="submit"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menyimpan" : "Simpan" }}
              </span>
            </button>
            <button
              class="btn-secondary text-sm flex items-center gap-1"
              :disabled="loading"
              type="button"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </form>
      </VueSidePanel>
    </client-only>

    <form-item
      :show="showPanelItem"
      :data="selectedEdit"
      @closed="showPanelItem = false"
      @add-item="addItem"
    />
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();

const emit = defineEmits(["closed", "refresh"]);

const props = defineProps({
  show: Boolean,
  data: Object,
});

const showPanel = ref(false);
const showPanelItem = ref(false);

const payload = ref({
  name: "",
  isActive: "",
  description: "",
  items: [],
});

const items = ref([]);

const loading = ref(false);
const selectedEdit = ref({});

let draggedItemIndex = null;

const dragStart = (index) => {
  draggedItemIndex = index;
};

const drop = (index) => {
  if (draggedItemIndex !== null) {
    const draggedItem = payload.value.items[draggedItemIndex];
    payload.value.items.splice(draggedItemIndex, 1);
    payload.value.items.splice(index, 0, draggedItem);
    draggedItemIndex = null;
  }
};

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;

    if (props.data) {
      payload.value = { ...props.data };
      items.value = payload.value.items || [];
    }
  },
  { deep: true }
);

const addItem = (item) => {
  console.log(item);

  if (item.index !== null) {
    items.value[item.index] = item;
  }
  if (item.index === null) {
    items.value.push(item);
  }
  payload.value.items = items.value;
};

const deleteItem = (i) => {
  items.value.splice(i, 1);
  payload.value.items = items.value;
};

const save = async () => {
  try {
    loading.value = true;
    const p = payload.value;

    let res;
    if (props.data._id) {
      res = await adminPut(`/package?id=${props.data._id}`, p);
    } else {
      res = await adminPost("/package", p);
    }
    loading.value = false;
    emit("refresh");
    $toast.success(res.data.message);
  } catch (error) {
    $toast.error(`Terjadi kesalahan ${error}`);
  }
};
</script>
