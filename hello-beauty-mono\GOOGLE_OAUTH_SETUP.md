# Google OAuth Setup untuk Hello Beauty

## Setup Google OAuth

### 1. Buat Google OAuth Credentials

1. <PERSON><PERSON> [Google Cloud Console](https://console.cloud.google.com/)
2. Pilih atau buat project baru
3. Aktifkan Google+ API
4. <PERSON><PERSON> "Credentials" di sidebar
5. <PERSON><PERSON> "Create Credentials" > "OAuth 2.0 Client IDs"
6. <PERSON>lih "Web application"
7. Tambahkan Authorized redirect URIs:
   - Development: `http://localhost:8900/auth/google/callback`
   - Development MUA: `http://localhost:8900/mua/auth/google/callback`
   - Production: `https://your-domain.com/auth/google/callback`
   - Production MUA: `https://your-domain.com/mua/auth/google/callback`

### 2. Konfigurasi Environment Variables

#### Backend (.env)
```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
SESSION_SECRET="your-session-secret-key-here"
JWT_SECRET_MUA="muasecretkey123456789012345678"
FRONTEND_URL="http://localhost:3000"
```

#### Frontend (.env.local)
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8900
```

### 3. Install Dependencies

Backend sudah terinstall:
- passport
- passport-google-oauth20
- express-session

### 4. Flow Google OAuth

#### Untuk User Regular:
1. User klik "Login dengan Google" di `/login`
2. Redirect ke `/auth/google`
3. Google OAuth flow
4. Callback ke `/auth/google/callback`
5. Jika berhasil:
   - Jika phone number ada: redirect ke `/login/success?token=xxx`
   - Jika phone number kosong: redirect ke `/profile/complete?token=xxx&missing=phone`

#### Untuk MUA:
1. MUA klik "Login dengan Google" di `/mua/login`
2. Redirect ke `/mua/auth/google`
3. Google OAuth flow
4. Callback ke `/mua/auth/google/callback`
5. Jika berhasil:
   - Jika phone number ada: redirect ke `/mua/login/success?token=xxx`
   - Jika phone number kosong: redirect ke `/mua/profile/complete?token=xxx&missing=phone`

### 5. API Endpoints

#### User Endpoints:
- `GET /auth/google` - Initiate Google OAuth
- `GET /auth/google/callback` - Google OAuth callback
- `PUT /auth/update-phone` - Update phone number (requires JWT)

#### MUA Endpoints:
- `GET /mua/auth/google` - Initiate Google OAuth for MUA
- `GET /mua/auth/google/callback` - Google OAuth callback for MUA
- `PUT /mua/auth/update-phone` - Update phone number for MUA (requires JWT)

### 6. Database Schema Updates

#### User Model:
- `googleId` (String) - Google OAuth ID
- `profilePicture` (String) - Google profile picture URL
- `phone` (String) - Required field, bisa kosong untuk Google users

#### MUA Model:
- `googleId` (String) - Google OAuth ID
- `profilePicture` (String) - Google profile picture URL
- `phone` (String) - Required field, bisa kosong untuk Google users

### 7. Frontend Pages

#### User Pages:
- `/login/success` - Handle successful login
- `/profile/complete` - Complete profile (phone number)

#### MUA Pages:
- `/mua/login/success` - Handle successful MUA login
- `/mua/profile/complete` - Complete MUA profile (phone number)

### 8. Security Notes

- Google users akan memiliki password placeholder: `'google_oauth'`
- JWT token tetap digunakan untuk session management
- Phone number wajib diisi setelah login Google
- Session secret harus kuat dan unik untuk production

### 9. Testing

1. Start backend: `npm run dev` di folder `be/`
2. Start frontend: `npm run dev` di folder `fe/`
3. Buka `http://localhost:3000/login`
4. Klik "Login dengan Google"
5. Test flow lengkap

### 10. Production Deployment

1. Update redirect URIs di Google Console dengan domain production
2. Update environment variables dengan nilai production
3. Pastikan HTTPS enabled untuk production
4. Update `FRONTEND_URL` dan `NEXT_PUBLIC_API_BASE_URL` dengan domain production