const logger = require('../config/logger');

const logFunction = (fnName, params = {}, level = 'info') => {
  const logData = {
    function: fnName,
    params,
    timestamp: new Date().toISOString()
  };

  switch (level) {
    case 'error':
      logger.error('Function Error:', logData);
      break;
    case 'warn':
      logger.warn('Function Warning:', logData);
      break;
    case 'debug':
      logger.debug('Function Debug:', logData);
      break;
    default:
      logger.info('Function Call:', logData);
  }
};

const logError = (fnName, error, params = {}) => {
  logger.error('Function Error:', {
    function: fnName,
    error: {
      message: error.message,
      stack: error.stack
    },
    params,
    timestamp: new Date().toISOString()
  });
};

const logSuccess = (fnName, result, params = {}) => {
  logger.info('Function Success:', {
    function: fnName,
    result,
    params,
    timestamp: new Date().toISOString()
  });
};

module.exports = {
  logFunction,
  logError,
  logSuccess
}; 
