import { defineStore } from 'pinia';

// main is the name of the store. It is unique across your application
// and will appear in devtools
export const useAuth = defineStore('auth', {
  // a function that returns a fresh state
  state: () => ({
    token: 'xx',
    isLogin: false,
    tokenV0y: 'xx',
    isLoginmin: false
  }),
  actions: {
    setToken(data) {
      this.token = data;
    },
    setLogin(data) {
      this.isLogin = data;
    },
    settokenV0y(data) {
      this.tokenV0y = data;
    },
    setLoginmin(data) {
      this.isLoginmin = data;
    }
  }
});
