"use client";
import Link from "next/link";
import { useState } from "react";
import ErrorSpan from "@/app/_component/errorSpan";
import { Icon } from "@iconify/react";
import { apiMua } from "@/app/_helper/api-mua";
import { setCookie } from "@/app/_helper/cookie";

export default function MuaLogin() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      setLoading(true);
      setError("");
      const payload = {
        email,
        password,
      };

      const res = await apiMua("POST", "/auth/login", payload);

      setCookie("mua_token", res.data.token);
      setCookie("mua", JSON.stringify(res.data.mua));
      window.location.reload();
    } catch (error) {
      setError(error.message);
      setLoading(false);
    }
  };

  return (
    <div className="min-w-[400px]">
      <div className="mt-12">
        <h3 className="text-2xl font-semibold text-center mb-6">
          Silahkan<br/>Login Dulu<span className="text-hb-pink">.</span>
        </h3>
      </div>

      <form className="" onSubmit={handleSubmit} autocomplete="off">
        <div className="grid grid-cols-1">
          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
            <Icon icon="hugeicons:mail-02" className="mr-2" />
            <input
              disabled={loading}
              required
              type="email"
              placeholder="Email "
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full  focus:outline-none"
            />
          </div>

          <div className="w-full p-3 border rounded-lg mb-4 flex items-center group bg-white ">
            <Icon icon="hugeicons:circle-password" className="mr-2" />
            <input
              disabled={loading}
              required
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full  focus:outline-none"
            />
          </div>

          <div>{error && <ErrorSpan msg={error} />}</div>
          <div >
            <button
              type="submit"
              className="btn-primary flex items-center justify-center"
            >
              {loading && (
                <Icon
                  icon="svg-spinners:180-ring-with-bg"
                  className="text-white animate-spin mr-2"
                />
              )}
              Login
            </button>
          </div>
          <div className="py-6 w-full text-center text-gray-400 text-xs">
            Belum Punya Akun ?{" "}
            <Link href="/mua/register" className="text-hb-pink underline">
              Register
            </Link>
          </div>
        </div>
      </form>
    </div>
  );
}
