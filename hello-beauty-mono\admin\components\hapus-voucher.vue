<template>
  <div>
    <client-only>
      <VueSidePanel
        v-model="showPanel"
        lock-scroll
        hide-close-btn
        side="right"
        width="640px"
        @closed="$emit('closed')"
      >
        <div class="p-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-bold">Hapus Voucher</h2>
          </div>

          <p class="text-gray-600 mb-4">
            Apakah Anda yakin ingin menghapus voucher
            <span class="font-semibold">{{ data.code }}</span
            >?
          </p>

          <div class="h-4" />
          <div class="flex gap-2">
            <button
              class="btn-danger text-sm flex items-center gap-1"
              :disabled="loading"
              @click="deleteVoucher"
            >
              <icon v-if="loading" name="svg-spinners:270-ring-with-bg" />
              <span>
                {{ loading ? "Menghapus" : "Hapus" }}
              </span>
            </button>
            <button
              class="btn-secondary text-sm flex items-center gap-1"
              :disabled="loading"
              @click.prevent="showPanel = false"
            >
              Batal
            </button>
          </div>
        </div>
      </VueSidePanel>
    </client-only>
  </div>
</template>

<script setup>
const { $toast } = useNuxtApp();
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["closed", "refresh"]);

const showPanel = ref(false);
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    showPanel.value = !!val;
  },
  { deep: true }
);

const deleteVoucher = async () => {
  try {
    loading.value = true;
    await adminDelete(`/voucher/${props.data._id}`);
    $toast.success("Voucher berhasil dihapus");
    emit("refresh");
  } catch (error) {
    $toast.error(
      `Terjadi kesalahan ${error.response?.data?.message || error.message}`
    );
  } finally {
    loading.value = false;
  }
};
</script> 
